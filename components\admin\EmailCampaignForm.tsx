'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Bold, Italic, List, ListOrdered, Link, Save, Send, ArrowLeft } from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { emailCampaignSchema, type EmailCampaignInput } from '@/lib/validations'

interface EmailCampaignFormProps {
  campaign?: any
  isEditing?: boolean
}

export default function EmailCampaignForm({ campaign, isEditing = false }: EmailCampaignFormProps) {
  const router = useRouter()
  const supabase = createClient()
  
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<EmailCampaignInput>({
    resolver: zodResolver(emailCampaignSchema),
    defaultValues: {
      subject: campaign?.subject || '',
      previewText: campaign?.preview_text || '',
      senderName: campaign?.sender_name || 'JCC Customs and Commercial',
      senderEmail: campaign?.sender_email || '<EMAIL>',
      status: campaign?.status || 'draft',
      content: '',
      contentHtml: '',
    }
  })

  const watchedData = watch()

  const editor = useEditor({
    extensions: [StarterKit],
    content: campaign?.content || '<p>Start writing your email content here...</p>',
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[400px] p-4',
      },
    },
  })

  const onSubmit = async (data: EmailCampaignInput, saveAs: 'draft' | 'send' = 'draft') => {
    setIsLoading(true)
    setError('')

    try {
      const content = editor?.getHTML() || ''
      
      // Update form data with editor content
      setValue('content', content)
      setValue('contentHtml', content)
      
      const campaignData = {
        title: data.subject, // Use subject as title for database
        subject: data.subject,
        content,
        content_html: content,
        sender_name: data.senderName,
        sender_email: data.senderEmail,
        status: saveAs === 'send' ? 'sending' : 'draft',
        ...(saveAs === 'send' && { sent_at: new Date().toISOString() })
      }

      if (isEditing && campaign?.id) {
        const { error } = await supabase
          .from('email_campaigns')
          .update(campaignData)
          .eq('id', campaign.id)

        if (error) throw error
      } else {
        const { error } = await supabase
          .from('email_campaigns')
          .insert([campaignData])

        if (error) throw error
      }

      if (saveAs === 'send') {
        // TODO: Implement actual email sending logic
        console.log('Email campaign would be sent here')
      }

      router.push('/admin/emails')
      router.refresh()
    } catch (err: any) {
      setError(err.message || 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="gilroy-text"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Email Campaigns
      </Button>

      <form onSubmit={handleFormSubmit((data) => onSubmit(data, 'draft'))} className="space-y-6">
        {/* Campaign Details */}
        <Card>
          <CardHeader>
            <CardTitle className="gilroy-text-bold">Campaign Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="subject" className="gilroy-text-medium">Email Subject *</Label>
                <Input
                  id="subject"
                  {...register('subject')}
                  placeholder="Enter email subject"
                  className={`gilroy-text ${errors.subject ? 'border-red-500' : ''}`}
                />
                {errors.subject && (
                  <p className="text-sm text-red-600 mt-1">{errors.subject.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="preview_text" className="gilroy-text-medium">Preview Text</Label>
                <Input
                  id="preview_text"
                  {...register('previewText')}
                  placeholder="Brief preview text"
                  className={`gilroy-text ${errors.previewText ? 'border-red-500' : ''}`}
                />
                {errors.previewText && (
                  <p className="text-sm text-red-600 mt-1">{errors.previewText.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sender_name" className="gilroy-text-medium">Sender Name *</Label>
                <Input
                  id="sender_name"
                  {...register('senderName')}
                  placeholder="Your name or company name"
                  className={`gilroy-text ${errors.senderName ? 'border-red-500' : ''}`}
                />
                {errors.senderName && (
                  <p className="text-sm text-red-600 mt-1">{errors.senderName.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="sender_email" className="gilroy-text-medium">Sender Email *</Label>
                <Input
                  id="sender_email"
                  type="email"
                  {...register('senderEmail')}
                  placeholder="<EMAIL>"
                  className={`gilroy-text ${errors.senderEmail ? 'border-red-500' : ''}`}
                />
                {errors.senderEmail && (
                  <p className="text-sm text-red-600 mt-1">{errors.senderEmail.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Content */}
        <Card>
          <CardHeader>
            <CardTitle className="gilroy-text-bold">Email Content</CardTitle>
            <div className="flex items-center space-x-2 pt-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleBold().run()}
                className={editor?.isActive('bold') ? 'bg-gray-200' : ''}
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleItalic().run()}
                className={editor?.isActive('italic') ? 'bg-gray-200' : ''}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleBulletList().run()}
                className={editor?.isActive('bulletList') ? 'bg-gray-200' : ''}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                className={editor?.isActive('orderedList') ? 'bg-gray-200' : ''}
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="border border-gray-200 rounded-lg">
              <EditorContent editor={editor} />
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 gilroy-text">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              type="submit"
              variant="outline"
              disabled={isLoading}
              className="gilroy-text-medium"
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Saving...' : 'Save Draft'}
            </Button>
            <Button
              type="button"
              onClick={handleFormSubmit((data) => onSubmit(data, 'send'))}
              disabled={isLoading || !watchedData.subject}
              className="gilroy-text-medium"
            >
              <Send className="h-4 w-4 mr-2" />
              {isLoading ? 'Sending...' : 'Send Campaign'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  )
} 