import { type NextRequest } from 'next/server'
import { updateSession } from './lib/middleware'
import { prerenderMiddleware } from './lib/prerender-middleware'

export async function middleware(request: NextRequest) {
  // Check if this request should be prerendered
  const prerenderResponse = await prerenderMiddleware(request)
  if (prerenderResponse) {
    return prerenderResponse
  }
  
  // Continue with existing session management
  return await updateSession(request)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api routes that don't need protection
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
} 