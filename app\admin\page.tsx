import { createServerSupabaseClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  MessageSquare, 
  FolderOpen, 
  Users, 
  FileText, 
  Images, 
  Mail, 
  Activity,
  ArrowRight,
  Plus,
  Eye,
  Send,
  Upload,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'

export default async function AdminDashboard() {
  const supabase = await createServerSupabaseClient()

  // Get notification counts
  const [
    { count: newContactsCount },
    { count: activeProjectsCount },
    { count: subscribersCount },
    { count: draftBlogsCount },
    { count: galleryCount },
    { count: pendingEmailsCount }
  ] = await Promise.all([
    supabase.from('contact_submissions').select('*', { count: 'exact', head: true }).eq('status', 'new'),
    supabase.from('projects').select('*', { count: 'exact', head: true }).eq('status', 'active'),
    supabase.from('subscribers').select('*', { count: 'exact', head: true }),
    supabase.from('blog_articles').select('*', { count: 'exact', head: true }).eq('status', 'draft'),
    supabase.from('gallery_images').select('*', { count: 'exact', head: true }),
    supabase.from('email_campaigns').select('*', { count: 'exact', head: true }).eq('status', 'draft')
  ])

  // Get recent activity for quick preview
  const { data: recentContacts } = await supabase
    .from('contact_submissions')
    .select('*')
    .eq('status', 'new')
    .order('created_at', { ascending: false })
    .limit(3)

  const { data: activeProjects } = await supabase
    .from('projects')
    .select('*')
    .eq('status', 'active')
    .order('updated_at', { ascending: false })
    .limit(3)

  const quickActions = [
    {
      title: 'Contact Forms',
      description: 'Review and respond to customer inquiries',
      icon: MessageSquare,
      color: 'bg-blue-500',
      lightColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      href: '/admin/contacts',
      count: newContactsCount || 0,
      actions: [
        { label: 'View All', href: '/admin/contacts', icon: Eye },
        { label: 'Quick Reply', href: '/admin/contacts?action=reply', icon: Send }
      ]
    },
    {
      title: 'Active Projects',
      description: 'Manage ongoing customer projects',
      icon: FolderOpen,
      color: 'bg-green-500',
      lightColor: 'bg-green-50',
      borderColor: 'border-green-200',
      href: '/admin/projects',
      count: activeProjectsCount || 0,
      actions: [
        { label: 'View Projects', href: '/admin/projects', icon: Eye },
        { label: 'New Project', href: '/admin/projects/new', icon: Plus }
      ]
    },
    {
      title: 'Email Campaigns',
      description: 'Send newsletters and marketing emails',
      icon: Mail,
      color: 'bg-purple-500',
      lightColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      href: '/admin/emails',
      count: pendingEmailsCount || 0,
      actions: [
        { label: 'View Campaigns', href: '/admin/emails', icon: Eye },
        { label: 'New Campaign', href: '/admin/emails/new', icon: Plus }
      ]
    },
    {
      title: 'Subscribers',
      description: 'Manage your email subscriber list',
      icon: Users,
      color: 'bg-orange-500',
      lightColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      href: '/admin/subscribers',
      count: subscribersCount || 0,
      actions: [
        { label: 'View List', href: '/admin/subscribers', icon: Eye },
        { label: 'Export', href: '/admin/subscribers?action=export', icon: ArrowRight }
      ]
    },
    {
      title: 'Blog Articles',
      description: 'Create and manage blog content',
      icon: FileText,
      color: 'bg-indigo-500',
      lightColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200',
      href: '/admin/blog',
      count: draftBlogsCount || 0,
      actions: [
        { label: 'View Articles', href: '/admin/blog', icon: Eye },
        { label: 'New Article', href: '/admin/blog/new', icon: Plus }
      ]
    },
    {
      title: 'Gallery',
      description: 'Upload and organize project images',
      icon: Images,
      color: 'bg-pink-500',
      lightColor: 'bg-pink-50',
      borderColor: 'border-pink-200',
      href: '/admin/gallery',
      count: galleryCount || 0,
      actions: [
        { label: 'View Gallery', href: '/admin/gallery', icon: Eye },
        { label: 'Upload Images', href: '/admin/gallery?action=upload', icon: Upload }
      ]
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <h1 className="text-3xl font-bold text-gray-900 gilroy-text-bold">Dashboard</h1>
        <p className="text-gray-600 mt-2 gilroy-text">
          Quick actions and notifications for your business operations
        </p>
        <div className="flex items-center space-x-2 mt-4">
          <Activity className="h-4 w-4 text-green-500" />
          <span className="text-sm text-green-600 gilroy-text-medium">System Online</span>
        </div>
      </div>

      {/* Quick Actions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quickActions.map((action) => (
          <Card key={action.title} className={`hover:shadow-lg transition-all duration-200 border-l-4 ${action.borderColor} relative`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${action.lightColor} ring-1 ring-gray-200`}>
                    <action.icon className={`h-5 w-5 text-white`} style={{ color: action.color.replace('bg-', '').replace('-500', '') }} />
                  </div>
                  <div>
                    <CardTitle className="text-lg gilroy-text-bold">{action.title}</CardTitle>
                    <CardDescription className="gilroy-text text-sm">{action.description}</CardDescription>
                  </div>
                </div>
                {/* Notification Badge */}
                {action.count > 0 && (
                  <div className="absolute -top-2 -right-2">
                    <div className="flex items-center justify-center w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full ring-2 ring-white">
                      {action.count > 99 ? '99+' : action.count}
                    </div>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {action.actions.map((actionItem) => (
                  <Link key={actionItem.label} href={actionItem.href}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-left hover:bg-gray-50 gilroy-text"
                    >
                      <actionItem.icon className="h-4 w-4 mr-2" />
                      {actionItem.label}
                      <ArrowRight className="h-3 w-3 ml-auto" />
                    </Button>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity Preview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Contacts */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardHeader className="border-b border-gray-100">
            <div className="flex items-center justify-between">
              <CardTitle className="gilroy-text-bold">Recent Contact Forms</CardTitle>
              <Link href="/admin/contacts">
                <Button variant="ghost" size="sm" className="gilroy-text">
                  View All
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-100">
              {recentContacts && recentContacts.length > 0 ? (
                recentContacts.map((contact) => (
                  <div key={contact.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 gilroy-text-medium">{contact.name}</p>
                        <p className="text-sm text-gray-600 gilroy-text">{contact.email}</p>
                        <p className="text-xs text-gray-500 gilroy-text mt-1">
                          {contact.category?.replace('-', ' ').toUpperCase() || 'GENERAL'}
                        </p>
                      </div>
                      <div className="text-right ml-4">
                        <Badge className="bg-blue-100 text-blue-800 gilroy-text-medium text-xs">
                          NEW
                        </Badge>
                        <p className="text-xs text-gray-500 gilroy-text mt-1">
                          {new Date(contact.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-8 text-center">
                  <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 gilroy-text">No new contact forms</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Active Projects */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardHeader className="border-b border-gray-100">
            <div className="flex items-center justify-between">
              <CardTitle className="gilroy-text-bold">Active Projects</CardTitle>
              <Link href="/admin/projects">
                <Button variant="ghost" size="sm" className="gilroy-text">
                  View All
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-100">
              {activeProjects && activeProjects.length > 0 ? (
                activeProjects.map((project) => (
                  <div key={project.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 gilroy-text-medium">{project.title}</p>
                        <p className="text-sm text-gray-600 gilroy-text">{project.customer_name}</p>
                        <p className="text-xs text-gray-500 gilroy-text mt-1">
                          {project.vehicle_details || 'No vehicle details'}
                        </p>
                      </div>
                      <div className="text-right ml-4 space-y-2">
                        <Badge className="bg-green-100 text-green-800 gilroy-text-medium text-xs">
                          ACTIVE
                        </Badge>
                        <div className="flex items-center space-x-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                              style={{ width: `${project.progress_percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500 gilroy-text-medium">{project.progress_percentage}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-8 text-center">
                  <FolderOpen className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 gilroy-text">No active projects</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 