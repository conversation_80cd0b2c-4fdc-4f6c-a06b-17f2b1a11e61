'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  FolderOpen, 
  Calendar, 
  Clock, 
  CheckCircle, 
  Circle, 
  AlertCircle,
  MessageSquare,
  TrendingUp,
  User,
  Mail,
  Phone,
  Car
} from 'lucide-react'

interface Project {
  id: string
  title: string
  description?: string
  customer_name: string
  customer_email?: string
  customer_phone?: string
  vehicle_details?: string
  status: string
  progress_percentage: number
  start_date?: string
  estimated_completion?: string
  created_at: string
}

interface Todo {
  id: string
  task: string
  description?: string
  priority: 'low' | 'medium' | 'high'
  status: 'pending' | 'in_progress' | 'completed'
  due_date?: string
  created_at: string
}

interface Update {
  id: string
  title: string
  description?: string
  update_type: 'progress' | 'communication' | 'milestone' | 'issue'
  progress_percentage?: number
  created_at: string
}

interface CustomerPortalProps {
  project: Project
  todos: Todo[]
  updates: Update[]
}

export default function CustomerPortal({ project, todos, updates }: CustomerPortalProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'on_hold':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case 'progress':
        return <TrendingUp className="h-4 w-4 text-blue-600" />
      case 'communication':
        return <MessageSquare className="h-4 w-4 text-green-600" />
      case 'milestone':
        return <CheckCircle className="h-4 w-4 text-purple-600" />
      case 'issue':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Circle className="h-4 w-4 text-gray-600" />
    }
  }

  const completedTodos = todos.filter(todo => todo.status === 'completed').length
  const totalTodos = todos.length
  const todoProgress = totalTodos > 0 ? (completedTodos / totalTodos) * 100 : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FolderOpen className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 gilroy-text-bold">
                  {project.title}
                </h1>
                <p className="text-gray-600 gilroy-text">
                  Project Portal - {project.customer_name}
                </p>
              </div>
            </div>
            <Badge className={`${getStatusColor(project.status)} gilroy-text-medium`}>
              {project.status.toUpperCase()}
            </Badge>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Project Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="gilroy-text-bold">Project Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {project.description && (
                  <div>
                    <h3 className="font-medium text-gray-900 gilroy-text-medium mb-2">Description</h3>
                    <p className="text-gray-600 gilroy-text">{project.description}</p>
                  </div>
                )}

                {/* Progress Bar */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900 gilroy-text-medium">Overall Progress</h3>
                    <span className="text-sm font-medium text-gray-600 gilroy-text-medium">
                      {project.progress_percentage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300" 
                      style={{ width: `${project.progress_percentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* Project Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {project.start_date && (
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500 gilroy-text">Start Date</p>
                        <p className="font-medium text-gray-900 gilroy-text-medium">
                          {new Date(project.start_date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  )}
                  {project.estimated_completion && (
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500 gilroy-text">Estimated Completion</p>
                        <p className="font-medium text-gray-900 gilroy-text-medium">
                          {new Date(project.estimated_completion).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Task Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="gilroy-text-bold">Task Progress</CardTitle>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600 gilroy-text">
                    {completedTodos} of {totalTodos} tasks completed
                  </p>
                  <span className="text-sm font-medium text-gray-600 gilroy-text-medium">
                    {Math.round(todoProgress)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${todoProgress}%` }}
                  ></div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-gray-100">
                  {todos.length > 0 ? (
                    todos.map((todo) => (
                      <div key={todo.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            {todo.status === 'completed' ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : todo.status === 'in_progress' ? (
                              <Clock className="h-5 w-5 text-yellow-600" />
                            ) : (
                              <Circle className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className={`font-medium gilroy-text-medium ${
                                todo.status === 'completed' ? 'text-gray-500 line-through' : 'text-gray-900'
                              }`}>
                                {todo.task}
                              </h3>
                              <div className="flex items-center space-x-2">
                                <Badge className={`${getPriorityColor(todo.priority)} gilroy-text-medium text-xs`}>
                                  {todo.priority.toUpperCase()}
                                </Badge>
                                {todo.due_date && (
                                  <span className="text-xs text-gray-500 gilroy-text">
                                    Due: {new Date(todo.due_date).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                            </div>
                            {todo.description && (
                              <p className="text-sm text-gray-600 gilroy-text mt-1">
                                {todo.description}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center">
                      <Circle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 gilroy-text">No tasks available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Project Updates */}
            <Card>
              <CardHeader>
                <CardTitle className="gilroy-text-bold">Project Updates</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-gray-100">
                  {updates.length > 0 ? (
                    updates.map((update) => (
                      <div key={update.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {getUpdateIcon(update.update_type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium text-gray-900 gilroy-text-medium">
                                {update.title}
                              </h3>
                              <span className="text-sm text-gray-500 gilroy-text">
                                {new Date(update.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            {update.description && (
                              <p className="text-sm text-gray-600 gilroy-text mt-1">
                                {update.description}
                              </p>
                            )}
                            {update.progress_percentage !== null && (
                              <div className="mt-2">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-xs text-gray-500 gilroy-text">Progress Update</span>
                                  <span className="text-xs font-medium text-gray-600 gilroy-text-medium">
                                    {update.progress_percentage}%
                                  </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-1">
                                  <div 
                                    className="bg-blue-600 h-1 rounded-full" 
                                    style={{ width: `${update.progress_percentage}%` }}
                                  ></div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center">
                      <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 gilroy-text">No updates available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="gilroy-text-bold">Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500 gilroy-text">Customer</p>
                    <p className="font-medium text-gray-900 gilroy-text-medium">
                      {project.customer_name}
                    </p>
                  </div>
                </div>
                {project.customer_email && (
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 gilroy-text">Email</p>
                      <p className="font-medium text-gray-900 gilroy-text-medium">
                        {project.customer_email}
                      </p>
                    </div>
                  </div>
                )}
                {project.customer_phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 gilroy-text">Phone</p>
                      <p className="font-medium text-gray-900 gilroy-text-medium">
                        {project.customer_phone}
                      </p>
                    </div>
                  </div>
                )}
                {project.vehicle_details && (
                  <div className="flex items-center space-x-3">
                    <Car className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 gilroy-text">Vehicle</p>
                      <p className="font-medium text-gray-900 gilroy-text-medium">
                        {project.vehicle_details}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="gilroy-text-bold">Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 gilroy-text">Total Tasks</span>
                  <span className="font-medium text-gray-900 gilroy-text-medium">{totalTodos}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 gilroy-text">Completed</span>
                  <span className="font-medium text-green-600 gilroy-text-medium">{completedTodos}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 gilroy-text">In Progress</span>
                  <span className="font-medium text-yellow-600 gilroy-text-medium">
                    {todos.filter(t => t.status === 'in_progress').length}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 gilroy-text">Project Updates</span>
                  <span className="font-medium text-gray-900 gilroy-text-medium">{updates.length}</span>
                </div>
              </CardContent>
            </Card>

            {/* Support */}
            <Card>
              <CardHeader>
                <CardTitle className="gilroy-text-bold">Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 gilroy-text mb-4">
                  If you have any questions about your project, please don't hesitate to contact us.
                </p>
                <Button className="w-full gilroy-text-medium">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 