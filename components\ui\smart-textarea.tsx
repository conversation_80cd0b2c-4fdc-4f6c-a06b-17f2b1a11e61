'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'

interface SmartTextareaProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  rows?: number
  disabled?: boolean
  name?: string
  // Add contact context for intelligent suggestions
  contactContext?: {
    name: string
    email: string
    message: string
    serviceType?: string
    serviceQuestions?: Record<string, string>
    selectedColor?: string
  }
}

interface LineInfo {
  lineNumber: number
  lineContent: string
  cursorPositionInLine: number
  totalLines: number
  isAtEndOfLine: boolean
  isAtEndOfText: boolean
}

interface SuggestionContext {
  fullText: string
  currentLine: string
  lineNumber: number
  cursorPositionInLine: number
  totalLines: number
  previousLines: string[]
  nextLines: string[]
  isAtEndOfLine: boolean
  isAtEndOfText: boolean
  // Add contact context for intelligent suggestions
  contactContext?: {
    name: string
    email: string
    message: string
    serviceType?: string
    serviceQuestions?: Record<string, string>
    selectedColor?: string
  }
}

export default function SmartTextarea({
  value,
  onChange,
  placeholder = 'Type your message...',
  className,
  rows = 4,
  disabled = false,
  name,
  contactContext
}: SmartTextareaProps) {
  const [suggestion, setSuggestion] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const ghostRef = useRef<HTMLDivElement>(null)
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Get detailed line information from cursor position
  const getLineInfo = useCallback((text: string, cursorPosition: number): LineInfo => {
    const lines = text.split('\n')
    let currentPosition = 0
    let lineNumber = 0
    let cursorPositionInLine = 0
    let lineContent = ''

    for (let i = 0; i < lines.length; i++) {
      const lineLength = lines[i].length
      const lineEndPosition = currentPosition + lineLength

      if (cursorPosition <= lineEndPosition) {
        lineNumber = i
        cursorPositionInLine = cursorPosition - currentPosition
        lineContent = lines[i]
        break
      }

      currentPosition = lineEndPosition + 1 // +1 for newline character
    }

    const isAtEndOfLine = cursorPositionInLine === lineContent.length
    const isAtEndOfText = cursorPosition === text.length

    return {
      lineNumber,
      lineContent,
      cursorPositionInLine,
      totalLines: lines.length,
      isAtEndOfLine,
      isAtEndOfText
    }
  }, [])

  // Create comprehensive context for AI suggestions
  const createSuggestionContext = useCallback((text: string, cursorPosition: number): SuggestionContext => {
    const lines = text.split('\n')
    const lineInfo = getLineInfo(text, cursorPosition)

    const previousLines = lines.slice(0, lineInfo.lineNumber)
    const nextLines = lines.slice(lineInfo.lineNumber + 1)

    return {
      fullText: text,
      currentLine: lineInfo.lineContent,
      lineNumber: lineInfo.lineNumber,
      cursorPositionInLine: lineInfo.cursorPositionInLine,
      totalLines: lineInfo.totalLines,
      previousLines,
      nextLines,
      isAtEndOfLine: lineInfo.isAtEndOfLine,
      isAtEndOfText: lineInfo.isAtEndOfText,
      contactContext
    }
  }, [getLineInfo, contactContext])

  // Fetch AI suggestions with enhanced context
  const fetchSuggestion = useCallback(async (text: string, cursorPosition: number) => {
    if (text.length < 5) return

    const context = createSuggestionContext(text, cursorPosition)
    
    // Only suggest when at end of line or text for better UX
    // Skip suggestions when cursor is at start of a new empty line to avoid duplicates
    if (context.cursorPositionInLine === 0 && context.currentLine.trim() === '') {
      setSuggestion('')
      return
    }

    if (!context.isAtEndOfLine && !context.isAtEndOfText) {
      setSuggestion('')
      return
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setIsLoading(true)

    try {
      const response = await fetch('/api/ai/typing-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ context }),
        signal: abortControllerRef.current.signal,
      })

      if (!response.ok) throw new Error('Failed to fetch suggestion')

      const data = await response.json()
      setSuggestion(data.suggestion || '')
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching suggestion:', error)
      }
      setSuggestion('')
    } finally {
      setIsLoading(false)
    }
  }, [createSuggestionContext])

  // Debounced suggestion fetching
  const debouncedFetchSuggestion = useCallback((text: string, cursorPosition: number) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      fetchSuggestion(text, cursorPosition)
    }, 800)
  }, [fetchSuggestion])

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    const cursorPosition = e.target.selectionStart
    
    onChange(newValue)
    debouncedFetchSuggestion(newValue, cursorPosition)
  }

  // Handle key presses
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab' && suggestion) {
      e.preventDefault()
      const textarea = textareaRef.current
      if (!textarea) return

      const cursorPosition = textarea.selectionStart
      const newValue = value + suggestion
      
      onChange(newValue)
      setSuggestion('')

      // Set cursor position after the suggestion
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(cursorPosition + suggestion.length, cursorPosition + suggestion.length)
      }, 0)
    } else if (e.key === 'Escape') {
      setSuggestion('')
    } else if (e.key !== 'Tab') {
      // Hide suggestion on other key presses
      setSuggestion('')
    }
  }

  // Handle cursor position changes
  const handleSelectionChange = () => {
    const textarea = textareaRef.current
    if (!textarea) return

    const cursorPosition = textarea.selectionStart
    const context = createSuggestionContext(value, cursorPosition)
    
    // Only show suggestions when at end of line or text
    if (!context.isAtEndOfLine && !context.isAtEndOfText) {
      setSuggestion('')
    }
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // Calculate ghost text position
  const getGhostTextStyle = () => {
    const textarea = textareaRef.current
    if (!textarea || !suggestion) return {}

    const style = window.getComputedStyle(textarea)
    return {
      fontFamily: style.fontFamily,
      fontSize: style.fontSize,
      lineHeight: style.lineHeight,
      padding: style.padding,
      border: 'transparent',
      whiteSpace: 'pre-wrap' as const,
      wordWrap: 'break-word' as const,
      overflow: 'hidden',
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      pointerEvents: 'none' as const,
      color: 'transparent',
      background: 'transparent',
      resize: 'none' as const,
    }
  }

  return (
    <div className="relative">
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onSelect={handleSelectionChange}
          onClick={handleSelectionChange}
          placeholder={placeholder}
          className={cn(
            'w-full resize-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            className
          )}
          rows={rows}
          disabled={disabled}
          name={name}
        />
        
        {/* Ghost text overlay */}
        {suggestion && (
          <div
            ref={ghostRef}
            style={getGhostTextStyle()}
            className="pointer-events-none"
          >
            <span style={{ color: 'transparent' }}>{value}</span>
            <span style={{ color: '#9CA3AF' }}>{suggestion}</span>
          </div>
        )}
      </div>

      {/* Status indicators */}
      <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
        <div className="flex items-center gap-2">
          {isLoading && (
            <span className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              AI thinking...
            </span>
          )}
          {suggestion && !isLoading && (
            <span className="text-gray-400">
              Press Tab to accept suggestion
            </span>
          )}
        </div>
        
        {/* Debug info (can be removed in production) */}
        {process.env.NODE_ENV === 'development' && value && (
          <div className="text-xs text-gray-400">
            {(() => {
              const textarea = textareaRef.current
              if (!textarea) return ''
              
              const cursorPosition = textarea.selectionStart
              const context = createSuggestionContext(value, cursorPosition)
              
              return `Line ${context.lineNumber + 1}/${context.totalLines} | Pos ${context.cursorPositionInLine}/${context.currentLine.length}`
            })()}
          </div>
        )}
      </div>
    </div>
  )
} 