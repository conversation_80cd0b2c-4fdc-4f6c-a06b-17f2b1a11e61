'use client';

import { useState, useEffect } from 'react';


import Image from 'next/image';
import Link from 'next/link';
import { AlertTriangle, CheckCircle, Clock, Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import JCCStickyFooter from '@/components/footer';
import Header from '@/components/header';

// Rust severity levels with interactive content
const rustSeverityLevels = [
  {
    id: 'surface',
    title: 'Surface Rust',
    severity: 'Low',
    description: 'Early stage rust that\'s just starting to show. Easily treatable and preventable.',
    timeframe: '1-2 days',
    cost: '£200-500',
    image: '/images/sticky-scroll-component-images/3.jpeg',
    treatments: ['Surface preparation', 'Rust converter application', 'Primer and paint', 'Protective coating']
  },
  {
    id: 'moderate',
    title: 'Moderate Rust',
    severity: 'Medium',
    description: 'Rust has started to eat into the metal. Needs proper treatment to stop spreading.',
    timeframe: '3-5 days',
    cost: '£500-1200',
    image: '/images/sticky-scroll-component-images/7.jpeg',
    treatments: ['Cut out affected areas', 'Weld in new metal', 'Professional rust treatment', 'Full respray of area']
  },
  {
    id: 'severe',
    title: 'Severe Rust',
    severity: 'High',
    description: 'Structural rust that\'s compromising the integrity. Urgent action needed!',
    timeframe: '1-2 weeks',
    cost: '£1200-3000+',
    image: '/images/sticky-scroll-component-images/5.jpeg',
    treatments: ['Complete section replacement', 'Structural welding', 'MOT compliance work', 'Full professional restoration']
  }
];

// FAQ data specific to rust
const rustFAQs = [
  {
    id: 1,
    question: "How do I know if my rust is serious?",
    answer: "If you can poke your finger through it, it's serious! But honestly, even surface rust spreads fast. Best to get it checked - we'll give you a free assessment and a cuppa while you wait.",
    highlight: "free assessment and a cuppa"
  },
  {
    id: 2,
    question: "Will rust come back after treatment?",
    answer: "Not if we do it properly! We don't just slap paint over rust - we cut it out, treat it properly, and use quality materials. That's why we can offer guarantees on our rust work.",
    highlight: "guarantees on our rust work"
  },
  {
    id: 3,
    question: "Can you fix rust holes?",
    answer: "Absolutely! We're proper old-school craftsmen. We'll cut out the bad bits and weld in new metal. Your van will be stronger than it was before!",
    highlight: "stronger than it was before"
  },
  {
    id: 4,
    question: "How much will it cost?",
    answer: "Depends on how bad it is, but we're always fair and transparent. No nasty surprises - we'll break down exactly what needs doing and why.",
    highlight: "always fair and transparent"
  },
  {
    id: 5,
    question: "Do you do insurance work?",
    answer: "We sure do! We work with all the major insurers and can handle all the paperwork. Just bring us your claim number and we'll sort the rest.",
    highlight: "handle all the paperwork"
  }
];

// Before & After stories
const rustStories = [
  {
    id: 1,
    title: "The VW T5 That Looked Beyond Hope",
    problem: "Massive rust holes in the rear quarter, floor pan rotted through",
    solution: "Complete section replacement, new floor pan, professional respray",
    result: "Like a brand new van - passed MOT with flying colors!",
    timeframe: "2 weeks",
    image: "/images/sticky-scroll-component-images/8.jpeg"
  },
  {
    id: 2,
    title: "Transit Rust Nightmare Turned Dream",
    problem: "Rust spreading from wheel arches, structural concerns",
    solution: "Cut out and weld, rust-proof treatment, protective coating",
    result: "Stronger than factory spec, customer over the moon",
    timeframe: "10 days",
    image: "/images/sticky-scroll-component-images/9.jpeg"
  }
];

// Interactive FAQ Component
const RustFAQItem = ({ item, isOpen, onToggle }: { 
  item: typeof rustFAQs[0], 
  isOpen: boolean, 
  onToggle: () => void 
}) => {
  const renderAnswer = (text: string, highlight: string) => {
    if (!highlight) return text;
    
    const parts = text.split(highlight);
    return (
      <>
        {parts[0]}
        <span className="bg-gray-200 text-black px-1 rounded font-medium">
          {highlight}
        </span>
        {parts[1]}
      </>
    );
  };

  return (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        className="w-full text-left py-6 px-8 hover:bg-gray-50 transition-colors flex items-center justify-between group"
        onClick={onToggle}
      >
        <span className="gilroy-text-bold text-lg md:text-xl font-semibold text-black group-hover:text-gray-700 transition-colors pr-4">
          {item.question}
        </span>
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.2 }}
          className="flex-shrink-0"
        >
          <Plus size={24} className="text-black" />
        </motion.div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="px-8 pb-6">
              <p className="gilroy-text text-base md:text-lg text-gray-600 leading-relaxed">
                {renderAnswer(item.answer, item.highlight)}
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default function RustPage() {
  const [selectedSeverity, setSelectedSeverity] = useState<string | null>(null);
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const [selectedStory, setSelectedStory] = useState(0);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      <Header onContactClick={() => scrollToSection('contact')} />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Metal Restoration and Welding Work"
            fill
            className="object-cover"
            priority
          />
        </div>
        
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black/75 z-5"></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:100px_100px] opacity-15 [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)] z-10"></div>
        
        <div className="relative z-20 text-center max-w-6xl mx-auto px-4">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >            
            <h1 className="gilroy-text-bold text-6xl md:text-8xl lg:text-9xl font-black uppercase tracking-tight text-white mb-6 leading-none">
              Rust
              <br />
              <span className="text-gray-400">Busters</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </motion.div>
           
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="gilroy-text text-xl md:text-2xl font-light mb-6 max-w-4xl mx-auto leading-relaxed text-gray-300"
          >
            We don&apos;t just treat rust - we obliterate it!
          </motion.p>
           
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="gilroy-text text-lg md:text-xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
          >
            From tiny bubbles to massive holes - no rust is too scary for us!
          </motion.p>
           
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center"
          >
            <button 
              onClick={() => scrollToSection('assessment')}
              className="gilroy-text-bold bg-white text-black px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Free Rust Check</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Free Rust Check
              </span>
            </button>
            <Link 
              href="/#contact"
              className="gilroy-text-bold border-2 border-white text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Emergency Call</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Emergency Call
              </span>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Interactive Rust Assessment Section */}
      <section id="assessment" className="py-32 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black">
              How Bad Is It?
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Click on your rust level below to see what we can do about it. Don&apos;t worry - we&apos;ve seen worse!
            </p>
          </div>

          {/* Interactive Severity Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {rustSeverityLevels.map((level) => (
              <motion.div
                key={level.id}
                className={`bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer transition-all duration-300 ${
                  selectedSeverity === level.id ? 'ring-4 ring-black transform scale-105' : 'hover:shadow-xl hover:transform hover:scale-102'
                }`}
                onClick={() => setSelectedSeverity(selectedSeverity === level.id ? null : level.id)}
                whileHover={{ y: -5 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={level.image}
                    alt={level.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-black text-white px-3 py-1 rounded-full text-sm font-bold">
                    {level.severity}
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="gilroy-text text-2xl font-bold mb-3 text-black">{level.title}</h3>
                  <p className="gilroy-text text-gray-600 mb-4 leading-relaxed">{level.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <Clock size={16} />
                      <span className="gilroy-text">{level.timeframe}</span>
                    </div>
                    <div className="gilroy-text-bold font-semibold text-black">{level.cost}</div>
                  </div>
                  
                  <div className="text-center">
                    <span className="gilroy-text-bold text-black font-medium text-sm uppercase tracking-wider">
                      {selectedSeverity === level.id ? 'Click to close' : 'Click for details'}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Expanded Details */}
          <AnimatePresence>
            {selectedSeverity && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-black text-white rounded-lg p-8 overflow-hidden"
              >
                {(() => {
                  const level = rustSeverityLevels.find(l => l.id === selectedSeverity);
                  if (!level) return null;
                  
                  return (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      <div>
                        <h3 className="gilroy-text text-3xl font-bold mb-4 text-white">{level.title} Treatment</h3>
                        <p className="gilroy-text text-xl text-gray-300 mb-6 leading-relaxed">
                          Here&apos;s exactly what we&apos;ll do to sort out your {level.title.toLowerCase()} problem:
                        </p>
                        
                        <ul className="space-y-3">
                          {level.treatments.map((treatment, index) => (
                            <li key={index} className="flex items-start space-x-3">
                              <CheckCircle size={20} className="text-white mt-1 flex-shrink-0" />
                              <span className="gilroy-text text-gray-300">{treatment}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="bg-white/10 rounded-lg p-6">
                        <h4 className="gilroy-text text-2xl font-bold mb-4 text-white">What You Get</h4>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Timeframe:</span>
                            <span className="gilroy-text-bold text-white font-semibold">{level.timeframe}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Typical Cost:</span>
                            <span className="gilroy-text-bold text-white font-semibold">{level.cost}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Guarantee:</span>
                            <span className="gilroy-text-bold text-white font-semibold">✓ Included</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Free Assessment:</span>
                            <span className="gilroy-text-bold text-white font-semibold">✓ Always</span>
                          </div>
                        </div>
                        
                        <button 
                          onClick={() => scrollToSection('contact')}
                          className="gilroy-text w-full mt-6 bg-white text-black py-3 px-6 font-bold uppercase tracking-wider hover:bg-gray-200 transition-colors"
                        >
                          Get This Fixed Now
                        </button>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[80vh]">
            {/* Left Content */}
            <div className="bg-black text-white p-12 lg:p-16 flex flex-col">
              <div className="flex flex-col justify-between flex-1">
                <div>
                  <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 leading-none text-white">
                    Rust Horror
                    <br />
                    <span className="text-3xl md:text-4xl font-light text-gray-300">
                      Stories
                    </span>
                  </h2>
                  
                  <div className="space-y-6 mb-10">
                    <p className="gilroy-text text-lg text-gray-300 leading-relaxed">
                      We&apos;ve seen some proper rust nightmares over the years. Vans that looked like they belonged in a scrapyard, holes you could put your head through, structural damage that made people cry.
                    </p>
                    
                    <p className="gilroy-text text-lg text-gray-300 leading-relaxed">
                      But here&apos;s the thing - we love a challenge! Every rust job is like solving a puzzle, and we&apos;re pretty good at it.
                    </p>
                  </div>
                </div>

                {/* Story Navigation */}
                <div className="space-y-4">
                  {rustStories.map((story, index) => (
                    <button
                      key={story.id}
                      onClick={() => setSelectedStory(index)}
                      className={`w-full text-left p-4 rounded transition-all ${
                        selectedStory === index 
                          ? 'bg-white text-black' 
                          : 'bg-white/10 text-white hover:bg-white/20'
                      }`}
                    >
                      <h3 className="gilroy-text font-bold text-lg mb-2">{story.title}</h3>
                      <p className="gilroy-text text-sm opacity-80">{story.problem}</p>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Content - Story Details */}
            <div className="bg-gray-100 p-12 lg:p-16 flex flex-col justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={selectedStory}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="mb-8">
                    <Image
                      src={rustStories[selectedStory].image}
                      alt={rustStories[selectedStory].title}
                      width={400}
                      height={300}
                      className="w-full h-64 object-cover rounded-lg shadow-lg"
                    />
                  </div>
                  
                  <h3 className="gilroy-text text-3xl font-bold mb-6 text-black">
                    {rustStories[selectedStory].title}
                  </h3>
                  
                  <div className="space-y-6">
                    <div>
                      <h4 className="gilroy-text font-bold text-black mb-2 uppercase tracking-wider">The Problem</h4>
                      <p className="gilroy-text text-gray-700 leading-relaxed">{rustStories[selectedStory].problem}</p>
                    </div>
                    
                    <div>
                      <h4 className="gilroy-text font-bold text-black mb-2 uppercase tracking-wider">Our Solution</h4>
                      <p className="gilroy-text text-gray-700 leading-relaxed">{rustStories[selectedStory].solution}</p>
                    </div>
                    
                    <div>
                      <h4 className="gilroy-text font-bold text-black mb-2 uppercase tracking-wider">The Result</h4>
                      <p className="gilroy-text text-gray-700 leading-relaxed">{rustStories[selectedStory].result}</p>
                    </div>
                    
                    <div className="bg-black text-white p-6 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="gilroy-text text-gray-300">Completed in:</span>
                        <span className="gilroy-text font-bold text-xl">{rustStories[selectedStory].timeframe}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight text-black mb-8">
              Rust Questions?
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We get asked about rust a lot. Here are the most common questions and our honest answers.
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
            {rustFAQs.map((item) => (
              <div key={item.id}>
                <RustFAQItem
                  item={item}
                  isOpen={openFAQ === item.id}
                  onToggle={() => setOpenFAQ(openFAQ === item.id ? null : item.id)}
                />
              </div>
            ))}
          </div>
          
          <div className="mt-20 text-center bg-black text-white py-16 px-8 rounded-lg">
            <h3 className="gilroy-text-bold text-3xl md:text-4xl font-black uppercase tracking-tight mb-6 text-white">
              Still Worried About Rust?
            </h3>
            <p className="gilroy-text text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              Pop round for a free assessment and a brew. We&apos;ll tell you exactly what&apos;s what - no sales pressure, just honest advice.
            </p>
            <button 
              onClick={() => scrollToSection('contact')}
              className="gilroy-text bg-white text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Free Rust Check</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Free Rust Check
              </span>
            </button>
          </div>
        </div>
      </section>

      {/* Emergency CTA Section */}
      <section className="py-20 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/10 border-2 border-white rounded-lg p-12">
            <AlertTriangle size={48} className="text-white mx-auto mb-6" />
            <h2 className="gilroy-text-bold text-4xl md:text-5xl font-black uppercase tracking-tight mb-8 text-white">
              Rust Emergency?
            </h2>
            <p className="gilroy-text text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-12">
              If your rust is getting worse by the day, don&apos;t wait! The longer you leave it, the more expensive it gets. We can often fit emergency jobs in within 24 hours.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link 
                href="/#contact"
                className="gilroy-text bg-white text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
              >
                <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Call Now: 01603 123456</span>
                <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                  Call Now: 01603 123456
                </span>
              </Link>
              
              <Link 
                href="/#contact"
                className="gilroy-text border-2 border-white text-white px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
              >
                <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Send Photos</span>
                <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                  Send Photos
                </span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
} 