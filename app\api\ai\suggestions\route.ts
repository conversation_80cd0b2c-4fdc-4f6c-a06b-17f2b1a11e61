import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Helper function to convert common color values to human-readable descriptions
function getCommonColorDescription(colorValue: string): string | null {
  // Handle common hex colors
  if (colorValue.startsWith('#')) {
    const commonColors: Record<string, string> = {
      '#000000': 'black',
      '#ffffff': 'white',
      '#ff0000': 'red',
      '#00ff00': 'green',
      '#0000ff': 'blue',
      '#ffff00': 'yellow',
      '#ff00ff': 'magenta',
      '#00ffff': 'cyan',
      '#c0c0c0': 'silver',
      '#808080': 'gray',
      '#800000': 'maroon',
      '#808000': 'olive',
      '#008000': 'dark green',
      '#800080': 'purple',
      '#008080': 'teal',
      '#000080': 'navy blue'
    }
    
    const lowerColor = colorValue.toLowerCase()
    if (commonColors[lowerColor]) {
      return commonColors[lowerColor]
    }
  }
  
  // Handle basic grayscale HSL colors
  if (colorValue.startsWith('hsl(')) {
    const hslMatch = colorValue.match(/hsl\((\d+(?:\.\d+)?),\s*(\d+(?:\.\d+)?)%,\s*(\d+(?:\.\d+)?)%\)/)
    if (hslMatch) {
      const saturation = parseFloat(hslMatch[2])
      const lightness = parseFloat(hslMatch[3])
      
      // Only handle grayscale colors (low saturation)
      if (saturation < 10) {
        if (lightness > 95) return 'white'
        else if (lightness > 80) return 'light gray'
        else if (lightness > 60) return 'gray'
        else if (lightness > 30) return 'dark gray'
        else if (lightness > 5) return 'very dark gray'
        else return 'black'
      }
    }
  }
  
  // Return original value if no common color match found, or null if input is null
  return colorValue
}

const requestSchema = z.object({
  contactContext: z.object({
    name: z.string(),
    email: z.string(),
    message: z.string(),
    serviceType: z.string().optional(),
    serviceQuestions: z.record(z.string(), z.string()).optional(),
    selectedColor: z.string().optional(),
    vehicleType: z.string().optional(),
    vehicleMake: z.string().optional(),
    vehicleModel: z.string().optional(),
    vehicleYear: z.number().optional(),
  }),
  currentResponse: z.string().optional(),
  tone: z.enum(['professional', 'casual']).optional(),
  type: z.enum(['suggestions', 'enhance']),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { contactContext, currentResponse, tone = 'professional', type } = requestSchema.parse(body)

    const openRouterApiKey = process.env.OPENROUTER_API_KEY
    const model = process.env.OPENROUTER_MODEL

    if (!openRouterApiKey) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500 }
      )
    }

    let prompt = ''
    
    // Build prompt for generating a single, markdown-formatted email response
    if (type === 'suggestions') {
      // Build comprehensive customer context
      const vehicleInfo = [
        contactContext.vehicleYear,
        contactContext.vehicleMake,
        contactContext.vehicleModel,
        contactContext.vehicleType && `(${contactContext.vehicleType})`
      ].filter(Boolean).join(' ')

      const colorDescription = contactContext.selectedColor 
        ? getCommonColorDescription(contactContext.selectedColor) || `${contactContext.selectedColor} (describe this color in simple, human-friendly terms)`
        : null

      // Format service questions for better readability
      const serviceQuestionsList = contactContext.serviceQuestions 
        ? Object.entries(contactContext.serviceQuestions).map(([key, value]) => {
            const formattedKey = key.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
            return `- ${formattedKey}: ${value}`
          }).join('\n')
        : ''

      prompt = `You are an expert customer service representative for JCC Customs and Commercial, a specialist company for campervans, caravans, motorhomes, vans, and cars. They offer services including accident repairs, resprays, full restorations, interior conversions, and advanced campervan electrics.

GLOBAL CONTEXT:
- Company Location: Norwich, United Kingdom (UK)
- Language: Use UK English spelling and grammar.
- Currency: If mentioning currency, assume GBP (£).
- Dates: Format dates using UK standards (e.g., DD/MM/YYYY or Day, DD Month YYYY).
- Pricing (Approximate Starting From):
  - Half Resprays: £1700
  - Full Resprays: £2990

CUSTOMER CONTEXT (ALREADY PROVIDED - USE THIS INFORMATION):
- Name: ${contactContext.name}
- Email: ${contactContext.email}
- Service Type: ${contactContext.serviceType || 'General inquiry'}
- Original Message: "${contactContext.message}"

${vehicleInfo ? `VEHICLE DETAILS (ALREADY PROVIDED):
- Vehicle: ${vehicleInfo}` : ''}

${colorDescription ? `COLOR SELECTION (ALREADY CHOSEN - TREAT AS FINAL):
- Selected Color: ${colorDescription} (If hex code, assume it's the exact shade desired)` : ''}

${serviceQuestionsList ? `SERVICE DETAILS (ALREADY PROVIDED):
${serviceQuestionsList}` : ''}

COLOR HANDLING RULES:
1. If a color is provided (e.g., red or #FF0000), TREAT IT AS THE EXACT, FINAL CHOICE. Do not ask for more details like shade or paint code.
2. Reference the given color directly (e.g., 'your chosen red (#FF0000)').
3. Never request clarification on color if it's listed above.

CRITICAL INSTRUCTIONS - AVOID ASKING FOR ALREADY PROVIDED INFORMATION:
1. The customer has **ALREADY** provided all the information listed above in the 'CUSTOMER CONTEXT' and 'VEHICLE DETAILS' sections. DO NOT ask for it again.
2. You **MUST** acknowledge and reference the specific details provided, such as their vehicle (make, model, year, type) and selected color.
3. ONLY ask for information that is genuinely missing and absolutely essential for providing a quote or service.
4. Be helpful and demonstrate that you have a clear understanding of their specific needs based on the information they've supplied.

TASK: Write ONE clear, concise email reply addressing their inquiry. Requirements:
1. DO NOT include a subject line
2. Start with "Dear ${contactContext.name},"
3. Use a ${tone} tone (professional yet friendly)
4. ACKNOWLEDGE their provided information (vehicle, color, service details) in your response
5. Provide a preliminary quote or estimate based on known info if possible
6. Use Markdown for paragraph spacing (blank line between paragraphs). Avoid headings or bold text
7. Keep length between 120-200 words
8. Show you understand their specific needs based on what they've told you

Return ONLY the email body, with no JSON, code fences, or commentary.`;
    } else {
      prompt = `You are an expert editor improving email tone and clarity. 

Current Response Draft:
${currentResponse}

CRITICAL INSTRUCTIONS:
1. ONLY improve the tone, grammar, and clarity of the existing text
2. DO NOT add new content, information, or paragraphs
3. DO NOT change the core message or structure
4. Keep the same length - do not make it significantly longer or shorter
5. Make it sound more ${tone === 'professional' ? 'professional and polished' : 'friendly and conversational'}
6. Fix any grammar or awkward phrasing
7. Maintain all the same key points and information

Return ONLY the improved version of the exact same response, with no additional content or explanations.`
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'JCC Customs AI Assistant',
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 1000,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('OpenRouter API error:', errorText)
      return NextResponse.json(
        { error: 'Failed to get AI suggestions' },
        { status: 500 }
      )
    }

    const data = await response.json()
    const aiResponse = data.choices[0]?.message?.content

    if (!aiResponse) {
      return NextResponse.json(
        { error: 'No response from AI' },
        { status: 500 }
      )
    }

    if (type === 'suggestions') {
      const suggestions = [
        {
          title: 'AI Suggestion',
          content: aiResponse.trim(),
        },
      ]

      return NextResponse.json({ suggestions })
    } else {
      return NextResponse.json({ enhancedResponse: aiResponse })
    }
  } catch (error) {
    console.error('AI suggestions error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 