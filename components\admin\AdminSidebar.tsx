'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Home, 
  MessageSquare, 
  FolderOpen, 
  Mail, 
  Users, 
  FileText, 
  Images
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: Home },
  { name: 'Contact Forms', href: '/admin/contacts', icon: MessageSquare },
  { name: 'Projects', href: '/admin/projects', icon: FolderOpen },
  { name: 'Email System', href: '/admin/emails', icon: Mail },
  { name: 'Subscribers', href: '/admin/subscribers', icon: Users },
  { name: 'Blog', href: '/admin/blog', icon: FileText },
  { name: 'Gallery', href: '/admin/gallery', icon: Images },
]

export default function AdminSidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200 h-screen sticky top-0">
      {/* Navigation */}
      <div className="p-4 pt-8">
        <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wider gilroy-text-medium mb-4">
          Navigation
        </h2>
        <nav className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`
                  group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 gilroy-text
                  ${isActive 
                    ? 'bg-gray-900 text-white shadow-sm' 
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }
                `}
              >
                <item.icon
                  className={`
                    mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-200
                    ${isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'}
                  `}
                />
                {item.name}
              </Link>
            )
          })}
        </nav>
      </div>
      
      {/* Bottom section with website link */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <Link
          href="/"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200 gilroy-text"
        >
          <span>← Back to Website</span>
        </Link>
      </div>
    </div>
  )
}
