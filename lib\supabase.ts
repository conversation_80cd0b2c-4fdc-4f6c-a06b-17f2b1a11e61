import { createBrowserClient, createServerClient, type CookieOptions } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client for browser components
export const createClient = () => createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client for server components, route handlers, and server actions
export const createServerSupabaseClient = async () => {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()

  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet: { name: string; value: string; options: CookieOptions }[]) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          )
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  })
}

// Database types
export interface Database {
  public: {
    Tables: {
      admin_users: {
        Row: {
          id: string
          email: string
          password_hash: string | null
          name: string
          role: 'admin' | 'super_admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash?: string | null
          name: string
          role?: 'admin' | 'super_admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string | null
          name?: string
          role?: 'admin' | 'super_admin'
          created_at?: string
          updated_at?: string
        }
      }
      contact_submissions: {
        Row: {
          id: string
          name: string
          email: string
          phone: string | null
          company: string | null
          service_type: string | null
          vehicle_type: string | null
          message: string
          status: 'new' | 'responded' | 'archived'
          admin_response: string | null
          response_sent_at: string | null
          created_at: string
          updated_at: string
          // New fields for enhanced contact form
          service_questions: Record<string, string> | null
          selected_color: string | null
          attachments: string[] | null
          attachment_urls: string[] | null
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone?: string | null
          company?: string | null
          service_type?: string | null
          vehicle_type?: string | null
          message: string
          status?: 'new' | 'responded' | 'archived'
          admin_response?: string | null
          response_sent_at?: string | null
          created_at?: string
          updated_at?: string
          // New fields for enhanced contact form
          service_questions?: Record<string, string> | null
          selected_color?: string | null
          attachments?: string[] | null
          attachment_urls?: string[] | null
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string | null
          company?: string | null
          service_type?: string | null
          vehicle_type?: string | null
          message?: string
          status?: 'new' | 'responded' | 'archived'
          admin_response?: string | null
          response_sent_at?: string | null
          created_at?: string
          updated_at?: string
          // New fields for enhanced contact form
          service_questions?: Record<string, string> | null
          selected_color?: string | null
          attachments?: string[] | null
          attachment_urls?: string[] | null
        }
      }
      projects: {
        Row: {
          id: string
          title: string
          description: string | null
          customer_name: string
          customer_email: string
          customer_phone: string | null
          status: 'active' | 'completed' | 'on_hold' | 'cancelled'
          progress_percentage: number
          estimated_completion: string | null
          actual_completion: string | null
          public_access_token: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          customer_name: string
          customer_email: string
          customer_phone?: string | null
          status?: 'active' | 'completed' | 'on_hold' | 'cancelled'
          progress_percentage?: number
          estimated_completion?: string | null
          actual_completion?: string | null
          public_access_token?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          customer_name?: string
          customer_email?: string
          customer_phone?: string | null
          status?: 'active' | 'completed' | 'on_hold' | 'cancelled'
          progress_percentage?: number
          estimated_completion?: string | null
          actual_completion?: string | null
          public_access_token?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      project_todos: {
        Row: {
          id: string
          project_id: string
          title: string
          description: string | null
          completed: boolean
          completed_at: string | null
          completed_by: string | null
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          title: string
          description?: string | null
          completed?: boolean
          completed_at?: string | null
          completed_by?: string | null
          order_index?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          title?: string
          description?: string | null
          completed?: boolean
          completed_at?: string | null
          completed_by?: string | null
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
      project_updates: {
        Row: {
          id: string
          project_id: string
          title: string
          content: string
          content_html: string
          images: string[] | null
          created_by: string | null
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          title: string
          content: string
          content_html: string
          images?: string[] | null
          created_by?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          title?: string
          content?: string
          content_html?: string
          images?: string[] | null
          created_by?: string | null
          created_at?: string
        }
      }
      subscribers: {
        Row: {
          id: string
          email: string
          name: string | null
          status: 'active' | 'unsubscribed' | 'bounced'
          tags: string[] | null
          subscribed_at: string
          unsubscribed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          status?: 'active' | 'unsubscribed' | 'bounced'
          tags?: string[] | null
          subscribed_at?: string
          unsubscribed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          status?: 'active' | 'unsubscribed' | 'bounced'
          tags?: string[] | null
          subscribed_at?: string
          unsubscribed_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      email_campaigns: {
        Row: {
          id: string
          title: string
          subject: string
          content: string
          content_html: string
          recipient_tags: string[] | null
          status: 'draft' | 'sending' | 'sent' | 'cancelled'
          scheduled_at: string | null
          sent_at: string | null
          recipient_count: number
          opened_count: number
          clicked_count: number
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          subject: string
          content: string
          content_html: string
          recipient_tags?: string[] | null
          status?: 'draft' | 'sending' | 'sent' | 'cancelled'
          scheduled_at?: string | null
          sent_at?: string | null
          recipient_count?: number
          opened_count?: number
          clicked_count?: number
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          subject?: string
          content?: string
          content_html?: string
          recipient_tags?: string[] | null
          status?: 'draft' | 'sending' | 'sent' | 'cancelled'
          scheduled_at?: string | null
          sent_at?: string | null
          recipient_count?: number
          opened_count?: number
          clicked_count?: number
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      blog_articles: {
        Row: {
          id: string
          title: string
          slug: string
          excerpt: string | null
          content: string
          content_html: string
          featured_image: string | null
          hero_image: string | null
          images: string[] | null
          tags: string[] | null
          status: 'draft' | 'published' | 'archived'
          published_at: string | null
          seo_title: string | null
          seo_description: string | null
          author_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          excerpt?: string | null
          content: string
          content_html: string
          featured_image?: string | null
          hero_image?: string | null
          images?: string[] | null
          tags?: string[] | null
          status?: 'draft' | 'published' | 'archived'
          published_at?: string | null
          seo_title?: string | null
          seo_description?: string | null
          author_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          excerpt?: string | null
          content?: string
          content_html?: string
          featured_image?: string | null
          hero_image?: string | null
          images?: string[] | null
          tags?: string[] | null
          status?: 'draft' | 'published' | 'archived'
          published_at?: string | null
          seo_title?: string | null
          seo_description?: string | null
          author_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      gallery_images: {
        Row: {
          id: string
          title: string
          description: string | null
          image_url: string
          thumbnail_url: string | null
          alt_text: string | null
          category: string | null
          tags: string[] | null
          order_index: number
          is_featured: boolean
          is_public: boolean
          file_size: number | null
          file_type: string | null
          uploaded_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          image_url: string
          thumbnail_url?: string | null
          alt_text?: string | null
          category?: string | null
          tags?: string[] | null
          order_index?: number
          is_featured?: boolean
          is_public?: boolean
          file_size?: number | null
          file_type?: string | null
          uploaded_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          image_url?: string
          thumbnail_url?: string | null
          alt_text?: string | null
          category?: string | null
          tags?: string[] | null
          order_index?: number
          is_featured?: boolean
          is_public?: boolean
          file_size?: number | null
          file_type?: string | null
          uploaded_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      before_after_pairs: {
        Row: {
          id: string
          title: string
          description: string | null
          before_image_id: string
          after_image_id: string
          category: string | null
          tags: string[] | null
          order_index: number
          is_featured: boolean
          is_public: boolean
          vehicle_details: Record<string, any> | null
          work_details: Record<string, any> | null
          created_at: string
          updated_at: string
          uploaded_by: string | null
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          before_image_id: string
          after_image_id: string
          category?: string | null
          tags?: string[] | null
          order_index?: number
          is_featured?: boolean
          is_public?: boolean
          vehicle_details?: Record<string, any> | null
          work_details?: Record<string, any> | null
          created_at?: string
          updated_at?: string
          uploaded_by?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          before_image_id?: string
          after_image_id?: string
          category?: string | null
          tags?: string[] | null
          order_index?: number
          is_featured?: boolean
          is_public?: boolean
          vehicle_details?: Record<string, any> | null
          work_details?: Record<string, any> | null
          created_at?: string
          updated_at?: string
          uploaded_by?: string | null
        }
      }
      email_logs: {
        Row: {
          id: string
          to_email: string
          subject: string
          content: string
          type: 'contact_response' | 'project_update' | 'marketing' | 'system'
          status: 'pending' | 'sent' | 'failed' | 'bounced'
          sent_at: string | null
          error_message: string | null
          campaign_id: string | null
          project_id: string | null
          contact_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          to_email: string
          subject: string
          content: string
          type: 'contact_response' | 'project_update' | 'marketing' | 'system'
          status?: 'pending' | 'sent' | 'failed' | 'bounced'
          sent_at?: string | null
          error_message?: string | null
          campaign_id?: string | null
          project_id?: string | null
          contact_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          to_email?: string
          subject?: string
          content?: string
          type?: 'contact_response' | 'project_update' | 'marketing' | 'system'
          status?: 'pending' | 'sent' | 'failed' | 'bounced'
          sent_at?: string | null
          error_message?: string | null
          campaign_id?: string | null
          project_id?: string | null
          contact_id?: string | null
          created_at?: string
        }
      }
    }
  }
} 