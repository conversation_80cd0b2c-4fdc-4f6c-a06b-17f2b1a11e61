import { z } from 'zod';

// Common validation patterns
const emailSchema = z.string().email('Please enter a valid email address');
const phoneSchema = z.string()
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
  .optional()
  .or(z.literal(''));
const slugSchema = z.string()
  .min(1, 'Slug is required')
  .max(100, 'Slug must be less than 100 characters')
  .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug must be lowercase letters, numbers, and hyphens only');
const urlSchema = z.string().url('Please enter a valid URL').optional().or(z.literal(''));
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number');

// Admin Login Schema
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

// Contact Response Schema
export const contactResponseSchema = z.object({
  response: z.string().min(10, 'Response must be at least 10 characters'),
  responseHtml: z.string().min(10, 'Response content is required'),
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject must be less than 200 characters'),
});

// Project Schema
export const projectSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  customerName: z.string().min(2, 'Customer name must be at least 2 characters').max(100, 'Customer name must be less than 100 characters'),
  customerEmail: emailSchema,
  customerPhone: phoneSchema,
  vehicleDetails: z.string().max(500, 'Vehicle details must be less than 500 characters').optional(),
  status: z.enum(['planning', 'in_progress', 'completed', 'on_hold', 'cancelled']),
  progressPercentage: z.number().min(0, 'Progress must be at least 0%').max(100, 'Progress cannot exceed 100%'),
  estimatedCompletion: z.string().optional(),
  actualCompletion: z.string().optional(),
});

// Project Todo Schema
export const projectTodoSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  priority: z.enum(['low', 'medium', 'high']),
  dueDate: z.string().optional(),
});

// Project Update Schema
export const projectUpdateSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(200, 'Title must be less than 200 characters'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  updateType: z.enum(['progress', 'communication', 'milestone', 'issue']),
  progressPercentage: z.number().min(0, 'Progress must be at least 0%').max(100, 'Progress cannot exceed 100%').optional(),
});

// Email Campaign Schema
export const emailCampaignSchema = z.object({
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject must be less than 200 characters'),
  previewText: z.string().max(150, 'Preview text must be less than 150 characters').optional(),
  senderName: z.string().min(2, 'Sender name must be at least 2 characters').max(100, 'Sender name must be less than 100 characters'),
  senderEmail: emailSchema,
  content: z.string().min(50, 'Content must be at least 50 characters'),
  contentHtml: z.string().min(50, 'Content is required'),
  status: z.enum(['draft', 'sending', 'sent', 'cancelled']),
});

// Subscriber Schema
export const subscriberSchema = z.object({
  email: emailSchema,
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters').optional(),
  status: z.enum(['active', 'unsubscribed', 'bounced']),
  tags: z.array(z.string()).optional(),
});

// Blog Article Schema (for drafts - more lenient)
export const blogArticleSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  slug: z.string().optional(), // Allow empty slug for drafts
  excerpt: z.string().max(300, 'Excerpt must be less than 300 characters').optional(),
  content: z.string().optional(), // Allow empty content for drafts
  contentHtml: z.string().optional(), // Allow empty content for drafts
  featuredImage: z.string().optional(), // Allow empty featured image for drafts
  heroImage: z.string().optional(), // Allow empty hero image for drafts
  category: z.string().min(1, 'Category is required').default('general'),
  tags: z.array(z.string()).optional(),
  status: z.enum(['draft', 'published', 'archived']),
  publishedAt: z.string().optional(),
  seoTitle: z.string().max(60, 'SEO title must be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description must be less than 160 characters').optional(),
});

// Blog Article Schema for published articles (stricter validation)
export const blogArticlePublishSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters').max(200, 'Title must be less than 200 characters'),
  slug: slugSchema,
  excerpt: z.string().min(50, 'Excerpt must be at least 50 characters').max(300, 'Excerpt must be less than 300 characters'),
  content: z.string().min(100, 'Content must be at least 100 characters'),
  contentHtml: z.string().min(100, 'Content is required'),
  featuredImage: urlSchema,
  heroImage: z.string().optional(), // Hero image is optional even for published articles
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional(),
  status: z.enum(['draft', 'published', 'archived']),
  publishedAt: z.string().optional(),
  seoTitle: z.string().max(60, 'SEO title must be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description must be less than 160 characters').optional(),
});

// Gallery Image Schema
export const galleryImageSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  altText: z.string().min(5, 'Alt text must be at least 5 characters').max(200, 'Alt text must be less than 200 characters'),
  category: z.string().min(2, 'Category must be at least 2 characters').max(50, 'Category must be less than 50 characters').optional(),
  tags: z.array(z.string()).optional(),
  orderIndex: z.number().min(0, 'Order index must be at least 0').optional(),
  isFeatured: z.boolean().optional(),
});

// File Upload Schema
export const fileUploadSchema = z.object({
  file: z.instanceof(File).refine(
    (file) => file.size <= 50 * 1024 * 1024, // 50MB limit
    'File size must be less than 50MB'
  ).refine(
    (file) => {
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'text/csv',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ]
      return allowedTypes.includes(file.type)
    },
    'File type not supported. Please use JPEG, PNG, GIF, WebP, SVG, CSV, PDF, or Word documents.'
  ),
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  altText: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
})

// Before/After pair upload schema
export const beforeAfterUploadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  beforeImageFile: z.instanceof(File).refine(
    (file) => file.size <= 10 * 1024 * 1024, // 10MB limit for images
    'Before image size must be less than 10MB'
  ).refine(
    (file) => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      return allowedTypes.includes(file.type)
    },
    'Before image must be a valid image (JPEG, PNG, GIF, or WebP)'
  ),
  afterImageFile: z.instanceof(File).refine(
    (file) => file.size <= 10 * 1024 * 1024, // 10MB limit for images
    'After image size must be less than 10MB'
  ).refine(
    (file) => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      return allowedTypes.includes(file.type)
    },
    'After image must be a valid image (JPEG, PNG, GIF, or WebP)'
  ),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  orderIndex: z.number().min(0, 'Order must be 0 or higher').default(0),
  // Vehicle details
  vehicleYear: z.string().optional(),
  vehicleMake: z.string().optional(),
  vehicleModel: z.string().optional(),
  vehicleType: z.string().optional(),
  vehicleColor: z.string().optional(),
  // Work details
  completionDate: z.string().optional(),
  estimatedCost: z.string().optional(),
  workNotes: z.string().optional(),
})

// Gallery image upload with file schema
export const galleryImageUploadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  altText: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  orderIndex: z.number().min(0, 'Order must be 0 or higher').default(0),
  imageFile: z.instanceof(File).refine(
    (file) => file.size <= 10 * 1024 * 1024, // 10MB limit for images
    'Image size must be less than 10MB'
  ).refine(
    (file) => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      return allowedTypes.includes(file.type)
    },
    'File must be a valid image (JPEG, PNG, GIF, or WebP)'
  ),
})

// Subscriber import schema
export const subscriberImportSchema = z.object({
  csvFile: z.instanceof(File).refine(
    (file) => file.size <= 5 * 1024 * 1024, // 5MB limit for CSV
    'CSV file size must be less than 5MB'
  ).refine(
    (file) => file.type === 'text/csv' || file.name.endsWith('.csv'),
    'File must be a CSV file'
  ),
  overwriteExisting: z.boolean().default(false),
  sendWelcomeEmail: z.boolean().default(false),
})

// Subscriber form schema
export const subscriberFormSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  status: z.enum(['active', 'unsubscribed', 'bounced']).default('active'),
  tags: z.array(z.string()).optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
})

// Bulk Email Schema
export const bulkEmailSchema = z.object({
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject must be less than 200 characters'),
  content: z.string().min(50, 'Content must be at least 50 characters'),
  contentHtml: z.string().min(50, 'Content is required'),
  recipientEmails: z.array(emailSchema).min(1, 'At least one recipient is required'),
});

// Search and Filter Schemas
export const searchSchema = z.object({
  query: z.string().max(200, 'Search query must be less than 200 characters').optional(),
  status: z.string().optional(),
  category: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

// Export types
export type LoginInput = z.infer<typeof loginSchema>;
export type ContactResponseInput = z.infer<typeof contactResponseSchema>;
export type ProjectInput = z.infer<typeof projectSchema>;
export type ProjectTodoInput = z.infer<typeof projectTodoSchema>;
export type ProjectUpdateInput = z.infer<typeof projectUpdateSchema>;
export type EmailCampaignInput = z.infer<typeof emailCampaignSchema>;
export type SubscriberInput = z.infer<typeof subscriberSchema>;
export type BlogArticleInput = z.infer<typeof blogArticleSchema>;
export type GalleryImageInput = z.infer<typeof galleryImageSchema>;
export type GalleryImageUploadInput = z.infer<typeof galleryImageUploadSchema>;
export type BeforeAfterUploadInput = z.infer<typeof beforeAfterUploadSchema>;
export type SubscriberImportInput = z.infer<typeof subscriberImportSchema>;
export type SubscriberFormInput = z.infer<typeof subscriberFormSchema>;
export type FileUploadInput = z.infer<typeof fileUploadSchema>;
export type BulkEmailInput = z.infer<typeof bulkEmailSchema>;
export type SearchInput = z.infer<typeof searchSchema>;

// Integrated gallery upload schemas (without file validation for client-side use)
export const galleryUploadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  altText: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  orderIndex: z.number().min(0, 'Order must be 0 or higher').default(0),
  albumId: z.string().optional(), // Optional album assignment
  // Vehicle details (optional for regular uploads)
  vehicleYear: z.string().optional(),
  vehicleMake: z.string().optional(),
  vehicleModel: z.string().optional(),
  vehicleType: z.string().optional(),
  vehicleColor: z.string().optional(),
  // Work details (optional for regular uploads)
  completionDate: z.string().optional(),
  estimatedCost: z.string().optional(),
  workNotes: z.string().optional(),
})

export const beforeAfterFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  altText: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  category: z.string().min(1, 'Category is required').default('before-after'),
  tags: z.string().optional(),
  isPublic: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  orderIndex: z.number().min(0, 'Order must be 0 or higher').default(0),
  albumId: z.string().optional(), // Optional album assignment
  // Vehicle details
  vehicleYear: z.string().optional(),
  vehicleMake: z.string().min(1, 'Vehicle make is required'),
  vehicleModel: z.string().min(1, 'Vehicle model is required'),
  vehicleType: z.string().optional(),
  vehicleColor: z.string().optional(),
  // Work details
  completionDate: z.string().optional(),
  estimatedCost: z.string().optional(),
  workNotes: z.string().optional(),
})

// Export new types
export type GalleryUpload = z.infer<typeof galleryUploadSchema>;
export type BeforeAfterUpload = z.infer<typeof beforeAfterFormSchema>;
