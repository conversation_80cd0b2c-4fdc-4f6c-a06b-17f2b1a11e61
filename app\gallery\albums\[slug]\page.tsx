'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, Play, X, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '@/components/header';
import JCCStickyFooter from '@/components/footer';
import BeforeAfterSlider from '@/components/ui/before-after-slider';

// Album and Image interfaces
interface AlbumImage {
    id: string;
    title: string;
    description: string;
    category: string;
    type: 'image' | 'video' | 'before-after';
    src: string;
    thumbnail?: string;
    tags: string[];
    featured?: boolean;
    alt_text?: string;
    created_at?: string;
    order_index?: number;
    album_id?: string;
    beforeImage?: {
        id: string;
        url: string;
        alt: string;
        title: string;
    };
    afterImage?: {
        id: string;
        url: string;
        alt: string;
        title: string;
    };
}

interface Album {
    id: string;
    title: string;
    description: string;
    slug: string;
    cover_image_url?: string;
    cover_image_alt?: string;
    is_featured: boolean;
    is_published: boolean;
    order_index: number;
    created_at: string;
    updated_at: string;
    images?: AlbumImage[];
}

interface AlbumPageProps {
    params: {
        slug: string;
    };
}

export default function AlbumPage({ params }: AlbumPageProps) {
    const [album, setAlbum] = useState<Album | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [selectedImage, setSelectedImage] = useState<AlbumImage | null>(null);
    const [isLightboxOpen, setIsLightboxOpen] = useState(false);

    // Fetch album data
    useEffect(() => {
        const fetchAlbum = async () => {
            setIsLoading(true);
            setError('');

            try {
                const resolvedParams = await params;

                // First, fetch albums to find the one with matching slug
                const albumsResponse = await fetch('/api/gallery/albums?published_only=true');
                if (!albumsResponse.ok) {
                    throw new Error('Failed to fetch albums');
                }

                const albumsData = await albumsResponse.json();
                const targetAlbum = albumsData.albums?.find((a: Album) => a.slug === resolvedParams.slug);

                if (!targetAlbum) {
                    setError('Album not found');
                    return;
                }

                // Now fetch the album with its images
                const albumResponse = await fetch(`/api/gallery/albums/${targetAlbum.id}?include_images=true`);
                if (!albumResponse.ok) {
                    throw new Error('Failed to fetch album details');
                }

                const albumData = await albumResponse.json();

                // First, check if this album has before/after pairs
                const beforeAfterResponse = await fetch('/api/gallery/before-after');
                let beforeAfterPairs: any[] = [];
                if (beforeAfterResponse.ok) {
                    const beforeAfterData = await beforeAfterResponse.json();
                    beforeAfterPairs = beforeAfterData.filter((pair: any) => {
                        // Check if both images in the pair belong to this album
                        const beforeInAlbum = albumData.album.images?.some((img: any) => img.id === pair.beforeImage.id);
                        const afterInAlbum = albumData.album.images?.some((img: any) => img.id === pair.afterImage.id);
                        return beforeInAlbum && afterInAlbum;
                    });
                }

                // Get IDs of images that are part of before/after pairs
                const beforeAfterImageIds = new Set();
                beforeAfterPairs.forEach((pair: any) => {
                    beforeAfterImageIds.add(pair.beforeImage.id);
                    beforeAfterImageIds.add(pair.afterImage.id);
                });

                // Transform images to match the expected interface
                const transformedImages = albumData.album.images?.filter((img: any) =>
                    !beforeAfterImageIds.has(img.id) // Exclude images that are part of before/after pairs
                ).map((img: any) => ({
                    ...img,
                    src: img.image_url, // Map image_url to src
                    type: 'image',
                    description: img.description || '',
                    category: img.category || '',
                    tags: img.tags || [],
                    featured: img.is_featured || false
                })) || [];

                // Add before/after pairs as combined items
                const beforeAfterItems = beforeAfterPairs.map((pair: any) => ({
                    id: pair.id,
                    title: pair.title,
                    description: pair.description,
                    category: pair.category,
                    type: 'before-after',
                    src: pair.afterImage.url, // Use after image as main src
                    tags: pair.tags || [],
                    featured: pair.isFeatured || false,
                    beforeImage: pair.beforeImage,
                    afterImage: pair.afterImage,
                    order_index: pair.orderIndex,
                    created_at: pair.createdAt
                }));

                const transformedAlbum = {
                    ...albumData.album,
                    images: [...transformedImages, ...beforeAfterItems].sort((a, b) => {
                        // Sort by order_index, then by created_at
                        if (a.order_index !== b.order_index) {
                            return (a.order_index || 0) - (b.order_index || 0);
                        }
                        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
                    })
                };

                setAlbum(transformedAlbum);
            } catch (err) {
                console.error('Error fetching album:', err);
                setError('Failed to load album');
            } finally {
                setIsLoading(false);
            }
        };

        fetchAlbum();
    }, [params]);

    // Lightbox functions
    const openLightbox = (image: AlbumImage) => {
        setSelectedImage(image);
        setIsLightboxOpen(true);
    };

    const closeLightbox = () => {
        setIsLightboxOpen(false);
        setSelectedImage(null);
    };

    const handlePrevious = () => {
        if (!album?.images || !selectedImage) return;
        const currentIndex = album.images.findIndex(img => img.id === selectedImage.id);
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : album.images.length - 1;
        setSelectedImage(album.images[prevIndex]);
    };

    const handleNext = () => {
        if (!album?.images || !selectedImage) return;
        const currentIndex = album.images.findIndex(img => img.id === selectedImage.id);
        const nextIndex = currentIndex < album.images.length - 1 ? currentIndex + 1 : 0;
        setSelectedImage(album.images[nextIndex]);
    };

    // Loading state
    if (isLoading) {
        return (
            <div className="min-h-screen bg-white text-black">
                <Header forceVisible={true} />
                <div className="pt-20 flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <Loader2 className="h-16 w-16 text-black mx-auto animate-spin mb-6" />
                        <h3 className="gilroy-text-bold text-2xl font-black text-black mb-3 uppercase tracking-tight">
                            Loading Album...
                        </h3>
                    </div>
                </div>
            </div>
        );
    }

    // Error state
    if (error || !album) {
        return (
            <div className="min-h-screen bg-white text-black">
                <Header forceVisible={true} />
                <div className="pt-20 flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <h3 className="gilroy-text-bold text-2xl font-black text-black mb-3 uppercase tracking-tight">
                            {error || 'Album Not Found'}
                        </h3>
                        <p className="gilroy-text text-black mb-6">
                            The album you're looking for doesn't exist or isn't available.
                        </p>
                        <Link
                            href="/gallery"
                            className="bg-black text-white px-6 py-3 gilroy-text-bold font-bold uppercase tracking-wider hover:bg-white hover:text-black border border-black transition-colors"
                        >
                            Back to Gallery
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-white text-black">
            <Header forceVisible={true} />

            {/* Album Header */}
            <section className="relative pt-20 md:pt-24 lg:pt-28 pb-16 md:pb-20 bg-black text-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Breadcrumb */}
                    <div className="mb-8 md:mb-12">
                        <nav className="flex items-center space-x-2 text-sm">
                            <Link href="/" className="text-white hover:text-gray-300 transition-colors">
                                Home
                            </Link>
                            <span className="text-gray-500">/</span>
                            <Link href="/gallery" className="text-white hover:text-gray-300 transition-colors">
                                Gallery
                            </Link>
                            <span className="text-gray-500">/</span>
                            <span className="text-gray-300">{album.title}</span>
                        </nav>
                    </div>

                    {/* Back Button */}
                    <div className="mb-8">
                        <Link
                            href="/gallery"
                            className="inline-flex items-center text-white hover:text-gray-300 transition-colors group"
                        >
                            <ArrowLeft size={20} className="mr-2 group-hover:-translate-x-1 transition-transform" />
                            <span className="gilroy-text-bold font-bold uppercase tracking-wider">Back to Gallery</span>
                        </Link>
                    </div>

                    {/* Album Title */}
                    <motion.div
                        className="text-center"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                    >
                        <h1 className="gilroy-text-bold text-4xl md:text-5xl lg:text-6xl font-black uppercase tracking-tight mb-6">
                            {album.title}
                        </h1>

                        {album.description && (
                            <p className="gilroy-text text-lg md:text-xl text-white max-w-3xl mx-auto leading-relaxed mb-8">
                                {album.description}
                            </p>
                        )}

                        {/* Image Count */}
                        <div className="inline-flex items-center bg-white/10 px-4 py-2 rounded-lg">
                            <span className="gilroy-text-bold text-sm font-bold text-white">
                                {album.images?.length || 0} Images
                            </span>
                        </div>
                    </motion.div>
                </div>
            </section>

            {/* Album Images */}
            <section className="py-16 md:py-20 lg:py-24">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {album.images && album.images.length > 0 ? (
                        <div className="grid gap-8 md:gap-10 lg:gap-12 grid-cols-1 md:grid-cols-2">
                            {album.images.map((image, index) => (
                                <motion.div
                                    key={image.id}
                                    initial={{ opacity: 0, y: 40, scale: 0.95 }}
                                    animate={{ opacity: 1, y: 0, scale: 1 }}
                                    transition={{
                                        duration: 0.6,
                                        delay: index * 0.08,
                                        type: "spring",
                                        stiffness: 180,
                                        damping: 25
                                    }}
                                    whileHover={{
                                        y: -8,
                                        scale: 1.02,
                                        transition: { duration: 0.4, type: "spring", stiffness: 220, damping: 28 }
                                    }}
                                    whileTap={{ scale: 0.98 }}
                                    className="group relative overflow-hidden rounded-xl bg-black cursor-pointer touch-manipulation shadow-2xl hover:shadow-3xl transition-all duration-700 ease-out border border-gray-100/20"
                                    onClick={() => openLightbox(image)}
                                >
                                    {/* Image */}
                                    <div className="relative aspect-[3/2] overflow-hidden">
                                        {image.type === 'before-after' && image.beforeImage && image.afterImage ? (
                                            <BeforeAfterSlider
                                                beforeImage={image.beforeImage.url}
                                                afterImage={image.afterImage.url}
                                                title=""
                                                beforeAlt={image.beforeImage.alt}
                                                afterAlt={image.afterImage.alt}
                                                className="h-full border-0 shadow-none bg-transparent rounded-xl overflow-hidden"
                                                showTitle={false}
                                            />
                                        ) : (
                                            <>
                                                <Image
                                                    src={image.src}
                                                    alt={image.title}
                                                    fill
                                                    className="object-cover select-none pointer-events-none transition-all duration-700 ease-out group-hover:scale-110 group-hover:brightness-110"
                                                    draggable={false}
                                                    onContextMenu={(e) => e.preventDefault()}
                                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                                    priority={index < 6}
                                                />

                                                {/* Overlay */}
                                                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out" />
                                            </>
                                        )}
                                    </div>

                                    {/* Content Overlay */}
                                    <div className="absolute inset-0 flex flex-col justify-end p-6 lg:p-8 text-white opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out transform translate-y-4 group-hover:translate-y-0">
                                        <h3 className="gilroy-text-bold text-xl lg:text-2xl font-black uppercase tracking-tight mb-3 leading-tight">
                                            {image.title}
                                        </h3>
                                        {image.description && (
                                            <p className="gilroy-text text-sm lg:text-base text-white mb-4 line-clamp-3 leading-relaxed">
                                                {image.description}
                                            </p>
                                        )}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-16 md:py-20">
                            <h3 className="gilroy-text-bold text-2xl md:text-3xl font-black uppercase tracking-tight mb-4 text-black">
                                No Images Yet
                            </h3>
                            <p className="gilroy-text text-black mb-8 max-w-md mx-auto">
                                This album doesn't have any images yet. Check back soon!
                            </p>
                        </div>
                    )}
                </div>
            </section>

            {/* Lightbox - Simple version */}
            <AnimatePresence>
                {isLightboxOpen && selectedImage && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 z-50 bg-black/95 backdrop-blur-sm"
                        onClick={closeLightbox}
                    >
                        {/* Close Button */}
                        <button
                            onClick={closeLightbox}
                            className="absolute top-4 right-4 z-60 bg-black/80 hover:bg-black text-white p-3 rounded-full"
                        >
                            <X size={20} />
                        </button>

                        {/* Navigation */}
                        {album.images && album.images.length > 1 && (
                            <>
                                <button
                                    onClick={(e) => { e.stopPropagation(); handlePrevious(); }}
                                    className="absolute left-4 top-1/2 -translate-y-1/2 z-60 bg-black/80 hover:bg-black text-white p-3 rounded-full"
                                >
                                    <ArrowLeft size={20} />
                                </button>
                                <button
                                    onClick={(e) => { e.stopPropagation(); handleNext(); }}
                                    className="absolute right-4 top-1/2 -translate-y-1/2 z-60 bg-black/80 hover:bg-black text-white p-3 rounded-full"
                                >
                                    <ArrowLeft size={20} className="rotate-180" />
                                </button>
                            </>
                        )}

                        {/* Image */}
                        <div className="flex items-center justify-center min-h-screen p-4 md:p-8">
                            <motion.div
                                key={selectedImage.id}
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.8, opacity: 0 }}
                                className="max-w-7xl w-full"
                                onClick={(e) => e.stopPropagation()}
                            >
                                {selectedImage.type === 'before-after' && selectedImage.beforeImage && selectedImage.afterImage ? (
                                    <BeforeAfterSlider
                                        beforeImage={selectedImage.beforeImage.url}
                                        afterImage={selectedImage.afterImage.url}
                                        title={selectedImage.title}
                                        beforeAlt={selectedImage.beforeImage.alt}
                                        afterAlt={selectedImage.afterImage.alt}
                                        className="max-h-[85vh] rounded-lg w-full"
                                        onDragStart={() => {/* Prevent navigation interference */ }}
                                        onDragEnd={() => {/* Prevent navigation interference */ }}
                                    />
                                ) : (
                                    <Image
                                        src={selectedImage.src}
                                        alt={selectedImage.title}
                                        width={1200}
                                        height={800}
                                        className="w-full h-auto max-h-[85vh] object-contain rounded-lg"
                                    />
                                )}
                                <div className="text-center mt-4 text-white">
                                    <h3 className="gilroy-text-bold text-xl font-black uppercase mb-2">
                                        {selectedImage.title}
                                    </h3>
                                    {selectedImage.description && (
                                        <p className="gilroy-text text-gray-300">
                                            {selectedImage.description}
                                        </p>
                                    )}
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            <JCCStickyFooter />
        </div>
    );
} 