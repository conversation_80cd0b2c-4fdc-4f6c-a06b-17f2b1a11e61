import { redirect } from 'next/navigation'
import { createServerSupabaseClient } from '@/lib/supabase'
import BlogArticleForm from '@/components/admin/BlogArticleForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

interface BlogEditPageProps {
    params: {
        id: string
    }
}

export default async function BlogEditPage({ params }: BlogEditPageProps) {
    const supabase = await createServerSupabaseClient()

    // Fetch the blog article
    const { data: article, error } = await supabase
        .from('blog_articles')
        .select('*')
        .eq('id', params.id)
        .single()

    if (error || !article) {
        redirect('/admin/blog')
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-4xl mx-auto p-6">
                {/* Header */}
                <div className="mb-6">
                    <Link href="/admin/blog">
                        <Button variant="ghost" className="mb-4 gilroy-text">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Blog
                        </Button>
                    </Link>
                    <h1 className="text-3xl font-bold text-gray-900 gilroy-text-bold">
                        Edit Article: {article.title}
                    </h1>
                    <p className="text-gray-600 mt-2 gilroy-text">
                        Modify your blog article content and settings
                    </p>
                </div>

                {/* Article Form */}
                <Card>
                    <CardContent className="p-6">
                        <BlogArticleForm article={article} isEditing={true} />
                    </CardContent>
                </Card>
            </div>
        </div>
    )
} 