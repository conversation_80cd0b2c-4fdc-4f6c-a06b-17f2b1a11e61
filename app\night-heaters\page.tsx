'use client';

import { useState, useEffect } from 'react';


import Image from 'next/image';
import Link from 'next/link';
import { Thermometer, Battery, Clock, Shield, Zap, Snowflake, CheckCircle, ArrowRight, Settings } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import JCCStickyFooter from '@/components/footer';
import Header from '@/components/header';

// Heater specifications
const heaterSpecs = [
  {
    model: 'Autoterm Air 2D',
    power: '2kW',
    fuelConsumption: '0.1-0.24 L/h',
    voltage: '12V',
    weight: '2.5kg',
    price: '£990',
    features: ['Compact design', 'Perfect for small vans', 'Ultra quiet operation'],
    popular: false
  },
  {
    model: 'Autoterm Air 4D',
    power: '4kW',
    fuelConsumption: '0.1-0.51 L/h',
    voltage: '12V/24V',
    weight: '5.5kg',
    price: '£1,290',
    features: ['Most popular choice', 'Ideal for large vans', 'Fast heating capability'],
    popular: true
  },
  {
    model: 'Autoterm Air 8D',
    power: '8kW',
    fuelConsumption: '0.19-0.96 L/h',
    voltage: '24V',
    weight: '7.5kg',
    price: '£1,590',
    features: ['Maximum power', 'Commercial vehicles', 'Extreme conditions'],
    popular: false
  }
];

// Installation benefits
const installationBenefits = [
  {
    icon: Clock,
    title: 'Same Day Fitting',
    description: 'Professional installation completed in one day for most vehicles',
    detail: 'No overnight stays required'
  },
  {
    icon: Shield,
    title: '3 Year Guarantee',
    description: 'Comprehensive warranty covering parts and installation workmanship',
    detail: 'Peace of mind included'
  },
  {
    icon: Settings,
    title: 'Autoterm Dealer',
    description: 'Authorized dealer with certified technicians and genuine parts',
    detail: 'Factory trained specialists'
  },
  {
    icon: CheckCircle,
    title: 'EU Safety Standards',
    description: 'All installations meet European safety regulations and standards',
    detail: 'Certified safe operation'
  }
];

// Temperature comparison component
const TemperatureComparison = () => {
  const [activeTab, setActiveTab] = useState<'without' | 'with'>('without');
  
  return (
    <div className="bg-black text-white p-8 rounded-lg">
      <h3 className="gilroy-text-bold text-2xl font-bold mb-6 text-center text-white">Winter Comfort Comparison</h3>
      
      <div className="flex justify-center mb-8">
        <div className="bg-white/10 p-1 rounded-full">
          <button
            onClick={() => setActiveTab('without')}
            className={`px-6 py-2 rounded-full transition-all duration-300 ${
              activeTab === 'without' 
                ? 'bg-white text-black font-bold' 
                : 'text-white hover:bg-white/20'
            }`}
          >
            <span className="gilroy-text-bold text-sm uppercase tracking-wider">Without Heater</span>
          </button>
          <button
            onClick={() => setActiveTab('with')}
            className={`px-6 py-2 rounded-full transition-all duration-300 ${
              activeTab === 'with' 
                ? 'bg-white text-black font-bold' 
                : 'text-white hover:bg-white/20'
            }`}
          >
            <span className="gilroy-text-bold text-sm uppercase tracking-wider">With Heater</span>
          </button>
        </div>
      </div>
      
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {activeTab === 'without' ? (
            <>
              <div className="text-center">
                <Snowflake size={48} className="mx-auto mb-4 text-blue-300" />
                <h4 className="gilroy-text-bold text-xl font-bold mb-2 text-white">Interior Temperature</h4>
                <p className="gilroy-text-bold text-3xl font-black text-blue-300">2°C</p>
                <p className="gilroy-text text-sm text-gray-400">Freezing cold nights</p>
              </div>
              <div className="text-center">
                <Battery size={48} className="mx-auto mb-4 text-red-300" />
                <h4 className="gilroy-text-bold text-xl font-bold mb-2 text-white">Battery Drain</h4>
                <p className="gilroy-text-bold text-3xl font-black text-red-300">High</p>
                <p className="gilroy-text text-sm text-gray-400">Running engine for heat</p>
              </div>
            </>
          ) : (
            <>
              <div className="text-center">
                <Thermometer size={48} className="mx-auto mb-4 text-white" />
                <h4 className="gilroy-text-bold text-xl font-bold mb-2 text-white">Interior Temperature</h4>
                <p className="gilroy-text-bold text-3xl font-black text-white">22°C</p>
                <p className="gilroy-text text-sm text-gray-400">Perfectly comfortable</p>
              </div>
              <div className="text-center">
                <Zap size={48} className="mx-auto mb-4 text-green-300" />
                <h4 className="gilroy-text-bold text-xl font-bold mb-2 text-white">Fuel Efficiency</h4>
                <p className="gilroy-text-bold text-3xl font-black text-green-300">0.2L/h</p>
                <p className="gilroy-text text-sm text-gray-400">Minimal fuel consumption</p>
              </div>
            </>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default function NightHeatersPage() {
  const [selectedHeater, setSelectedHeater] = useState(1); // Default to popular choice

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      <Header onContactClick={() => scrollToSection('contact')} />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/winter-mountain.png"
            alt="Night Heater Unit"
            fill
            className="object-cover"
            priority
          />
        </div>
        
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black/60 z-5"></div>
        
                 {/* Snowflake pattern overlay */}
         <div className="absolute inset-0 opacity-10 z-10">
           {Array.from({ length: 20 }).map((_, i) => {
             // Use deterministic values based on index to avoid hydration mismatch
             const left = (i * 37 + 13) % 100; // Deterministic left position
             const top = (i * 23 + 7) % 100;   // Deterministic top position
             const fontSize = 10 + (i % 20);   // Deterministic font size
             const duration = 3 + (i % 3);     // Deterministic duration
             const delay = i * 0.2;            // Deterministic delay
             
             return (
               <motion.div
                 key={i}
                 className="absolute text-white"
                 style={{
                   left: `${left}%`,
                   top: `${top}%`,
                   fontSize: `${fontSize}px`
                 }}
                 animate={{
                   y: [0, -20, 0],
                   rotate: [0, 360],
                 }}
                 transition={{
                   duration: duration,
                   repeat: Infinity,
                   delay: delay
                 }}
               >
                 ❄
               </motion.div>
             );
           })}
         </div>
        
        <div className="relative z-20 text-center max-w-6xl mx-auto px-4">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >

            
            <h1 className="gilroy-text-bold text-6xl md:text-8xl lg:text-9xl font-black uppercase tracking-tight text-white mb-6 leading-none">
              Night
              <br />
              <span className="text-gray-400">Heaters</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </motion.div>
           
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="gilroy-text text-xl md:text-2xl font-light mb-6 max-w-4xl mx-auto leading-relaxed text-gray-300"
          >
            Stay warm all winter long with professional heating systems
          </motion.p>
           
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="gilroy-text text-lg md:text-xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
          >
            Professional installation of Autoterm night heaters - sleep comfortably in any weather.
          </motion.p>
           
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center"
          >
            <button 
              onClick={() => scrollToSection('comparison')}
              className="gilroy-text-bold bg-white text-black px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See Comparison</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See Comparison
              </span>
            </button>
            <Link 
              href="/#contact"
              className="gilroy-text-bold border-2 border-white text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Quote</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Quote
              </span>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Temperature Comparison Section */}
      <section className="py-32 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black">
              Why Night Heaters?
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the difference proper heating makes to your adventures
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <TemperatureComparison />
          </div>
        </div>
      </section>

      {/* Heater Selection */}
      <section id="heaters" className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black">
              Choose Your Heater
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Professional Autoterm heaters for every van size and heating need
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {heaterSpecs.map((heater, index) => (
              <motion.div
                key={index}
                className={`relative bg-white border-2 transition-all duration-300 cursor-pointer ${
                  heater.popular 
                    ? 'border-black shadow-2xl transform scale-105' 
                    : 'border-gray-200 hover:border-gray-400 hover:shadow-lg'
                } ${selectedHeater === index ? 'ring-4 ring-black/20' : ''}`}
                onClick={() => setSelectedHeater(index)}
                whileHover={{ y: -5 }}
                whileTap={{ scale: 0.98 }}
              >
                {heater.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-black text-white px-6 py-2 rounded-full">
                    <span className="gilroy-text-bold text-sm font-bold uppercase tracking-wider">Most Popular</span>
                  </div>
                )}
                
                <div className="p-8">
                  <div className="text-center mb-6">
                    <h3 className="gilroy-text-bold text-2xl font-bold mb-2 text-black uppercase tracking-wider">
                      {heater.model}
                    </h3>
                                         <div className="gilroy-text-bold text-4xl font-black text-black mb-2">
                       {heater.price}
                     </div>
                    <div className="gilroy-text text-sm text-gray-500 uppercase tracking-wider">
                      Including Installation
                    </div>
                  </div>

                  <div className="space-y-2 mb-6">
                    <div className="flex justify-between items-center">
                      <span className="gilroy-text text-gray-600">Power Output:</span>
                      <span className="gilroy-text-bold text-black font-semibold">{heater.power}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="gilroy-text text-gray-600">Fuel Use:</span>
                      <span className="gilroy-text-bold text-black font-semibold">{heater.fuelConsumption}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="gilroy-text text-gray-600">Voltage:</span>
                      <span className="gilroy-text-bold text-black font-semibold">{heater.voltage}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="gilroy-text text-gray-600">Weight:</span>
                      <span className="gilroy-text-bold text-black font-semibold">{heater.weight}</span>
                    </div>
                  </div>
                
                <div className="mb-6">
                  <h4 className="gilroy-text-bold text-sm font-bold text-black uppercase tracking-wider">Perfect For:</h4>
                  <ul className="mt-2 space-y-1">
                    {heater.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <CheckCircle size={14} className="text-black" />
                        <span className="gilroy-text text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <button className="gilroy-text-bold w-full bg-black text-white py-3 px-6 font-bold uppercase tracking-wider relative overflow-hidden group">
                  <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Choose This Model</span>
                  <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                  <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                    Choose This Model
                  </span>
                </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Installation Benefits */}
      <section className="py-32 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-white">
              Installation Benefits
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Professional installation with comprehensive support and warranty
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {installationBenefits.map((benefit, index) => (
              <motion.div
                key={index}
                className="bg-white text-black p-8 hover:transform hover:scale-105 transition-all duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center flex-shrink-0">
                    <benefit.icon size={32} />
                  </div>
                  <div className="flex-1">
                    <h3 className="gilroy-text-bold text-2xl font-bold mb-2 text-black uppercase tracking-wider">
                      {benefit.title}
                    </h3>
                    <p className="gilroy-text text-gray-600 mb-2 leading-relaxed">
                      {benefit.description}
                    </p>
                    <p className="gilroy-text-bold text-sm text-black font-semibold">
                      {benefit.detail}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="mt-20 text-center bg-white/10 py-16 px-8 rounded-lg">
            <h3 className="gilroy-text-bold text-3xl md:text-4xl font-black uppercase tracking-tight mb-6 text-white">
              Ready For Winter?
            </h3>
            <div className="w-20 h-1 bg-white mx-auto mb-8"></div>
            <p className="gilroy-text text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              Get your night heater installed before winter arrives. Book your fitting today.
            </p>
            <Link 
              href="/#contact"
              className="gilroy-text-bold inline-flex items-center space-x-2 bg-white text-black px-8 py-4 font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Book Installation</span>
              <ArrowRight size={20} className="relative z-10 group-hover:opacity-0 transition-opacity duration-300" />
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Book Installation
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="py-32 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Left Content */}
            <div>
              <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black leading-none">
                Complete Installation
                <br />
                Service
              </h2>
              <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
              
              <div className="space-y-6 mb-10">
                <p className="gilroy-text text-lg text-gray-700 leading-relaxed">
                  We don't just sell heaters - we provide complete installation service with everything you need for safe, reliable heating. Our certified technicians handle every aspect of the installation process.
                </p>
                
                <p className="gilroy-text text-lg text-gray-700 leading-relaxed">
                  From initial consultation to final testing, we ensure your heater is installed correctly and safely, meeting all European safety standards.
                </p>
              </div>
              
              <div className="bg-black text-white p-6 rounded-lg mb-10">
                <h3 className="gilroy-text-bold text-xl font-bold mb-4 text-white uppercase tracking-wider">Why Autoterm?</h3>
                <ul className="space-y-2">
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>Market leading reliability</span>
                  </li>
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>Proven in extreme conditions</span>
                  </li>
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>Ultra-low fuel consumption</span>
                  </li>
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>Whisper quiet operation</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Right Content - Specifications */}
            <div className="space-y-8">
              <div className="bg-white p-8 border-2 border-black">
                <h3 className="gilroy-text-bold text-2xl font-bold mb-6 text-black uppercase tracking-wider">Installation Includes</h3>
                <ul className="space-y-3">
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Complete heater unit and controls</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Professional ducting and exhaust system</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Fuel tank and supply lines</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Electrical wiring and control panel</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Remote control and timer</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Safety systems and sensors</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Full system testing and commissioning</span>
                  </li>
                  <li className="gilroy-text text-gray-700 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-black" />
                    <span>Operating instructions and warranty</span>
                  </li>
                </ul>
              </div>

              <div className="bg-black text-white p-8">
                <h3 className="gilroy-text-bold text-2xl font-bold mb-6 text-white uppercase tracking-wider">Operating Costs</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="gilroy-text text-gray-300">Fuel consumption (2kW):</span>
                    <span className="gilroy-text-bold text-white font-semibold">0.1-0.24 L/h</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="gilroy-text text-gray-300">8 hours heating cost:</span>
                    <span className="gilroy-text-bold text-white font-semibold">£2.50-£4.00</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="gilroy-text text-gray-300">Electrical consumption:</span>
                    <span className="gilroy-text-bold text-white font-semibold">10-29W</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="gilroy-text text-gray-300">Noise level:</span>
                    <span className="gilroy-text-bold text-white font-semibold">&lt;45dB</span>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 border-2 border-gray-200">
                <h3 className="gilroy-text-bold text-2xl font-bold mb-6 text-black uppercase tracking-wider">Safety Features</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Shield size={20} className="text-black" />
                    <span className="gilroy-text text-sm text-gray-700">Overheat protection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield size={20} className="text-black" />
                    <span className="gilroy-text text-sm text-gray-700">Flame failure detection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield size={20} className="text-black" />
                    <span className="gilroy-text text-sm text-gray-700">Low voltage protection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield size={20} className="text-black" />
                    <span className="gilroy-text text-sm text-gray-700">Automatic shutdown</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="gilroy-text-bold text-4xl md:text-5xl font-black uppercase tracking-tight mb-8 text-black">
            Ready For Cozy Nights?
          </h2>
          <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
          <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Don't let cold weather stop your adventures. Get professional night heater installation today.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link 
              href="/#contact"
              className="gilroy-text-bold bg-black text-white px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Book Installation</span>
              <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Book Installation
              </span>
            </Link>
            <Link 
              href="/gallery"
              className="gilroy-text-bold border-2 border-black text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See Examples</span>
              <div className="absolute inset-0 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See Examples
              </span>
            </Link>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
} 