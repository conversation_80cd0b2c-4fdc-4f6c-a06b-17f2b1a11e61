'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import TiptapLink from '@tiptap/extension-link'
import ImageResize from 'tiptap-extension-resize-image'
import TextAlign from '@tiptap/extension-text-align'
import Heading from '@tiptap/extension-heading'
import Placeholder from '@tiptap/extension-placeholder'
import { Bold, Italic, List, ListOrdered, Link, Save, Send, ArrowLeft, Plus, X, Heading1, Heading2, Heading3, AlignLeft, AlignCenter, AlignRight, ImageIcon, Quote, Code, Undo, Redo, Upload } from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { blogArticleSchema, blogArticlePublishSchema, type BlogArticleInput } from '@/lib/validations'
import { invalidateBlogCache } from '@/lib/cache-utils'
import MediaGalleryPicker from './MediaGalleryPicker'
import BlogImageUpload from './BlogImageUpload'

interface BlogArticleFormProps {
  article?: any
  isEditing?: boolean
}

export default function BlogArticleForm({ article, isEditing = false }: BlogArticleFormProps) {
  const router = useRouter()
  const supabase = createClient()

  const [tagInput, setTagInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showMediaPicker, setShowMediaPicker] = useState(false)
  const [showImageUpload, setShowImageUpload] = useState(false)
  const [showFeaturedMediaPicker, setShowFeaturedMediaPicker] = useState(false)
  const [showFeaturedImageUpload, setShowFeaturedImageUpload] = useState(false)
  const [showHeroMediaPicker, setShowHeroMediaPicker] = useState(false)
  const [showHeroImageUpload, setShowHeroImageUpload] = useState(false)

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<BlogArticleInput>({
    resolver: zodResolver(blogArticleSchema) as any,
    defaultValues: {
      title: article?.title || '',
      slug: article?.slug || '',
      excerpt: article?.excerpt || '',
      content: '',
      contentHtml: '',
      featuredImage: article?.featured_image || '',
      heroImage: article?.hero_image || '',
      category: article?.category || 'general',
      tags: article?.tags || [],
      status: article?.status || 'draft',
      publishedAt: article?.published_at || '',
      seoTitle: article?.meta_title || '',
      seoDescription: article?.meta_description || '',
    }
  })

  const watchedData = watch()

  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        heading: false, // Disable StarterKit's heading to use our custom one
      }),
      TiptapLink.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      ImageResize,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Heading.configure({
        levels: [1, 2, 3],
        HTMLAttributes: {
          class: 'font-bold',
        },
      }),
      Placeholder.configure({
        placeholder: ({ node }) => {
          if (node.type.name === 'heading') {
            return 'Heading...'
          }
          return 'Start writing your article content here. Use the toolbar above to format text, add images, and structure your content. This editor shows exactly how your article will appear when published.'
        },
      }),
    ],
    content: article?.content || '',
    editorProps: {
      attributes: {
        class: 'focus:outline-none',
      },
    },
  })

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const onSubmit = async (data: BlogArticleInput, saveAs: 'draft' | 'published' = 'draft') => {
    console.log('onSubmit called with:', { data, saveAs })
    setIsLoading(true)
    setError('')

    try {
      const content = editor?.getHTML() || ''
      const slug = data.slug || generateSlug(data.title)

      console.log('Editor content length:', content.length)
      console.log('Editor content:', content)
      console.log('Form data processed:', { content: content.slice(0, 100), slug })

      // Prepare the complete data object
      const completeData = {
        ...data,
        content,
        contentHtml: content,
        slug
      }

      // Validate with appropriate schema
      const schema = saveAs === 'published' ? blogArticlePublishSchema : blogArticleSchema
      const validationResult = schema.safeParse(completeData)

      if (!validationResult.success) {
        console.error('Validation failed:', validationResult.error.issues)
        const errorMessages = validationResult.error.issues.map((e: any) => e.message)

        if (saveAs === 'published') {
          setError(`Cannot publish article - please fix the following:\n• ${errorMessages.join('\n• ')}`)
        } else {
          setError(`Cannot save article:\n• ${errorMessages.join('\n• ')}`)
        }
        return
      }

      console.log('Validation passed')

      // Update form data with editor content
      setValue('content', content)
      setValue('contentHtml', content)

      const articleData = {
        title: data.title,
        slug,
        excerpt: data.excerpt,
        content,
        content_html: content,
        featured_image: data.featuredImage,
        hero_image: data.heroImage,
        category: data.category || 'general',
        tags: data.tags,
        status: saveAs,
        seo_title: data.seoTitle,
        seo_description: data.seoDescription,
        ...(saveAs === 'published' && { published_at: new Date().toISOString() })
      }

      console.log('About to save article:', { articleData, isEditing, articleId: article?.id })

      if (isEditing && article?.id) {
        console.log('Updating existing article')
        const { data: result, error } = await supabase
          .from('blog_articles')
          .update(articleData)
          .eq('id', article.id)
          .select()

        if (error) {
          console.error('Update error:', error)
          throw error
        }
        console.log('Update successful:', result)
      } else {
        console.log('Creating new article')
        const { data: result, error } = await supabase
          .from('blog_articles')
          .insert([articleData])
          .select()

        if (error) {
          console.error('Insert error:', error)
          throw error
        }
        console.log('Insert successful:', result)
      }

      // Invalidate blog cache after successful operation
      console.log('Invalidating blog cache...')
      await invalidateBlogCache()

      console.log('Redirecting to blog admin...')
      router.push('/admin/blog')
      router.refresh()
    } catch (err: any) {
      console.error('Error in onSubmit:', err)
      setError(err.message || 'An error occurred')
    } finally {
      console.log('onSubmit finished')
      setIsLoading(false)
    }
  }

  const handleTitleChange = (value: string) => {
    setValue('title', value)
    // Auto-generate slug from title if slug is empty
    if (!watchedData.slug) {
      setValue('slug', generateSlug(value))
    }
  }

  const addTag = () => {
    if (tagInput.trim() && !watchedData.tags?.includes(tagInput.trim())) {
      const newTags = [...(watchedData.tags || []), tagInput.trim()]
      setValue('tags', newTags)
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    const newTags = (watchedData.tags || []).filter((tag: string) => tag !== tagToRemove)
    setValue('tags', newTags)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  const handleImageSelect = (image: any) => {
    editor?.chain().focus().setImage({
      src: image.image_url,
      alt: image.alt_text || image.title,
      title: image.title
    }).run()
  }

  const handleImageUpload = (imageData: { url: string; alt: string; title: string }) => {
    editor?.chain().focus().setImage({
      src: imageData.url,
      alt: imageData.alt,
      title: imageData.title
    }).run()
  }

  const handleFeaturedImageSelect = (image: any) => {
    setValue('featuredImage', image.image_url)
  }

  const handleFeaturedImageUpload = (imageData: { url: string; alt: string; title: string }) => {
    setValue('featuredImage', imageData.url)
  }

  const handleHeroImageSelect = (image: any) => {
    setValue('heroImage', image.image_url)
  }

  const handleHeroImageUpload = (imageData: { url: string; alt: string; title: string }) => {
    setValue('heroImage', imageData.url)
  }

  // Helper functions for validation status
  const getContentLength = () => {
    const content = editor?.getHTML() || ''
    // Remove HTML tags for character count
    const textContent = content.replace(/<[^>]*>/g, '').trim()
    return textContent.length
  }

  const isReadyToPublish = () => {
    const contentLength = getContentLength()
    const excerptLength = (watchedData.excerpt || '').length
    const hasTitle = (watchedData.title || '').length >= 5

    return hasTitle && contentLength >= 100 && excerptLength >= 50
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="gilroy-text"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Blog Articles
      </Button>

      <form onSubmit={handleFormSubmit((data) => onSubmit(data as unknown as BlogArticleInput, 'draft'))} className="space-y-6">
        {/* Publishing Requirements Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="gilroy-text-medium text-blue-900 mb-2">📝 Publishing Requirements</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <div className="flex items-center gap-2">
              <span className={`text-xs ${(watchedData.title || '').length >= 5 ? 'text-green-600' : 'text-gray-500'}`}>
                {(watchedData.title || '').length >= 5 ? '✓' : '○'}
              </span>
              <span>Title: {(watchedData.title || '').length >= 5 ? 'Ready' : `${(watchedData.title || '').length}/5 characters`}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs ${getContentLength() >= 100 ? 'text-green-600' : 'text-gray-500'}`}>
                {getContentLength() >= 100 ? '✓' : '○'}
              </span>
              <span>Content: {getContentLength() >= 100 ? 'Ready' : `${getContentLength()}/100 characters`}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs ${(watchedData.excerpt || '').length >= 50 ? 'text-green-600' : 'text-gray-500'}`}>
                {(watchedData.excerpt || '').length >= 50 ? '✓' : '○'}
              </span>
              <span>Excerpt: {(watchedData.excerpt || '').length >= 50 ? 'Ready' : `${(watchedData.excerpt || '').length}/50 characters`}</span>
            </div>
          </div>
          <p className="text-xs text-blue-700 mt-2">
            {isReadyToPublish() ? '🎉 Ready to publish!' : 'Complete the requirements above to publish your article. You can save as draft anytime.'}
          </p>
        </div>

        {/* Article Details */}
        <Card>
          <CardHeader>
            <CardTitle className="gilroy-text-bold">Article Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex justify-between items-center">
                  <Label htmlFor="title" className="gilroy-text-medium">Article Title *</Label>
                  <span className={`text-xs ${(watchedData.title || '').length >= 5 ? 'text-green-600' : 'text-gray-500'}`}>
                    {(watchedData.title || '').length}/5 characters
                    {(watchedData.title || '').length >= 5 && ' ✓'}
                  </span>
                </div>
                <Input
                  id="title"
                  {...register('title')}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter article title (minimum 5 characters for publishing)"
                  className={`gilroy-text ${errors.title ? 'border-red-500' : (watchedData.title || '').length >= 5 ? 'border-green-500' : ''}`}
                />
                {errors.title && (
                  <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                )}
                {(watchedData.title || '').length > 0 && (watchedData.title || '').length < 5 && (
                  <p className="text-sm text-amber-600 mt-1">
                    Need {5 - (watchedData.title || '').length} more characters to publish
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="slug" className="gilroy-text-medium">URL Slug *</Label>
                <Input
                  id="slug"
                  {...register('slug')}
                  placeholder="article-url-slug"
                  className={`gilroy-text ${errors.slug ? 'border-red-500' : ''}`}
                />
                {errors.slug && (
                  <p className="text-sm text-red-600 mt-1">{errors.slug.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="category" className="gilroy-text-medium">Category *</Label>
                <Select
                  value={watchedData.category || 'general'}
                  onValueChange={(value: string) => setValue('category', value)}
                >
                  <SelectTrigger className={`gilroy-text ${errors.category ? 'border-red-500' : ''}`}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="services">Services</SelectItem>
                    <SelectItem value="tips">Tips & Advice</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="restoration">Restoration</SelectItem>
                    <SelectItem value="news">Company News</SelectItem>
                    <SelectItem value="diy">DIY Guides</SelectItem>
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-600 mt-1">{errors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center">
                <Label htmlFor="excerpt" className="gilroy-text-medium">Article Excerpt</Label>
                <span className={`text-xs ${(watchedData.excerpt || '').length >= 50 ? 'text-green-600' : 'text-gray-500'}`}>
                  {(watchedData.excerpt || '').length}/50 characters
                  {(watchedData.excerpt || '').length >= 50 && ' ✓'}
                </span>
              </div>
              <Textarea
                id="excerpt"
                {...register('excerpt')}
                placeholder="Brief description of the article (required for publishing - minimum 50 characters)"
                rows={3}
                className={`gilroy-text ${errors.excerpt ? 'border-red-500' : (watchedData.excerpt || '').length >= 50 ? 'border-green-500' : ''}`}
              />
              {errors.excerpt && (
                <p className="text-sm text-red-600 mt-1">{errors.excerpt.message}</p>
              )}
              {(watchedData.excerpt || '').length > 0 && (watchedData.excerpt || '').length < 50 && (
                <p className="text-sm text-amber-600 mt-1">
                  Need {50 - (watchedData.excerpt || '').length} more characters to publish
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status" className="gilroy-text-medium">Status</Label>
                <Select
                  value={watchedData.status}
                  onValueChange={(value: 'draft' | 'published' | 'archived') =>
                    setValue('status', value)
                  }
                >
                  <SelectTrigger className={`gilroy-text ${errors.status ? 'border-red-500' : ''}`}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-600 mt-1">{errors.status.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="featured_image" className="gilroy-text-medium">Featured Image</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      id="featured_image"
                      {...register('featuredImage')}
                      placeholder="https://example.com/image.jpg"
                      className={`gilroy-text flex-1 ${errors.featuredImage ? 'border-red-500' : ''}`}
                    />
                    <div className="flex gap-1">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowFeaturedMediaPicker(true)}
                        className="whitespace-nowrap"
                        title="Select from Gallery"
                      >
                        <ImageIcon className="h-4 w-4" />
                        Gallery
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowFeaturedImageUpload(true)}
                        className="whitespace-nowrap"
                        title="Upload New Image"
                      >
                        <Upload className="h-4 w-4" />
                        Upload
                      </Button>
                    </div>
                  </div>
                  {watchedData.featuredImage && (
                    <div className="mt-2 p-2 border border-gray-200 rounded-md bg-gray-50">
                      <img
                        src={watchedData.featuredImage}
                        alt="Featured image preview"
                        className="w-full max-w-xs h-auto rounded"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none'
                        }}
                      />
                    </div>
                  )}
                  {errors.featuredImage && (
                    <p className="text-sm text-red-600 mt-1">{errors.featuredImage.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Hero Image */}
            <div>
              <Label htmlFor="hero_image" className="gilroy-text-medium">Hero Image</Label>
              <p className="text-sm text-gray-600 mb-2">Large banner image displayed at the top of the blog post</p>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    id="hero_image"
                    {...register('heroImage')}
                    placeholder="https://example.com/hero-image.jpg"
                    className={`gilroy-text flex-1 ${errors.heroImage ? 'border-red-500' : ''}`}
                  />
                  <div className="flex gap-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHeroMediaPicker(true)}
                      className="whitespace-nowrap"
                      title="Select from Gallery"
                    >
                      <ImageIcon className="h-4 w-4" />
                      Gallery
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHeroImageUpload(true)}
                      className="whitespace-nowrap"
                      title="Upload New Image"
                    >
                      <Upload className="h-4 w-4" />
                      Upload
                    </Button>
                  </div>
                </div>
                {watchedData.heroImage && (
                  <div className="mt-2 p-2 border border-gray-200 rounded-md bg-gray-50">
                    <img
                      src={watchedData.heroImage}
                      alt="Hero image preview"
                      className="w-full max-w-xs h-auto rounded"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none'
                      }}
                    />
                  </div>
                )}
                {errors.heroImage && (
                  <p className="text-sm text-red-600 mt-1">{errors.heroImage.message}</p>
                )}
              </div>
            </div>

            {/* Tags */}
            <div>
              <Label className="gilroy-text-medium">Tags</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {(watchedData.tags || []).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="gilroy-text">
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Add a tag"
                  className="gilroy-text"
                />
                <Button
                  type="button"
                  onClick={addTag}
                  variant="outline"
                  size="sm"
                  className="gilroy-text"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Article Content */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="gilroy-text-bold">Article Content</CardTitle>
              <span className={`text-xs ${getContentLength() >= 100 ? 'text-green-600' : 'text-gray-500'}`}>
                {getContentLength()}/100 characters
                {getContentLength() >= 100 && ' ✓'}
              </span>
            </div>
            <div className="flex flex-wrap items-center gap-1 pt-2 border-b pb-2">
              {/* History */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().undo().run()}
                disabled={!editor?.can().undo()}
                title="Undo"
              >
                <Undo className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().redo().run()}
                disabled={!editor?.can().redo()}
                title="Redo"
              >
                <Redo className="h-4 w-4" />
              </Button>

              <div className="h-6 w-px bg-gray-300 mx-1" />

              {/* Headings */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
                className={editor?.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}
                title="Heading 1"
              >
                <Heading1 className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
                className={editor?.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}
                title="Heading 2"
              >
                <Heading2 className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
                className={editor?.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}
                title="Heading 3"
              >
                <Heading3 className="h-4 w-4" />
              </Button>

              <div className="h-6 w-px bg-gray-300 mx-1" />

              {/* Text formatting */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleBold().run()}
                className={editor?.isActive('bold') ? 'bg-gray-200' : ''}
                title="Bold"
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleItalic().run()}
                className={editor?.isActive('italic') ? 'bg-gray-200' : ''}
                title="Italic"
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleCode().run()}
                className={editor?.isActive('code') ? 'bg-gray-200' : ''}
                title="Inline Code"
              >
                <Code className="h-4 w-4" />
              </Button>

              <div className="h-6 w-px bg-gray-300 mx-1" />

              {/* Lists */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleBulletList().run()}
                className={editor?.isActive('bulletList') ? 'bg-gray-200' : ''}
                title="Bullet List"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                className={editor?.isActive('orderedList') ? 'bg-gray-200' : ''}
                title="Numbered List"
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().toggleBlockquote().run()}
                className={editor?.isActive('blockquote') ? 'bg-gray-200' : ''}
                title="Quote"
              >
                <Quote className="h-4 w-4" />
              </Button>

              <div className="h-6 w-px bg-gray-300 mx-1" />

              {/* Alignment */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().setTextAlign('left').run()}
                className={editor?.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''}
                title="Align Left"
              >
                <AlignLeft className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().setTextAlign('center').run()}
                className={editor?.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''}
                title="Align Center"
              >
                <AlignCenter className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor?.chain().focus().setTextAlign('right').run()}
                className={editor?.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''}
                title="Align Right"
              >
                <AlignRight className="h-4 w-4" />
              </Button>

              <div className="h-6 w-px bg-gray-300 mx-1" />

              {/* Media */}
              <div className="relative group">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  title="Add Image"
                  className="flex items-center"
                >
                  <ImageIcon className="h-4 w-4" />
                  <div className="ml-1 text-xs">▼</div>
                </Button>

                {/* Dropdown menu */}
                <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <div className="py-1">
                    <button
                      type="button"
                      onClick={() => setShowMediaPicker(true)}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <ImageIcon className="h-4 w-4 mr-2" />
                      Select from Gallery
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowImageUpload(true)}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload New Image
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const url = window.prompt('Enter image URL:')
                        if (url) {
                          editor?.chain().focus().setImage({ src: url }).run()
                        }
                      }}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <Link className="h-4 w-4 mr-2" />
                      Insert from URL
                    </button>
                  </div>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  const url = window.prompt('Enter link URL:')
                  if (url) {
                    editor?.chain().focus().setLink({ href: url }).run()
                  }
                }}
                className={editor?.isActive('link') ? 'bg-gray-200' : ''}
                title="Add Link"
              >
                <Link className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="border border-gray-200 rounded-lg p-4">
              <EditorContent editor={editor} />
            </div>
          </CardContent>
        </Card>

        {/* SEO Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="gilroy-text-bold">SEO Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="meta_title" className="gilroy-text-medium">Meta Title</Label>
              <Input
                id="meta_title"
                {...register('seoTitle')}
                placeholder="SEO title (defaults to article title)"
                className={`gilroy-text ${errors.seoTitle ? 'border-red-500' : ''}`}
              />
              {errors.seoTitle && (
                <p className="text-sm text-red-600 mt-1">{errors.seoTitle.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="meta_description" className="gilroy-text-medium">Meta Description</Label>
              <Textarea
                id="meta_description"
                {...register('seoDescription')}
                placeholder="SEO description (defaults to excerpt)"
                rows={3}
                className={`gilroy-text ${errors.seoDescription ? 'border-red-500' : ''}`}
              />
              {errors.seoDescription && (
                <p className="text-sm text-red-600 mt-1">{errors.seoDescription.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 gilroy-text">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              type="submit"
              variant="outline"
              disabled={isLoading}
              className="gilroy-text-medium"
              onClick={() => {
                console.log('Save Draft button clicked')
                console.log('Form errors:', errors)
                console.log('Form data:', watchedData)
              }}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Saving...' : 'Save Draft'}
            </Button>
            <Button
              type="button"
              onClick={(e) => {
                console.log('Publish Article button clicked')
                console.log('Form errors before submit:', errors)
                console.log('Form data before submit:', watchedData)

                if (!isReadyToPublish()) {
                  const issues = []
                  if ((watchedData.title || '').length < 5) issues.push('Title needs at least 5 characters')
                  if (getContentLength() < 100) issues.push(`Content needs ${100 - getContentLength()} more characters`)
                  if ((watchedData.excerpt || '').length < 50) issues.push(`Excerpt needs ${50 - (watchedData.excerpt || '').length} more characters`)

                  setError(`Cannot publish article - please fix:\n• ${issues.join('\n• ')}`)
                  return
                }

                handleFormSubmit((data) => {
                  console.log('handleFormSubmit called with data:', data)
                  return onSubmit(data as unknown as BlogArticleInput, 'published')
                }, (errors) => {
                  console.log('Form validation failed:', errors)
                })(e)
              }}
              disabled={isLoading || !watchedData.title}
              className={`gilroy-text-medium ${!isReadyToPublish() ? 'opacity-50' : ''}`}
              title={!isReadyToPublish() ? 'Complete all required fields to publish' : ''}
            >
              <Send className="h-4 w-4 mr-2" />
              {isLoading ? 'Publishing...' : 'Publish Article'}
            </Button>
          </div>
        </div>
      </form>

      {/* Image Selection Modals */}
      <MediaGalleryPicker
        isOpen={showMediaPicker}
        onClose={() => setShowMediaPicker(false)}
        onSelect={handleImageSelect}
      />

      <BlogImageUpload
        isOpen={showImageUpload}
        onClose={() => setShowImageUpload(false)}
        onUpload={handleImageUpload}
      />

      {/* Featured Image Selection Modals */}
      <MediaGalleryPicker
        isOpen={showFeaturedMediaPicker}
        onClose={() => setShowFeaturedMediaPicker(false)}
        onSelect={handleFeaturedImageSelect}
      />

      <BlogImageUpload
        isOpen={showFeaturedImageUpload}
        onClose={() => setShowFeaturedImageUpload(false)}
        onUpload={handleFeaturedImageUpload}
      />

      {/* Hero Image Selection Modals */}
      <MediaGalleryPicker
        isOpen={showHeroMediaPicker}
        onClose={() => setShowHeroMediaPicker(false)}
        onSelect={handleHeroImageSelect}
      />

      <BlogImageUpload
        isOpen={showHeroImageUpload}
        onClose={() => setShowHeroImageUpload(false)}
        onUpload={handleHeroImageUpload}
      />
    </div>
  )
} 