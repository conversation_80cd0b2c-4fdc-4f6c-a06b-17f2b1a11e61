'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { BookOpen, Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface BlogPublishButtonProps {
    articleId: string
    className?: string
}

export default function BlogPublishButton({ articleId, className }: BlogPublishButtonProps) {
    const [isLoading, setIsLoading] = useState(false)
    const router = useRouter()

    const handlePublish = async () => {
        setIsLoading(true)

        try {
            const response = await fetch(`/api/admin/blog/${articleId}/publish`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })

            const data = await response.json()

            if (!response.ok) {
                throw new Error(data.error || 'Failed to publish article')
            }

            // Refresh the page to show updated status
            router.refresh()
        } catch (error: any) {
            console.error('Error publishing article:', error)
            alert(error.message || 'Failed to publish article')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <Button
            size="sm"
            className={`gilroy-text-medium ${className}`}
            onClick={handlePublish}
            disabled={isLoading}
        >
            {isLoading ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : (
                <BookOpen className="h-4 w-4 mr-1" />
            )}
            {isLoading ? 'Publishing...' : 'Publish'}
        </Button>
    )
} 