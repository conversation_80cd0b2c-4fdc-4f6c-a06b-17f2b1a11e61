import { createServerSupabaseClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Share, Calendar, User, Mail, Phone } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import ProjectTodos from '@/components/admin/ProjectTodos'
import ProjectUpdates from '@/components/admin/ProjectUpdates'

interface ProjectDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const { id } = await params
  const supabase = await createServerSupabaseClient()

  // Get project details
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .single()

  if (error || !project) {
    notFound()
  }

  // Get project todos
  const { data: todos } = await supabase
    .from('project_todos')
    .select('*')
    .eq('project_id', id)
    .order('created_at', { ascending: false })

  // Get project updates
  const { data: updates } = await supabase
    .from('project_updates')
    .select('*')
    .eq('project_id', id)
    .order('created_at', { ascending: false })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 75) return 'bg-green-500'
    if (percentage >= 50) return 'bg-blue-500'
    if (percentage >= 25) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/projects">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.title}</h1>
            <p className="text-gray-600 mt-1">Project Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Link href={`/admin/projects/${project.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          {project.public_access_token && (
            <Button variant="outline">
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>
          )}
        </div>
      </div>

      {/* Project Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Project Information</CardTitle>
                <Badge className={getStatusColor(project.status)}>
                  {project.status.replace('_', ' ')}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Progress</span>
                  <span className="text-sm font-bold">{project.progress_percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(project.progress_percentage)}`}
                    style={{ width: `${project.progress_percentage}%` }}
                  ></div>
                </div>
              </div>

              {/* Description */}
              {project.description && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700">{project.description}</p>
                </div>
              )}

              {/* Dates */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">Created</h4>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    {new Date(project.created_at).toLocaleDateString()}
                  </div>
                </div>
                {project.estimated_completion && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Estimated Completion</h4>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      {new Date(project.estimated_completion).toLocaleDateString()}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Contact Details</h4>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{project.customer_name}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Mail className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{project.customer_email}</span>
                </div>
                {project.customer_phone && (
                  <div className="flex items-center text-sm">
                    <Phone className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{project.customer_phone}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Customer Portal Access */}
            {project.public_access_token && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Customer Portal</h4>
                <div className="p-3 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-600 mb-2">
                    Customer can view progress at:
                  </p>
                  <code className="text-xs bg-white p-2 rounded border block break-all">
                    /project/{project.public_access_token}
                  </code>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Project Management */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Todo Management */}
        <Card>
          <CardHeader>
            <CardTitle>Project Tasks</CardTitle>
            <CardDescription>Manage project todos and track completion</CardDescription>
          </CardHeader>
          <CardContent>
            <ProjectTodos projectId={project.id} todos={todos || []} />
          </CardContent>
        </Card>

        {/* Project Updates */}
        <Card>
          <CardHeader>
            <CardTitle>Project Updates</CardTitle>
            <CardDescription>Timeline of project progress and communications</CardDescription>
          </CardHeader>
          <CardContent>
            <ProjectUpdates projectId={project.id} updates={updates || []} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 