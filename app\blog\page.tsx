'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Calendar, Clock, ArrowRight, Search, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '@/components/header';
import J<PERSON>StickyFooter from '@/components/footer';

// Blog post interface
interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  image: string;
  category: string;
  tags: string[];
  author: string;
  date: string;
  readTime: string;
  featured: boolean;
  seoTitle?: string;
  seoDescription?: string;
  createdAt: string;
  updatedAt: string;
}

const categories = ["All", "General", "Services", "Tips & Advice", "Maintenance", "Restoration", "Company News", "DIY Guides"];

// Map display names to database values
const categoryMapping: Record<string, string> = {
  "All": "all",
  "General": "general",
  "Services": "services",
  "Tips & Advice": "tips",
  "Maintenance": "maintenance",
  "Restoration": "restoration",
  "Company News": "news",
  "DIY Guides": "diy"
};

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch blog articles from API
  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/blog');

        if (!response.ok) {
          throw new Error('Failed to fetch blog posts');
        }

        const data = await response.json();
        setBlogPosts(data.articles || []);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError(err instanceof Error ? err.message : 'Failed to load blog posts');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, []);

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Header */}
      <Header onContactClick={() => scrollToSection('contact')} forceVisible={true} />

      {/* Hero Section */}
      <section className="pt-24 md:pt-32 pb-16 md:pb-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black uppercase tracking-tight text-black mb-6 leading-none"
              style={{ fontFamily: 'Gilroy, sans-serif' }}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              JCC Blog
            </motion.h1>
            <div className="w-20 md:w-24 h-1 bg-black mx-auto mb-6 md:mb-8"></div>
            <motion.p
              className="text-lg md:text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
              style={{ fontFamily: 'Gilroy, sans-serif' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Tips, insights, and updates from the JCC workshop
            </motion.p>
          </div>
        </div>
      </section>

      {/* Loading State */}
      {loading && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-gray-600" />
              <span className="ml-3 text-gray-600" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                Loading articles...
              </span>
            </div>
          </div>
        </section>
      )}

      {/* Error State */}
      {error && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <p className="text-red-600 mb-4" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                {error}
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
                style={{ fontFamily: 'Gilroy, sans-serif' }}
              >
                Try Again
              </button>
            </div>
          </div>
        </section>
      )}

      {/* Featured Article */}
      {!loading && !error && featuredPost && (
        <section className="py-16 md:py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-black text-black mb-8 text-center" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                FEATURED ARTICLE
              </h2>
              <div className="bg-white overflow-hidden shadow-2xl hover:shadow-3xl transition-shadow duration-300 group">
                <div className="md:flex">
                  <div className="md:w-1/2 relative overflow-hidden">
                    <Image
                      src={featuredPost.image}
                      alt={featuredPost.title}
                      width={600}
                      height={400}
                      className="w-full h-64 md:h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="bg-black text-white px-3 py-1 text-xs font-bold uppercase tracking-wider" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                        Featured
                      </span>
                    </div>
                  </div>
                  <div className="md:w-1/2 p-8 md:p-12 flex flex-col justify-center">
                    <div className="flex items-center mb-4 text-gray-600 text-sm">
                      <span className="bg-black text-white px-3 py-1 text-xs font-bold uppercase tracking-wider mr-4" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                        {featuredPost.category}
                      </span>
                      <Calendar size={16} className="mr-2" />
                      <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{featuredPost.date}</span>
                      <Clock size={16} className="ml-4 mr-2" />
                      <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{featuredPost.readTime}</span>
                    </div>
                    <h3 className="text-2xl md:text-3xl font-black text-black mb-4 leading-tight" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                      {featuredPost.title}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                      {featuredPost.excerpt}
                    </p>
                    <Link
                      href={`/blog/${featuredPost.slug}`}
                      className="inline-flex items-center text-black font-bold hover:text-gray-700 transition-colors duration-200"
                      style={{ fontFamily: 'Gilroy, sans-serif' }}
                    >
                      Read Full Article
                      <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Regular Articles Grid */}
      {!loading && !error && (
        <section className="py-16 md:py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {regularPosts.length > 0 ? (
              <>
                <motion.h2
                  className="text-3xl md:text-4xl font-black text-black mb-12 text-center"
                  style={{ fontFamily: 'Gilroy, sans-serif' }}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  LATEST ARTICLES
                </motion.h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {regularPosts.map((post, index) => (
                    <motion.article
                      key={post.id}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="bg-white overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 group"
                    >
                      <div className="relative overflow-hidden">
                        <Image
                          src={post.image}
                          alt={post.title}
                          width={400}
                          height={250}
                          className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute top-4 left-4">
                          <span className="bg-black text-white px-3 py-1 text-xs font-bold uppercase tracking-wider" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                            {post.category}
                          </span>
                        </div>
                      </div>

                      <div className="p-6">
                        <div className="flex items-center mb-3 text-gray-600 text-sm">
                          <Calendar size={14} className="mr-2" />
                          <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{post.date}</span>
                          <Clock size={14} className="ml-4 mr-2" />
                          <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{post.readTime}</span>
                        </div>

                        <h3 className="text-xl font-black text-black mb-3 leading-tight" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                          {post.title}
                        </h3>

                        <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                          {post.excerpt}
                        </p>

                        <Link
                          href={`/blog/${post.slug}`}
                          className="inline-flex items-center text-black font-bold hover:text-gray-700 transition-colors duration-200"
                          style={{ fontFamily: 'Gilroy, sans-serif' }}
                        >
                          Read More
                          <ArrowRight size={14} className="ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                        </Link>
                      </div>
                    </motion.article>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-16">
                <h3 className="text-2xl font-bold text-gray-600 mb-4" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                  No articles found
                </h3>
                <p className="text-gray-500" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                  Check back soon for new content!
                </p>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section id="contact" className="bg-black text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-black mb-6" style={{ fontFamily: 'Gilroy, sans-serif' }}>
            GOT A QUESTION ABOUT YOUR PROJECT?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto" style={{ fontFamily: 'Gilroy, sans-serif' }}>
            Our team of experts is here to help with any questions about your campervan, caravan, or vehicle project.
          </p>
          <Link
            href="/contact"
            className="inline-block bg-white text-black px-8 py-4 text-lg font-bold hover:bg-gray-200 transition-colors duration-200"
            style={{ fontFamily: 'Gilroy, sans-serif' }}
          >
            GET IN TOUCH
          </Link>
        </div>
      </section>

      {/* Footer */}
      <JCCStickyFooter />
    </div>
  );
} 