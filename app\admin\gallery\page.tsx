'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Camera, Images, Zap, TrendingUp, Clock, Upload, Grid3X3, Split } from 'lucide-react'
import GalleryUploadForm from '@/components/admin/GalleryUploadForm'
import GalleryGrid from '@/components/admin/GalleryGrid'
import BeforeAfterGrid from '@/components/admin/BeforeAfterGrid'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import AlbumGrid from '@/components/admin/AlbumGrid'
import { createClient } from '@/lib/supabase'

interface GalleryStats {
  totalImages: number
  publicImages: number
  featuredImages: number
  beforeAfterPairs: number
  recentUploads: number
}

function StatsCardSkeleton() {
  return (
    <Card className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 animate-pulse" />
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
        <CardTitle className="text-sm font-medium">
          <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
        </CardTitle>
        <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
      </CardHeader>
      <CardContent className="relative z-10">
        <div className="h-8 w-12 bg-gray-200 rounded animate-pulse mb-1" />
        <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
      </CardContent>
    </Card>
  )
}

function StatsCards() {
  const [stats, setStats] = useState<GalleryStats>({
    totalImages: 0,
    publicImages: 0,
    featuredImages: 0,
    beforeAfterPairs: 0,
    recentUploads: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchStats() {
      try {
        const supabase = createClient()

        const [
          { count: totalImages },
          { count: publicImages },
          { count: featuredImages },
          { count: beforeAfterPairs },
          { data: recentImages }
        ] = await Promise.all([
          supabase.from('gallery_images').select('*', { count: 'exact', head: true }),
          supabase.from('gallery_images').select('*', { count: 'exact', head: true }).eq('is_public', true),
          supabase.from('gallery_images').select('*', { count: 'exact', head: true }).eq('is_featured', true),
          supabase.from('before_after_pairs').select('*', { count: 'exact', head: true }),
          supabase.from('gallery_images')
            .select('created_at')
            .order('created_at', { ascending: false })
            .limit(5)
        ])

        const recentCount = recentImages?.filter(img => {
          const created = new Date(img.created_at)
          const weekAgo = new Date()
          weekAgo.setDate(weekAgo.getDate() - 7)
          return created >= weekAgo
        }).length || 0

        setStats({
          totalImages: totalImages || 0,
          publicImages: publicImages || 0,
          featuredImages: featuredImages || 0,
          beforeAfterPairs: beforeAfterPairs || 0,
          recentUploads: recentCount
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        {Array(5).fill(0).map((_, i) => (
          <StatsCardSkeleton key={i} />
        ))}
      </div>
    )
  }

  const publicPercentage = stats.totalImages > 0 ? Math.round((stats.publicImages / stats.totalImages) * 100) : 0

  const statsData = [
    {
      title: "Total Images",
      value: stats.totalImages,
      icon: Images,
      description: "All gallery images",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "Public Images",
      value: stats.publicImages,
      icon: Camera,
      description: `${publicPercentage}% of total`,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "Featured Images",
      value: stats.featuredImages,
      icon: Zap,
      description: "Highlighted content",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      title: "Before & After",
      value: stats.beforeAfterPairs,
      icon: TrendingUp,
      description: "Transformation pairs",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      title: "Recent Uploads",
      value: stats.recentUploads,
      icon: Clock,
      description: "This week",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
      {statsData.map((stat) => (
        <Card key={stat.title} className="group hover:shadow-md transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700">{stat.title}</CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor} group-hover:scale-110 transition-transform duration-200`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
            <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default function GalleryAdminPage() {
  const [activeTab, setActiveTab] = useState('upload')

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Gallery Management</h1>
          <p className="text-gray-600 mt-1">
            Upload and manage gallery images including before/after transformations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
            Live
          </Badge>
        </div>
      </div>

      {/* Stats Section */}
      <StatsCards />

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 lg:max-w-lg">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload
          </TabsTrigger>
          <TabsTrigger value="gallery" className="flex items-center gap-2">
            <Grid3X3 className="h-4 w-4" />
            Gallery
          </TabsTrigger>
          <TabsTrigger value="albums" className="flex items-center gap-2">
            <Images className="h-4 w-4" />
            Albums
          </TabsTrigger>
          <TabsTrigger value="before-after" className="flex items-center gap-2">
            <Split className="h-4 w-4" />
            Before & After
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="upload" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>Upload New Images</CardTitle>
                <CardDescription>
                  Add new images to your gallery with metadata and visibility settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <GalleryUploadForm />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="gallery" className="mt-0">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Gallery Images</CardTitle>
                    <CardDescription>
                      Manage all gallery images with bulk operations and visibility controls
                    </CardDescription>
                  </div>
                  <Badge variant="secondary" className="hidden sm:flex">
                    Grid View
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <GalleryGrid />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="albums" className="mt-0">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Gallery Albums</CardTitle>
                    <CardDescription>
                      Create and manage albums to organize your gallery images into collections
                    </CardDescription>
                  </div>
                  <Badge variant="secondary" className="hidden sm:flex">
                    Album View
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <AlbumGrid />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="before-after" className="mt-0">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Before & After Transformations</CardTitle>
                    <CardDescription>
                      Manage transformation pairs and project showcases
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="h-2 w-2 bg-red-500 rounded-full" />
                    <div className="h-2 w-2 bg-green-500 rounded-full" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <BeforeAfterGrid />
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}
