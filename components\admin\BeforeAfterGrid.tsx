'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Star,
  ArrowRight,
  Calendar,
  Car,
  Wrench,
  MoreVertical,
  ExternalLink
} from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { invalidateGalleryCache } from '@/lib/cache-utils'
import BeforeAfterSlider from '@/components/ui/before-after-slider'

interface BeforeAfterPair {
  id: string
  title: string
  description: string | null
  category: string | null
  tags: string[] | null
  order_index: number
  is_featured: boolean
  is_public: boolean
  vehicle_details: Record<string, any> | null
  work_details: Record<string, any> | null
  created_at: string
  updated_at: string
  before_image: {
    id: string
    title: string
    image_url: string
    alt_text: string | null
  }
  after_image: {
    id: string
    title: string
    image_url: string
    alt_text: string | null
  }
}

interface BeforeAfterGridProps {
  onRefresh?: () => void
}

export default function BeforeAfterGrid({ onRefresh }: BeforeAfterGridProps) {
  const router = useRouter()
  const supabase = createClient()
  
  const [pairs, setPairs] = useState<BeforeAfterPair[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPairs, setSelectedPairs] = useState<string[]>([])

  const fetchPairs = async () => {
    try {
      const { data, error } = await supabase
        .from('before_after_pairs')
        .select(`
          *,
          before_image:gallery_images!before_image_id(*),
          after_image:gallery_images!after_image_id(*)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setPairs(data || [])
    } catch (error) {
      console.error('Error fetching before/after pairs:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPairs()
  }, [])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this before/after pair? This will also delete the associated images.')) {
      return
    }

    try {
      const pair = pairs.find(p => p.id === id)
      if (!pair) return

      // Delete storage files
      const beforeFileName = pair.before_image.image_url.split('/').pop()
      const afterFileName = pair.after_image.image_url.split('/').pop()

      if (beforeFileName) {
        await supabase.storage.from('gallery-images').remove([beforeFileName])
      }
      if (afterFileName) {
        await supabase.storage.from('gallery-images').remove([afterFileName])
      }

      // Delete database records (cascade will handle before_after_pairs)
      await supabase.from('gallery_images').delete().eq('id', pair.before_image.id)
      await supabase.from('gallery_images').delete().eq('id', pair.after_image.id)

      // Refresh data
      await fetchPairs()
      await invalidateGalleryCache('before-after')
      onRefresh?.()
    } catch (error) {
      console.error('Error deleting before/after pair:', error)
      alert('Failed to delete before/after pair')
    }
  }

  const handleToggleVisibility = async (id: string, currentVisibility: boolean) => {
    try {
      const { error } = await supabase
        .from('before_after_pairs')
        .update({ is_public: !currentVisibility })
        .eq('id', id)

      if (error) throw error

      await fetchPairs()
      await invalidateGalleryCache('before-after')
    } catch (error) {
      console.error('Error updating visibility:', error)
    }
  }

  const handleToggleFeatured = async (id: string, currentFeatured: boolean) => {
    try {
      const { error } = await supabase
        .from('before_after_pairs')
        .update({ is_featured: !currentFeatured })
        .eq('id', id)

      if (error) throw error

      await fetchPairs()
      await invalidateGalleryCache('before-after')
    } catch (error) {
      console.error('Error updating featured status:', error)
    }
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedPairs.length} before/after pairs?`)) {
      return
    }

    try {
      for (const id of selectedPairs) {
        await handleDelete(id)
      }
      setSelectedPairs([])
    } catch (error) {
      console.error('Error bulk deleting:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const getVehicleInfo = (vehicleDetails: Record<string, any> | null) => {
    if (!vehicleDetails) return 'Vehicle details not available'
    const { year, make, model, type } = vehicleDetails
    const parts = [year, make, model, type].filter(Boolean)
    return parts.length > 0 ? parts.join(' ') : 'Vehicle details not available'
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600 gilroy-text">Loading before/after pairs...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with bulk actions */}
      {selectedPairs.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="gilroy-text-medium">
                {selectedPairs.length} pair{selectedPairs.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="gilroy-text-medium"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedPairs([])}
                  className="gilroy-text-medium"
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Before/After Pairs Grid */}
      {pairs.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <ArrowRight className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="gilroy-text-bold text-xl text-gray-600 mb-2">
              No Before/After Pairs Yet
            </h3>
            <p className="gilroy-text text-gray-500 mb-4">
              Upload your first before/after transformation to get started.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {pairs.map((pair) => (
            <Card key={pair.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="gilroy-text-bold text-lg flex items-center gap-2">
                      {pair.title}
                      {pair.is_featured && (
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                      )}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge 
                        variant={pair.is_public ? "default" : "secondary"}
                        className="text-xs gilroy-text-medium"
                      >
                        {pair.is_public ? 'Public' : 'Private'}
                      </Badge>
                      <span className="text-xs text-gray-500 gilroy-text">
                        {formatDate(pair.created_at)}
                      </span>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    checked={selectedPairs.includes(pair.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPairs([...selectedPairs, pair.id])
                      } else {
                        setSelectedPairs(selectedPairs.filter(id => id !== pair.id))
                      }
                    }}
                    className="mt-1"
                  />
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Before/After Slider */}
                <div className="relative">
                  <BeforeAfterSlider
                    beforeImage={pair.before_image.image_url}
                    afterImage={pair.after_image.image_url}
                    title=""
                    beforeAlt={pair.before_image.alt_text || `Before: ${pair.title}`}
                    afterAlt={pair.after_image.alt_text || `After: ${pair.title}`}
                    className="border border-gray-200"
                  />
                </div>

                {/* Description */}
                {pair.description && (
                  <p className="text-sm text-gray-600 gilroy-text line-clamp-2">
                    {pair.description}
                  </p>
                )}

                {/* Vehicle Details */}
                {pair.vehicle_details && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Car className="h-4 w-4" />
                    <span className="gilroy-text">{getVehicleInfo(pair.vehicle_details)}</span>
                  </div>
                )}

                {/* Work Details */}
                {pair.work_details?.serviceType && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Wrench className="h-4 w-4" />
                    <span className="gilroy-text">{pair.work_details.serviceType}</span>
                  </div>
                )}

                {/* Tags */}
                {pair.tags && pair.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {pair.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs gilroy-text">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleVisibility(pair.id, pair.is_public)}
                      className="text-gray-600 hover:text-gray-800 gilroy-text"
                    >
                      {pair.is_public ? (
                        <><Eye className="h-3 w-3 mr-1" />Public</>
                      ) : (
                        <><EyeOff className="h-3 w-3 mr-1" />Private</>
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleFeatured(pair.id, pair.is_featured)}
                      className="text-gray-600 hover:text-gray-800 gilroy-text"
                    >
                      <Star className={`h-3 w-3 mr-1 ${pair.is_featured ? 'text-yellow-500 fill-yellow-500' : ''}`} />
                      Featured
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(`/gallery?category=before-after`, '_blank')}
                      className="text-blue-600 hover:text-blue-700 gilroy-text"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(pair.id)}
                      className="text-red-600 hover:text-red-700 gilroy-text"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
} 