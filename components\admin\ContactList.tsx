'use client'

import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Eye, Reply, Calendar, User, Mail, MessageSquare, Car } from 'lucide-react'
import Link from 'next/link'

interface Contact {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  service_type?: string
  vehicle_type?: string
  vehicle_make?: string
  vehicle_model?: string
  vehicle_year?: number
  message: string
  status: 'new' | 'responded' | 'archived'
  created_at: string
  admin_response?: string
  response_sent_at?: string
  // New fields for enhanced contact form
  service_questions?: Record<string, string>
  selected_color?: string
  attachments?: string[]
  attachment_urls?: string[]
}

interface ContactListProps {
  contacts: Contact[]
}

export default function ContactList({ contacts }: ContactListProps) {

  if (contacts.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No contacts</h3>
        <p className="mt-1 text-sm text-gray-500">
          No customer inquiries have been submitted yet.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {contacts.map((contact) => (
        <Card key={contact.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <h3 className="text-lg font-medium text-gray-900">{contact.name}</h3>
                </div>
                <div className="mt-2 space-y-1">
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    {contact.email}
                  </div>
                  {contact.phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <User className="h-4 w-4 mr-2" />
                      {contact.phone}
                    </div>
                  )}
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    {new Date(contact.created_at).toLocaleDateString()}
                  </div>
                  {contact.service_type && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      {contact.service_type}
                    </div>
                  )}
                  {(contact.vehicle_make || contact.vehicle_model || contact.vehicle_year || contact.vehicle_type) && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Car className="h-4 w-4 mr-2" />
                      {[
                        contact.vehicle_year,
                        contact.vehicle_make,
                        contact.vehicle_model,
                        contact.vehicle_type && `(${contact.vehicle_type})`
                      ].filter(Boolean).join(' ')}
                    </div>
                  )}
                </div>
                <p className="mt-3 text-sm text-gray-700 line-clamp-2">
                  {contact.message}
                </p>
              </div>
              <div className="flex space-x-2">
                <Link href={`/admin/contacts/${contact.id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </Link>
                {contact.status === 'new' && (
                  <Link href={`/admin/contacts/${contact.id}/respond`}>
                    <Button variant="default" size="sm">
                      <Reply className="h-4 w-4 mr-2" />
                      Respond
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 