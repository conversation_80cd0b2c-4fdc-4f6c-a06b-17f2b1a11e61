{"name": "jcc", "version": "0.1.0", "private": true, "engines": {"bun": ">=1.2.17", "node": ">=22.13.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-spring/web": "^10.0.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tiptap/extension-heading": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/pm": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.6", "lenis": "^1.3.8", "lucide-react": "^0.525.0", "motion": "^12.23.6", "next": "^15.4.2", "prerender-node": "^3.8.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "tiptap-extension-resize-image": "^1.2.3", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.31.0", "eslint-config-next": "15.3.5", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}}