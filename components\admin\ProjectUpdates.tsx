'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Plus, 
  Calendar, 
  MessageSquare, 
  Image as ImageIcon, 
  Edit, 
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { projectUpdateSchema, type ProjectUpdateInput } from '@/lib/validations'

interface ProjectUpdate {
  id: string
  project_id: string
  title: string
  content: string
  content_html: string
  update_type: 'progress' | 'communication' | 'milestone' | 'issue'
  progress_percentage: number | null
  images: string[] | null
  created_at: string
  updated_at: string
}

interface ProjectUpdatesProps {
  projectId: string
  updates: ProjectUpdate[]
}

export default function ProjectUpdates({ projectId, updates }: ProjectUpdatesProps) {
  const [isAddingUpdate, setIsAddingUpdate] = useState(false)
  const [editingUpdate, setEditingUpdate] = useState<ProjectUpdate | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  // Form for adding new updates
  const {
    register: registerAdd,
    handleSubmit: handleAddSubmit,
    formState: { errors: addErrors },
    reset: resetAdd,
  } = useForm<ProjectUpdateInput>({
    resolver: zodResolver(projectUpdateSchema),
    defaultValues: {
      title: '',
      content: '',
      updateType: 'progress',
      progressPercentage: 0,
    }
  })

  // Form for editing updates
  const {
    register: registerEdit,
    handleSubmit: handleEditSubmit,
    formState: { errors: editErrors },
    reset: resetEdit,
    setValue: setEditValue,
  } = useForm<ProjectUpdateInput>({
    resolver: zodResolver(projectUpdateSchema),
  })

  const onAddUpdate = async (data: ProjectUpdateInput) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_updates')
        .insert([{
          project_id: projectId,
          title: data.title,
          content: data.content,
          content_html: data.content,
          update_type: data.updateType,
          progress_percentage: data.progressPercentage || null,
          images: null
        }])

      if (error) throw error

      resetAdd()
      setIsAddingUpdate(false)
      router.refresh()
    } catch (error) {
      console.error('Error adding update:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteUpdate = async (updateId: string) => {
    if (!confirm('Are you sure you want to delete this update?')) return

    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_updates')
        .delete()
        .eq('id', updateId)

      if (error) throw error

      router.refresh()
    } catch (error) {
      console.error('Error deleting update:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const onEditUpdate = async (data: ProjectUpdateInput) => {
    if (!editingUpdate) return

    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_updates')
        .update({
          title: data.title,
          content: data.content,
          content_html: data.content,
          update_type: data.updateType,
          progress_percentage: data.progressPercentage
        })
        .eq('id', editingUpdate.id)

      if (error) throw error

      setEditingUpdate(null)
      resetEdit()
      router.refresh()
    } catch (error) {
      console.error('Error updating update:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditUpdate = (update: ProjectUpdate) => {
    setEditingUpdate(update)
    setEditValue('title', update.title)
    setEditValue('content', update.content)
    setEditValue('updateType', update.update_type)
    setEditValue('progressPercentage', update.progress_percentage || 0)
  }

  const getUpdateTypeIcon = (type: string) => {
    switch (type) {
      case 'progress':
        return <CheckCircle className="h-4 w-4" />
      case 'communication':
        return <MessageSquare className="h-4 w-4" />
      case 'milestone':
        return <Calendar className="h-4 w-4" />
      case 'issue':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getUpdateTypeColor = (type: string) => {
    switch (type) {
      case 'progress':
        return 'bg-blue-100 text-blue-800'
      case 'communication':
        return 'bg-green-100 text-green-800'
      case 'milestone':
        return 'bg-purple-100 text-purple-800'
      case 'issue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const sortedUpdates = [...updates].sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  )

  return (
    <div className="space-y-4">
      {/* Add Update Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 gilroy-text-bold">Updates</h3>
        <Dialog open={isAddingUpdate} onOpenChange={setIsAddingUpdate}>
          <DialogTrigger asChild>
            <Button size="sm" className="gilroy-text-medium">
              <Plus className="h-4 w-4 mr-2" />
              Add Update
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="gilroy-text-bold">Add Project Update</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleAddSubmit(onAddUpdate)} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Title</label>
                <Input
                  {...registerAdd('title')}
                  placeholder="Enter update title"
                  className={`gilroy-text ${addErrors.title ? 'border-red-500' : ''}`}
                />
                {addErrors.title && (
                  <p className="text-sm text-red-600 mt-1">{addErrors.title.message}</p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Content</label>
                <Textarea
                  {...registerAdd('content')}
                  placeholder="Enter update content"
                  className={`gilroy-text ${addErrors.content ? 'border-red-500' : ''}`}
                  rows={4}
                />
                {addErrors.content && (
                  <p className="text-sm text-red-600 mt-1">{addErrors.content.message}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Type</label>
                  <select
                    {...registerAdd('updateType')}
                    className={`w-full p-2 border border-gray-300 rounded-md gilroy-text ${addErrors.updateType ? 'border-red-500' : ''}`}
                  >
                    <option value="progress">Progress Update</option>
                    <option value="communication">Communication</option>
                    <option value="milestone">Milestone</option>
                    <option value="issue">Issue</option>
                  </select>
                  {addErrors.updateType && (
                    <p className="text-sm text-red-600 mt-1">{addErrors.updateType.message}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Progress %</label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    {...registerAdd('progressPercentage', { valueAsNumber: true })}
                    placeholder="Optional"
                    className={`gilroy-text ${addErrors.progressPercentage ? 'border-red-500' : ''}`}
                  />
                  {addErrors.progressPercentage && (
                    <p className="text-sm text-red-600 mt-1">{addErrors.progressPercentage.message}</p>
                  )}
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsAddingUpdate(false)
                    resetAdd()
                  }}
                  className="gilroy-text"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="gilroy-text-medium"
                >
                  {isLoading ? 'Adding...' : 'Add Update'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Updates Timeline */}
      <div className="space-y-4">
        {sortedUpdates.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="gilroy-text">No updates yet. Add your first update to start tracking progress!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedUpdates.map((update, index) => (
              <div key={update.id} className="relative">
                {/* Timeline line */}
                {index < sortedUpdates.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200"></div>
                )}
                
                {/* Update card */}
                <div className="flex items-start space-x-4">
                  {/* Icon */}
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${getUpdateTypeColor(update.update_type)}`}>
                    {getUpdateTypeIcon(update.update_type)}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors duration-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium text-gray-900 gilroy-text-medium">{update.title}</h4>
                          <Badge className={`${getUpdateTypeColor(update.update_type)} gilroy-text text-xs`}>
                            {update.update_type.replace('_', ' ')}
                          </Badge>
                          {update.progress_percentage !== null && (
                            <Badge className="bg-blue-100 text-blue-800 gilroy-text text-xs">
                              {update.progress_percentage}%
                            </Badge>
                          )}
                        </div>
                        <p className="text-gray-700 gilroy-text mb-3">{update.content}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 gilroy-text">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(update.created_at).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{new Date(update.created_at).toLocaleTimeString()}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Actions */}
                      <div className="flex items-center space-x-1 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingUpdate(update)}
                          disabled={isLoading}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteUpdate(update.id)}
                          disabled={isLoading}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Edit Update Dialog */}
      {editingUpdate && (
        <Dialog open={!!editingUpdate} onOpenChange={() => setEditingUpdate(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="gilroy-text-bold">Edit Update</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleEditSubmit(onEditUpdate)} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Title</label>
                <Input
                  {...registerEdit('title')}
                  placeholder="Enter update title"
                  className={`gilroy-text ${editErrors.title ? 'border-red-500' : ''}`}
                />
                {editErrors.title && (
                  <p className="text-sm text-red-600 mt-1">{editErrors.title.message}</p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Content</label>
                <Textarea
                  {...registerEdit('content')}
                  placeholder="Enter update content"
                  className={`gilroy-text ${editErrors.content ? 'border-red-500' : ''}`}
                  rows={4}
                />
                {editErrors.content && (
                  <p className="text-sm text-red-600 mt-1">{editErrors.content.message}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Type</label>
                  <select
                    {...registerEdit('updateType')}
                    className={`w-full p-2 border border-gray-300 rounded-md gilroy-text ${editErrors.updateType ? 'border-red-500' : ''}`}
                  >
                    <option value="progress">Progress Update</option>
                    <option value="communication">Communication</option>
                    <option value="milestone">Milestone</option>
                    <option value="issue">Issue</option>
                  </select>
                  {editErrors.updateType && (
                    <p className="text-sm text-red-600 mt-1">{editErrors.updateType.message}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Progress %</label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    {...registerEdit('progressPercentage', { valueAsNumber: true })}
                    placeholder="Optional"
                    className={`gilroy-text ${editErrors.progressPercentage ? 'border-red-500' : ''}`}
                  />
                  {editErrors.progressPercentage && (
                    <p className="text-sm text-red-600 mt-1">{editErrors.progressPercentage.message}</p>
                  )}
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setEditingUpdate(null)
                    resetEdit()
                  }}
                  className="gilroy-text"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="gilroy-text-medium"
                >
                  {isLoading ? 'Updating...' : 'Update'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
