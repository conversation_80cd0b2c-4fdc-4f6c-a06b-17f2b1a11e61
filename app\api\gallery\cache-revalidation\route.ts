import { NextRequest, NextResponse } from 'next/server'
import { revalidateTag, revalidatePath } from 'next/cache'

// Webhook endpoint for cache invalidation
export async function POST(request: NextRequest) {
  try {
    // For external webhooks, verify the secret
    const authHeader = request.headers.get('authorization')
    const expectedSecret = process.env.WEBHOOK_SECRET
    const isExternalWebhook = authHeader && expectedSecret

    if (isExternalWebhook && authHeader !== `Bearer ${expectedSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { table, event_type, record } = body

    // Handle gallery_images, before_after_pairs, and gallery_albums table events
    if (table !== 'gallery_images' && table !== 'before_after_pairs' && table !== 'gallery_albums') {
      return NextResponse.json({ message: 'Ignored - not gallery or before/after table' })
    }

    console.log(`${table} cache invalidation triggered: ${event_type}`, record?.id)

    // Revalidate cache tags and paths
    revalidateTag('gallery-images')
    revalidateTag('gallery-public')
    revalidateTag('before-after-pairs')
    revalidateTag('gallery-before-after')
    revalidateTag('gallery-albums')
    revalidateTag('albums-public')
    revalidatePath('/gallery')
    revalidatePath('/api/gallery')
    revalidatePath('/api/gallery/before-after')
    revalidatePath('/api/gallery/albums')

    // If specific category is available, revalidate category-specific cache
    if (record?.category) {
      revalidateTag(`gallery-category-${record.category}`)
      revalidateTag(`before-after-category-${record.category}`)
    }

    const revalidatedItems = [
      'gallery-images',
      'gallery-public',
      'before-after-pairs',
      'gallery-before-after',
      'gallery-albums',
      'albums-public',
      '/gallery',
      '/api/gallery',
      '/api/gallery/before-after',
      '/api/gallery/albums'
    ]

    return NextResponse.json({
      message: 'Cache invalidated successfully',
      table,
      revalidated: revalidatedItems
    })
  } catch (error) {
    console.error('Cache revalidation error:', error)
    return NextResponse.json(
      { error: 'Failed to revalidate cache' },
      { status: 500 }
    )
  }
} 