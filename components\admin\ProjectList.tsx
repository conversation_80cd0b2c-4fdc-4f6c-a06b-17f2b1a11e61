'use client'

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { FolderOpen, Eye, Edit, Share, Calendar, User, Percent } from 'lucide-react'
import Link from 'next/link'

interface Project {
  id: string
  title: string
  description?: string
  customer_name: string
  customer_email: string
  customer_phone?: string
  status: 'active' | 'completed' | 'on_hold' | 'cancelled'
  progress_percentage: number
  estimated_completion?: string
  actual_completion?: string
  created_at: string
  updated_at: string
  public_access_token?: string
}

interface ProjectListProps {
  projects: Project[]
}

export default function ProjectList({ projects }: ProjectListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 75) return 'bg-green-500'
    if (percentage >= 50) return 'bg-blue-500'
    if (percentage >= 25) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (projects.length === 0) {
    return (
      <div className="text-center py-8">
        <FolderOpen className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
        <p className="mt-1 text-sm text-gray-500">
          Get started by creating a new project.
        </p>
        <div className="mt-6">
          <Link href="/admin/projects/new">
            <Button>
              <FolderOpen className="h-4 w-4 mr-2" />
              New Project
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <Card key={project.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-lg font-medium line-clamp-1">
                  {project.title}
                </CardTitle>
                <CardDescription className="mt-1">
                  {project.customer_name}
                </CardDescription>
              </div>
              <Badge className={getStatusColor(project.status)}>
                {project.status.replace('_', ' ')}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Progress</span>
                <span className="font-medium">{project.progress_percentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(project.progress_percentage)}`}
                  style={{ width: `${project.progress_percentage}%` }}
                ></div>
              </div>
            </div>

            {/* Project Details */}
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                <span>{project.customer_email}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                <span>Created {new Date(project.created_at).toLocaleDateString()}</span>
              </div>
              {project.estimated_completion && (
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>Due {new Date(project.estimated_completion).toLocaleDateString()}</span>
                </div>
              )}
            </div>

            {/* Description */}
            {project.description && (
              <p className="text-sm text-gray-700 line-clamp-2">
                {project.description}
              </p>
            )}

            {/* Actions */}
            <div className="flex space-x-2 pt-2">
              <Link href={`/admin/projects/${project.id}`} className="flex-1">
                <Button variant="outline" size="sm" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Button>
              </Link>
              <Link href={`/admin/projects/${project.id}/edit`} className="flex-1">
                <Button variant="outline" size="sm" className="w-full">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
              {project.public_access_token && (
                <Button variant="outline" size="sm">
                  <Share className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 