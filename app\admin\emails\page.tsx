import { createServerSupabaseClient } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Mail, 
  Plus, 
  Send, 
  Eye, 
  Edit, 
  Trash2, 
  Users,
  Calendar,
  TrendingUp,
  Clock
} from 'lucide-react'
import Link from 'next/link'

export default async function EmailsPage() {
  const supabase = await createServerSupabaseClient()

  // Get email campaigns
  const { data: campaigns } = await supabase
    .from('email_campaigns')
    .select('*')
    .order('created_at', { ascending: false })

  // Get subscriber count
  const { count: subscriberCount } = await supabase
    .from('subscribers')
    .select('*', { count: 'exact', head: true })

  // Get email stats
  const { count: totalSent } = await supabase
    .from('email_logs')
    .select('*', { count: 'exact', head: true })

  const { count: draftCount } = await supabase
    .from('email_campaigns')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'draft')

  const { count: sentCount } = await supabase
    .from('email_campaigns')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'sent')

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'scheduled':
        return 'bg-blue-100 text-blue-800'
      case 'sending':
        return 'bg-purple-100 text-purple-800'
      case 'sent':
        return 'bg-green-100 text-green-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 gilroy-text-bold">Email Campaigns</h1>
          <p className="text-gray-600 mt-2 gilroy-text">
            Create and manage email campaigns for your subscribers
          </p>
        </div>
        <Link href="/admin/emails/new">
          <Button className="gilroy-text-medium">
            <Plus className="h-4 w-4 mr-2" />
            New Campaign
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Total Subscribers</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{subscriberCount || 0}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Draft Campaigns</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{draftCount || 0}</p>
              </div>
              <Edit className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Sent Campaigns</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{sentCount || 0}</p>
              </div>
              <Send className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Total Emails Sent</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{totalSent || 0}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaigns List */}
      <Card>
        <CardHeader>
          <CardTitle className="gilroy-text-bold">Email Campaigns</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y divide-gray-100">
            {campaigns && campaigns.length > 0 ? (
              campaigns.map((campaign) => (
                <div key={campaign.id} className="p-6 hover:bg-gray-50 transition-colors duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <Mail className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium">
                            {campaign.subject}
                          </h3>
                          <p className="text-sm text-gray-600 gilroy-text mt-1">
                            {campaign.preview_text || 'No preview text'}
                          </p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-500 gilroy-text">
                                {new Date(campaign.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            {campaign.scheduled_at && (
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span className="text-sm text-gray-500 gilroy-text">
                                  Scheduled: {new Date(campaign.scheduled_at).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge className={`${getStatusColor(campaign.status)} gilroy-text-medium`}>
                        {campaign.status.toUpperCase()}
                      </Badge>
                      <div className="flex items-center space-x-2">
                        <Link href={`/admin/emails/${campaign.id}`}>
                          <Button variant="ghost" size="sm" className="gilroy-text">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </Link>
                        {campaign.status === 'draft' && (
                          <Link href={`/admin/emails/${campaign.id}/edit`}>
                            <Button variant="ghost" size="sm" className="gilroy-text">
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                          </Link>
                        )}
                        {campaign.status === 'draft' && (
                          <Link href={`/admin/emails/${campaign.id}/send`}>
                            <Button size="sm" className="gilroy-text-medium">
                              <Send className="h-4 w-4 mr-1" />
                              Send
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-12 text-center">
                <Mail className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium mb-2">
                  No email campaigns yet
                </h3>
                <p className="text-gray-500 gilroy-text mb-6">
                  Create your first email campaign to start engaging with your customers
                </p>
                <Link href="/admin/emails/new">
                  <Button className="gilroy-text-medium">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Campaign
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 