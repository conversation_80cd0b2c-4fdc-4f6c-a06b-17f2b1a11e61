import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')

    const supabase = createClient()

    // Build query for before/after pairs
    let query = supabase
      .from('before_after_pairs')
      .select(`
        *,
        before_image:gallery_images!before_image_id(*),
        after_image:gallery_images!after_image_id(*)
      `)
      .eq('is_public', true)
      .order('order_index', { ascending: true })
      .order('created_at', { ascending: false })

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    if (featured === 'true') {
      query = query.eq('is_featured', true)
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }

    if (limit) {
      query = query.limit(parseInt(limit))
    }

    const { data: pairs, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch before/after pairs' }, { status: 500 })
    }

    // Transform data for frontend consumption
    const transformedPairs = pairs?.map(pair => ({
      id: pair.id,
      title: pair.title,
      description: pair.description,
      category: pair.category,
      tags: pair.tags || [],
      beforeImage: {
        id: pair.before_image.id,
        url: pair.before_image.image_url,
        alt: pair.before_image.alt_text || `Before: ${pair.title}`,
        title: pair.before_image.title
      },
      afterImage: {
        id: pair.after_image.id,
        url: pair.after_image.image_url,
        alt: pair.after_image.alt_text || `After: ${pair.title}`,
        title: pair.after_image.title
      },
      vehicleDetails: pair.vehicle_details,
      workDetails: pair.work_details,
      isFeatured: pair.is_featured,
      orderIndex: pair.order_index,
      createdAt: pair.created_at,
      type: 'before-after' as const
    })) || []

    // Set cache headers
    const response = NextResponse.json(transformedPairs)
    response.headers.set('Cache-Control', 's-maxage=86400, stale-while-revalidate=43200')
    response.headers.set('CDN-Cache-Control', 's-maxage=86400')
    
    // Add cache tags for targeted invalidation
    const tags = ['before-after-pairs', 'gallery-before-after']
    if (category && category !== 'all') {
      tags.push(`before-after-category-${category}`)
    }
    if (featured === 'true') {
      tags.push('before-after-featured')
    }
    response.headers.set('Cache-Tag', tags.join(', '))

    return response
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 