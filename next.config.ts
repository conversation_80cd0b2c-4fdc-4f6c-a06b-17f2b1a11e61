import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'bpftdtzlbuqcjjphwwro.supabase.co',
      },
    ],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
  },
  
  // Performance optimizations
  compress: true,
  poweredByHeader: false,
  
  // Build optimizations
  experimental: {
    optimizePackageImports: ['lucide-react'],
    ppr: false,
  },
  
  // Disable static optimization for pages with client-side interactivity
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // Turbopack configuration
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  
  // Production settings
  ...(process.env.NODE_ENV === 'production' && {
    output: 'standalone',
    trailingSlash: false,
    generateEtags: false,
  }),
  
  // Skip static generation for problematic pages
  skipTrailingSlashRedirect: true,
};

export default nextConfig;
