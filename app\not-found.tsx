'use client';

import Link from 'next/link'
import { Home, ArrowLeft, Phone, Mail, MapPin } from 'lucide-react'
import Image from 'next/image'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="max-w-2xl w-full text-center">
          
          {/* Breadcrumbs */}
          <div className="mb-8">
            <nav className="flex justify-center" aria-label="Breadcrumb">
              <ol className="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li className="inline-flex items-center">
                  <Link href="/" className="inline-flex items-center text-sm font-medium text-gray-400 hover:text-white gilroy-text">
                    <Home className="w-3 h-3 me-2.5" />
                    Home
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                      <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span className="ms-1 text-sm font-medium text-gray-300 gilroy-text">404</span>
                  </div>
                </li>
              </ol>
            </nav>
          </div>

          {/* Logo */}
          <div className="mb-12">
            <Image
              src="/images/logo-main.png"
              alt="JCC Customs & Commercials"
              width={300}
              height={100}
              className="w-auto h-20 md:h-24 lg:h-28 mx-auto max-w-full"
            />
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </div>

          {/* 404 Error */}
          <div className="mb-12">
            <h1 className="gilroy-text-bold text-8xl md:text-9xl lg:text-[12rem] font-black text-white/20 mb-4 leading-none">
              404
            </h1>
            <h2 className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl font-black uppercase tracking-tight text-white mb-6">
              Page Not Found
            </h2>
            <p className="gilroy-text text-lg md:text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
            </p>
          </div>

          {/* Navigation Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center mb-16">
            <Link href="/">
              <button className="bg-white text-black px-6 md:px-8 py-4 md:py-4 text-base md:text-lg gilroy-text-bold font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95 w-full sm:w-auto min-h-[56px]">
                <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300 flex items-center justify-center">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </span>
                <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                <span className="absolute inset-0 flex items-center justify-center text-white gilroy-text-bold font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </span>
              </button>
            </Link>
            
            <button
              onClick={() => window.history.back()}
              className="border-2 border-white text-white px-6 md:px-8 py-4 md:py-4 text-base md:text-lg gilroy-text-bold font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95 w-full sm:w-auto min-h-[56px]"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300 flex items-center justify-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black gilroy-text-bold font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </span>
            </button>
          </div>

          {/* Contact Information */}
          <div className="border-t border-white/20 pt-12">
            <h3 className="gilroy-text-bold text-xl md:text-2xl font-black uppercase tracking-tight text-white mb-6">
              Need Help?
            </h3>
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8">
              <a 
                href="tel:+44123456789" 
                className="flex items-center justify-center text-gray-300 hover:text-white transition-colors gilroy-text"
              >
                <Phone className="h-4 w-4 mr-2" />
                Call Us
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center justify-center text-gray-300 hover:text-white transition-colors gilroy-text"
              >
                <Mail className="h-4 w-4 mr-2" />
                Email Us
              </a>
              <Link 
                href="/contact" 
                className="flex items-center justify-center text-gray-300 hover:text-white transition-colors gilroy-text"
              >
                <MapPin className="h-4 w-4 mr-2" />
                Visit Us
              </Link>
            </div>
          </div>

          {/* Tagline */}
          <div className="text-center">
            <p className="gilroy-text text-lg md:text-xl text-gray-400 italic">
              "The Campervan, Caravan and Motorhome Guys"
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 