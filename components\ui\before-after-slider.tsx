'use client'

import { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'

interface BeforeAfterSliderProps {
  beforeImage: string
  afterImage: string
  title: string
  beforeAlt?: string
  afterAlt?: string
  className?: string
  showTitle?: boolean
  onDragStart?: () => void
  onDragEnd?: () => void
}

export default function BeforeAfterSlider({
  beforeImage,
  afterImage,
  title,
  beforeAlt = 'Before image',
  afterAlt = 'After image',
  className = '',
  onDragStart,
  onDragEnd,
  showTitle = true
}: BeforeAfterSliderProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [sliderPosition, setSliderPosition] = useState(50)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const sliderRef = useRef<HTMLDivElement>(null)

  const handleMove = (clientX: number) => {
    if (!containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
    setSliderPosition(percentage)
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    onDragStart?.()
    handleMove(e.clientX)
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return
    e.preventDefault()
    e.stopPropagation()
    handleMove(e.clientX)
  }

  const handleMouseUp = (e: MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    onDragEnd?.()
  }

  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    onDragStart?.()
    handleMove(e.touches[0].clientX)
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging) return
    e.preventDefault()
    e.stopPropagation()
    handleMove(e.touches[0].clientX)
  }

  const handleTouchEnd = (e: TouchEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    onDragEnd?.()
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [isDragging])

  return (
    <Card className={`relative overflow-hidden group w-full ${className}`}>
      <div
        ref={containerRef}
        className={`relative w-full cursor-ew-resize select-none flex items-center justify-center ${className.includes('h-full') ? 'h-full' : 'h-[75vh]'}`}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        {/* After Image (Background) */}
        <div className="absolute inset-0">
          <Image
            src={afterImage}
            alt={afterAlt}
            fill
            className="object-contain object-center"
            onLoad={() => setIsLoaded(true)}
            priority
          />
        </div>

        {/* Before Image (Foreground with clipping) */}
        <div
          className="absolute inset-0 overflow-hidden"
          style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
        >
          <Image
            src={beforeImage}
            alt={beforeAlt}
            fill
            className="object-contain object-center"
            priority
          />
        </div>

        {/* Slider Line */}
        <div
          ref={sliderRef}
          className="absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-ew-resize z-10 transition-opacity duration-200"
          style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
        >
          {/* Slider Handle */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-gray-300 flex items-center justify-center cursor-ew-resize">
            <div className="w-3 h-3 border-l-2 border-r-2 border-gray-400"></div>
          </div>
        </div>

        {/* Labels */}
        <div className="absolute top-4 left-4 z-20">
          <Badge variant="destructive" className="bg-red-500 text-white font-medium">
            Before
          </Badge>
        </div>
        <div className="absolute top-4 right-4 z-20">
          <Badge variant="default" className="bg-green-500 text-white font-medium">
            After
          </Badge>
        </div>

        {/* Instructions overlay */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm">
            Drag to compare
          </div>
        </div>

        {/* Loading overlay */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-400">Loading comparison...</div>
          </div>
        )}
      </div>

      {showTitle && (
        <div className="p-4">
          <h3 className="font-semibold text-lg gilroy-text-bold">{title}</h3>
        </div>
      )}
    </Card>
  )
} 