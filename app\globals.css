/* Local Font Definitions */
@font-face {
  font-family: '<PERSON>roy';
  src: url('/fonts/Gilroy-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>roy';
  src: url('/fonts/Gilroy-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/fonts/Gilroy-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>roy';
  src: url('/fonts/Gilroy-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>roy';
  src: url('/fonts/Gilroy-Heavy.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SignPainter';
  src: url('/fonts/SignPainterHouseScript.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Gilroy', system-ui, -apple-system, sans-serif;
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --background-end-rgb: var(----background-end-rgb);
  --background-start-rgb: var(----background-start-rgb);
  --foreground-rgb: var(----foreground-rgb);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Gilroy', sans-serif;
  }

  /* Default text elements use Gilroy */
  p,
  span,
  div,
  input,
  textarea,
  select,
  button,
  label {
    font-family: 'Gilroy', sans-serif;
  }

  /* Headings use Gilroy by default (non-decorative) */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Gilroy', sans-serif;
  }

  /* Decorative headings use SignPainter */
  .hipster-heading {
    font-family: 'SignPainter', cursive !important;
  }

  /* Ensure all informational text uses Gilroy */
  .text-sm,
  .text-base,
  .text-lg,
  .text-xl,
  .text-2xl,
  .text-3xl,
  .text-4xl,
  .text-5xl,
  .text-6xl,
  .text-7xl {
    font-family: 'Gilroy', sans-serif;
  }
}

@layer components {
  .hipster-text {
    font-family: 'Gilroy', sans-serif;
    font-weight: 400;
    letter-spacing: 0.1em;
    text-transform: uppercase;
  }

  .gilroy-text {
    font-family: 'Gilroy', sans-serif;
    font-weight: 400;
  }

  .gilroy-text-medium {
    font-family: 'Gilroy', sans-serif;
    font-weight: 500;
  }

  .gilroy-text-bold {
    font-family: 'Gilroy', sans-serif;
    font-weight: 700;
  }

  .hipster-heading {
    font-family: 'SignPainter', cursive;
    font-weight: 400;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }

  .scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .scroll-reveal.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .btn-hipster {
    @apply px-8 py-3 font-medium uppercase tracking-wider transition-all duration-300;
    @apply border-2 border-black bg-transparent text-black;
    @apply hover:bg-black hover:text-white;
    font-family: 'Gilroy', sans-serif;
  }

  .btn-hipster-inverse {
    @apply px-8 py-3 font-medium uppercase tracking-wider transition-all duration-300;
    @apply border-2 border-white bg-black text-white;
    @apply hover:bg-white hover:text-black;
    font-family: 'Gilroy', sans-serif;
  }

  .service-card {
    @apply bg-black text-white p-6 h-full flex items-center justify-center text-center;
    @apply transition-all duration-300 hover:bg-gray-900 hover:scale-105;
    @apply border-2 border-transparent hover:border-white;
  }

  .hero-bg {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  }

  .section-divider {
    @apply w-24 h-1 bg-black mx-auto mb-8;
  }

  .contact-card {
    @apply bg-black text-white p-8 transition-all duration-300 hover:bg-gray-900;
    @apply border-2 border-transparent hover:border-gray-700;
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Brand Scroller Animations */
  .animate-scroll {
    animation: scroll 35s linear infinite;
  }

  .animate-scroll-reverse {
    animation: scroll-reverse 35s linear infinite;
  }

  @keyframes scroll {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(calc(-50% - 3rem));
    }
  }

  @keyframes scroll-reverse {
    0% {
      transform: translateX(calc(-50% - 3rem));
    }

    100% {
      transform: translateX(0);
    }
  }
}

/* =================================================================
Touch-Friendly Responsive Design - Mobile, Tablet & Desktop
================================================================= */

@layer components {

  /* Base responsive utilities */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improved button targets for touch */
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }

  /* Better form inputs for mobile */
  .input-touch {
    min-height: 44px;
    font-size: 16px;
    /* Prevents zoom on iOS */
    -webkit-appearance: none;
    appearance: none;
  }

  /* Large tablets and small desktops (up to 1024px) */
  @media (max-width: 1024px) {
    .hipster-heading {
      @apply text-3xl md:text-4xl;
    }

    .hipster-text {
      @apply text-sm md:text-base;
    }

    section {
      @apply py-16 md:py-20;
    }

    .container {
      @apply px-6;
    }

    /* Better touch targets */
    button,
    a {
      min-height: 44px;
      touch-action: manipulation;
    }
  }

  /* Tablets and large mobile devices (up to 768px) */
  @media (max-width: 768px) {

    /* Header adjustments */
    .header-mobile {
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }

    /* Typography scaling */
    .hipster-heading {
      @apply text-2xl sm:text-3xl;
      line-height: 1.1;
    }

    .hipster-text {
      @apply text-sm sm:text-base;
      line-height: 1.5;
    }

    /* Spacing adjustments */
    section {
      @apply py-12 sm:py-16;
    }

    .container {
      @apply px-4 sm:px-6;
    }

    /* Grid improvements */
    .grid-responsive {
      @apply grid-cols-1 gap-4;
    }

    /* Form improvements */
    input,
    textarea,
    select {
      font-size: 16px !important;
      /* Prevents iOS zoom */
      min-height: 44px;
    }

    /* Better image handling */
    img {
      height: auto;
      max-width: 100%;
    }
  }

  /* Mobile phones (up to 480px) */
  @media (max-width: 480px) {
    .hipster-heading {
      @apply text-xl;
      line-height: 1.2;
    }

    .hipster-text {
      @apply text-sm;
      line-height: 1.4;
    }

    /* Compact spacing */
    section {
      @apply py-10;
    }

    .container {
      @apply px-4;
    }

    /* Touch-optimized buttons */
    button {
      @apply px-6 py-3 text-sm;
      min-height: 48px;
      min-width: 48px;
    }

    /* Form field improvements */
    .form-field {
      @apply mb-4;
    }

    .form-field input,
    .form-field textarea,
    .form-field select {
      @apply w-full px-4 py-3 text-base;
      min-height: 48px;
      font-size: 16px !important;
    }

    /* Better card layouts */
    .card-mobile {
      @apply p-4 mb-4;
      border-radius: 8px;
    }
  }

  /* Extra small devices (up to 375px) */
  @media (max-width: 375px) {
    .hipster-heading {
      @apply text-lg;
    }

    .hipster-text {
      @apply text-xs;
    }

    .container {
      @apply px-3;
    }

    /* Compact buttons */
    button {
      @apply px-4 py-2 text-xs;
      min-height: 44px;
    }

    /* Tighter spacing */
    section {
      @apply py-8;
    }
  }

  /* Touch gesture improvements */
  @media (hover: none) and (pointer: coarse) {

    /* Hide hover effects on touch devices */
    .hover-effect:hover {
      transform: none !important;
      scale: none !important;
    }

    /* Better active states for touch */
    .touch-active:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }

    /* Improved button feedback */
    button:active,
    .btn:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }

  /* High DPI screens */
  @media (-webkit-min-device-pixel-ratio: 2),
  (min-resolution: 192dpi) {
    .hipster-text {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Landscape orientation on mobile */
  @media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
      min-height: 100vh;
    }

    .mobile-menu {
      padding-top: 60px;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {

    *,
    ::before,
    ::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Focus improvements for keyboard navigation */
  .focus-visible:focus-visible {
    outline: 2px solid #000;
    outline-offset: 2px;
  }

  /* Dark mode support for system preference */
  @media (prefers-color-scheme: dark) {
    .auto-dark {
      background-color: #111;
      color: #fff;
    }
  }
}

/* Custom scrollbar for webkit browsers */
@layer utilities {
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

/* TipTap Resizable Image Styles */
.resize-cursor {
  cursor: nw-resize;
}

img[data-type="resizable-image"] {
  transition: all 0.1s ease-in-out;
}

img[data-type="resizable-image"]:hover {
  cursor: pointer;
}

img[data-type="resizable-image"].ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Image resize handle styles */
.image-resizer {
  position: relative;
  display: inline-block;
}

.image-resizer .resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border: 2px solid #ffffff;
  border-radius: 3px;
  cursor: nw-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.image-resizer:hover .resize-handle,
.image-resizer .resize-handle:active {
  opacity: 1;
}

.image-resizer .resize-handle.bottom-right {
  bottom: -6px;
  right: -6px;
  cursor: nw-resize;
}

.image-resizer .resize-handle.bottom-left {
  bottom: -6px;
  left: -6px;
  cursor: ne-resize;
}

.image-resizer .resize-handle.top-right {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.image-resizer .resize-handle.top-left {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

/* Blog editor specific styles - Complete TipTap styling for rich text functionality */
.ProseMirror {
  font-family: 'Gilroy', sans-serif;
  font-size: 1.125rem;
  line-height: 1.7777778;
  color: #374151;
  max-width: none;
  min-height: 300px;
  outline: none;
}

.ProseMirror:focus {
  outline: none;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5em auto;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Typography styles for consistent rendering */
.ProseMirror h1 {
  font-family: 'Gilroy', sans-serif;
  font-size: 2.25rem;
  line-height: 1.1111111;
  font-weight: 800;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  color: #111827;
}

.ProseMirror h2 {
  font-family: 'Gilroy', sans-serif;
  font-size: 1.875rem;
  line-height: 1.3333333;
  font-weight: 700;
  margin-top: 1.5555556em;
  margin-bottom: 0.8888889em;
  color: #111827;
}

.ProseMirror h3 {
  font-family: 'Gilroy', sans-serif;
  font-size: 1.5rem;
  line-height: 1.3333333;
  font-weight: 600;
  margin-top: 1.5555556em;
  margin-bottom: 0.6666667em;
  color: #111827;
}

.ProseMirror p {
  font-family: 'Gilroy', sans-serif;
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  line-height: 1.7777778;
  color: #374151;
}

.ProseMirror ul {
  font-family: 'Gilroy', sans-serif;
  list-style-type: disc;
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.6666667em;
}

.ProseMirror ol {
  font-family: 'Gilroy', sans-serif;
  list-style-type: decimal;
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.6666667em;
}

.ProseMirror li {
  font-family: 'Gilroy', sans-serif;
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.7777778;
  color: #374151;
}

.ProseMirror ul ul,
.ProseMirror ol ol,
.ProseMirror ul ol,
.ProseMirror ol ul {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.ProseMirror blockquote {
  font-family: 'Gilroy', sans-serif;
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left-width: 0.25rem;
  border-left-color: #e5e7eb;
  quotes: "\201C" "\201D" "\2018" "\2019";
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
  padding-left: 1.1111111em;
}

.ProseMirror strong {
  font-family: 'Gilroy', sans-serif;
  color: #111827;
  font-weight: 600;
}

.ProseMirror em {
  font-family: 'Gilroy', sans-serif;
  color: #111827;
  font-style: italic;
}

.ProseMirror code {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  color: #111827;
  font-weight: 600;
  font-size: 0.8888889em;
  background-color: #f3f4f6;
  padding: 0.2222222em 0.4444444em;
  border-radius: 0.25rem;
}

.ProseMirror a {
  font-family: 'Gilroy', sans-serif;
  color: #111827;
  text-decoration: underline;
  font-weight: 500;
}

.ProseMirror a:hover {
  color: #374151;
}

/* Placeholder styling */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
  font-family: 'Gilroy', sans-serif;
}

/* Blog content styling - matches ProseMirror editor exactly */
.blog-content {
  font-family: 'Gilroy', sans-serif;
  font-size: 1.125rem;
  line-height: 1.7777778;
  color: #374151;
  max-width: none;
}

.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5em auto;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.blog-content h1 {
  font-family: 'Gilroy', sans-serif;
  font-size: 2.25rem;
  line-height: 1.1111111;
  font-weight: 800;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  color: #111827;
}

.blog-content h2 {
  font-family: 'Gilroy', sans-serif;
  font-size: 1.875rem;
  line-height: 1.3333333;
  font-weight: 700;
  margin-top: 1.5555556em;
  margin-bottom: 0.8888889em;
  color: #111827;
}

.blog-content h3 {
  font-family: 'Gilroy', sans-serif;
  font-size: 1.5rem;
  line-height: 1.3333333;
  font-weight: 600;
  margin-top: 1.5555556em;
  margin-bottom: 0.6666667em;
  color: #111827;
}

.blog-content p {
  font-family: 'Gilroy', sans-serif;
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  line-height: 1.7777778;
  color: #374151;
}

.blog-content ul {
  font-family: 'Gilroy', sans-serif;
  list-style-type: disc;
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.6666667em;
}

.blog-content ol {
  font-family: 'Gilroy', sans-serif;
  list-style-type: decimal;
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.6666667em;
}

.blog-content li {
  font-family: 'Gilroy', sans-serif;
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.7777778;
  color: #374151;
}

.blog-content ul ul,
.blog-content ol ol,
.blog-content ul ol,
.blog-content ol ul {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.blog-content blockquote {
  font-family: 'Gilroy', sans-serif;
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left-width: 0.25rem;
  border-left-color: #e5e7eb;
  quotes: "\201C" "\201D" "\2018" "\2019";
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
  padding-left: 1.1111111em;
}

.blog-content strong {
  font-family: 'Gilroy', sans-serif;
  color: #111827;
  font-weight: 600;
}

.blog-content em {
  font-family: 'Gilroy', sans-serif;
  color: #111827;
  font-style: italic;
}

.blog-content code {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  color: #111827;
  font-weight: 600;
  font-size: 0.8888889em;
  background-color: #f3f4f6;
  padding: 0.2222222em 0.4444444em;
  border-radius: 0.25rem;
}

.blog-content a {
  font-family: 'Gilroy', sans-serif;
  color: #111827;
  text-decoration: underline;
  font-weight: 500;
}

.blog-content a:hover {
  color: #374151;
}