'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface ResponsiveVideoProps {
  src: string;
  className?: string;
  aspectRatio?: 'video' | 'square' | 'wide' | 'auto';
  priority?: boolean;
  maxHeight?: string;
  muted?: boolean;
}

export const ResponsiveVideo: React.FC<ResponsiveVideoProps> = ({
  src,
  className = '',
  aspectRatio = 'auto',
  priority = false,
  maxHeight = '80vh',
  muted = false
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isInView, setIsInView] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [hasError, setHasError] = useState(false);
  const [isMetadataLoaded, setIsMetadataLoaded] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);

  // Detect if the browser cached metadata and loaded it before React attached event listeners
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (video.readyState >= 1) {
      // Metadata is already available
      if (aspectRatio === 'auto') {
        const ratio = video.videoWidth / video.videoHeight;
        if (ratio) {
          setVideoAspectRatio(ratio);
        }
      }
      setIsMetadataLoaded(true);
      setIsLoaded(video.readyState >= 3);
    }
  }, [aspectRatio]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            // Try to play if video has any data
            if (video.readyState >= 1 && video.paused) {
              video.play().catch((error) => {
                console.log('Autoplay failed for video:', src, error);
              });
            }
          } else {
            setIsInView(false);
            if (!video.paused) {
              video.pause();
            }
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: '50px'
      }
    );

    observer.observe(video);

    return () => {
      observer.disconnect();
    };
  }, [src]);

  const handleLoadedMetadata = () => {
    const video = videoRef.current;
    if (video) {
      setIsMetadataLoaded(true);
      if (aspectRatio === 'auto') {
        const ratio = video.videoWidth / video.videoHeight;
        setVideoAspectRatio(ratio);
      }
      setHasError(false);
    }
  };

  const handleLoadedData = () => {
    setIsLoaded(true);
    const video = videoRef.current;
    if (video && isInView && video.paused) {
      video.play().catch((error) => {
        console.log('Autoplay failed for video:', src, error);
      });
    }
  };

  const handleCanPlay = () => {
    setIsLoaded(true);
    const video = videoRef.current;
    if (video && isInView && video.paused) {
      video.play().catch((error) => {
        console.log('Autoplay failed for video:', src, error);
      });
    }
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(false);
    console.error('Video failed to load:', src);
  };

  const handleVideoClick = () => {
    const video = videoRef.current;
    if (video) {
      const newMutedState = !isMuted;
      setIsMuted(newMutedState);
      video.muted = newMutedState;
    }
  };

  const getAspectRatioClass = () => {
    if (aspectRatio === 'auto') {
      return ''; // We'll use inline styles for auto aspect ratio
    }
    
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case 'wide':
        return 'aspect-[21/9]';
      case 'video':
      default:
        return 'aspect-video';
    }
  };

  const getContainerStyle = () => {
    const baseStyle: React.CSSProperties = {
      maxHeight: maxHeight
    };

    // For auto aspect ratio, use a fallback if we don't have the ratio yet
    if (aspectRatio === 'auto') {
      if (videoAspectRatio) {
        const maxHeightValue = parseInt(maxHeight.replace('vh', ''));
        const viewportHeight = typeof window !== 'undefined' ? window.innerHeight : 800;
        const calculatedMaxHeight = (maxHeightValue / 100) * viewportHeight;
        
        return {
          ...baseStyle,
          aspectRatio: videoAspectRatio.toString(),
          width: '100%',
          maxWidth: `${calculatedMaxHeight * videoAspectRatio}px`
        };
      } else {
        // Fallback to 16:9 until we get the actual ratio
        return {
          ...baseStyle,
          aspectRatio: '16/9',
          width: '100%'
        };
      }
    }
    return baseStyle;
  };

  // Show video if metadata is loaded OR if we're using a fixed aspect ratio
  const shouldShowVideo = isMetadataLoaded || aspectRatio !== 'auto';

  return (
    <div 
      className={`relative w-full mx-auto ${getAspectRatioClass()} ${className}`}
      style={getContainerStyle()}
    >
      {/* Loading placeholder */}
      {!shouldShowVideo && !hasError && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse rounded-lg flex items-center justify-center z-10">
          <div className="text-gray-400 gilroy-text">Loading video...</div>
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center z-10">
          <div className="text-gray-500 text-center gilroy-text">
            <div className="mb-2">Failed to load video</div>
            <button 
              onClick={() => {
                setHasError(false);
                setIsLoaded(false);
                setIsMetadataLoaded(false);
                const video = videoRef.current;
                if (video) {
                  video.load();
                }
              }}
              className="text-sm text-blue-600 hover:text-blue-800 gilroy-text-medium"
            >
              Try again
            </button>
          </div>
        </div>
      )}
      
      <motion.video
        ref={videoRef}
        src={src}
        className={`w-full h-full object-cover rounded-lg transition-opacity duration-500 cursor-pointer ${
          shouldShowVideo ? 'opacity-100' : 'opacity-0'
        }`}
        muted={isMuted}
        loop
        playsInline
        preload={priority ? 'auto' : 'metadata'}
        onLoadedMetadata={handleLoadedMetadata}
        onLoadedData={handleLoadedData}
        onCanPlay={handleCanPlay}
        onError={handleError}
        onClick={handleVideoClick}
        style={{
          WebkitBackfaceVisibility: 'hidden',
          WebkitPerspective: 1000,
          WebkitTransform: 'translate3d(0, 0, 0)',
          transform: 'translate3d(0, 0, 0)',
          maxHeight: maxHeight
        }}
      />
      
      {/* Mute/Unmute indicator */}
      {shouldShowVideo && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
                     className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-2 rounded-full text-sm font-medium backdrop-blur-sm pointer-events-none gilroy-text-medium"
        >
          {isMuted ? '🔇 Click to unmute' : '🔊 Click to mute'}
        </motion.div>
      )}
      
      {/* Optional overlay for better text readability */}
      <div className="absolute inset-0 bg-black/10 rounded-lg pointer-events-none" />
    </div>
  );
};

export default ResponsiveVideo; 