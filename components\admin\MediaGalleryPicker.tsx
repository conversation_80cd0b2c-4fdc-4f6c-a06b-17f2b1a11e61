'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
    Search,
    Loader2,
    Image as ImageIcon,
    Eye,
    EyeOff,
    Check,
    X,
    Upload
} from 'lucide-react'
import Image from 'next/image'

interface GalleryImage {
    id: string
    title: string
    description?: string
    image_url: string
    alt_text?: string
    tags?: string[]
    is_public: boolean
    category?: string
    created_at: string
    file_size?: number
    file_type?: string
    order_index?: number
    is_featured?: boolean
    album_id?: string
}

interface MediaGalleryPickerProps {
    isOpen: boolean
    onClose: () => void
    onSelect: (image: GalleryImage) => void
    selectedImageId?: string
    mode?: 'select' | 'assign-to-album'
    albumId?: string
}

export default function MediaGalleryPicker({
    isOpen,
    onClose,
    onSelect,
    selectedImageId,
    mode = 'select',
    albumId
}: MediaGalleryPickerProps) {
    const [images, setImages] = useState<GalleryImage[]>([])
    const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState('')
    const [categoryFilter, setCategoryFilter] = useState<string>('all')
    const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null)
    const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set())
    const [showUpload, setShowUpload] = useState(false)
    const [uploading, setUploading] = useState(false)
    const [assigning, setAssigning] = useState(false)

    const supabase = createClient()

    const categories = [
        { value: 'all', label: 'All Categories' },
        { value: 'accident-repairs', label: 'Accident Repairs' },
        { value: 'resprays', label: 'Full Resprays' },
        { value: 'rust-treatment', label: 'Rust Treatment' },
        { value: 'raptor-paint', label: 'Raptor Paint' },
        { value: 'restoration', label: 'Restoration' },
        { value: 'custom-work', label: 'Custom Work' },
        { value: 'workshop', label: 'Workshop' },
        { value: 'before-after', label: 'Before & After' },
    ]

    const fetchImages = async () => {
        try {
            setIsLoading(true)
            let query = supabase
                .from('gallery_images')
                .select('*')
                .eq('is_public', true)
                .order('created_at', { ascending: false })

            // In assign mode, exclude images already in the target album
            if (mode === 'assign-to-album' && albumId) {
                query = query.or(`album_id.is.null,album_id.neq.${albumId}`)
            }

            const { data, error } = await query

            if (error) throw error
            setImages(data || [])
            setFilteredImages(data || [])
        } catch (error) {
            console.error('Error fetching images:', error)
        } finally {
            setIsLoading(false)
        }
    }

    useEffect(() => {
        if (isOpen) {
            fetchImages()
        }
    }, [isOpen])

    useEffect(() => {
        let filtered = images

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(image =>
                image.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                image.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                image.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
            )
        }

        // Filter by category
        if (categoryFilter !== 'all') {
            filtered = filtered.filter(image => image.category === categoryFilter)
        }

        setFilteredImages(filtered)
    }, [images, searchTerm, categoryFilter])

    const handleImageSelect = (image: GalleryImage) => {
        if (mode === 'assign-to-album') {
            // Toggle selection for multiple images
            setSelectedImages(prev => {
                const newSet = new Set(prev)
                if (newSet.has(image.id)) {
                    newSet.delete(image.id)
                } else {
                    newSet.add(image.id)
                }
                return newSet
            })
        } else {
            // Single selection for normal mode
            setSelectedImage(image)
        }
    }

    const handleConfirmSelection = () => {
        if (mode === 'assign-to-album') {
            handleAlbumAssignment()
        } else if (selectedImage) {
            onSelect(selectedImage)
            onClose()
            setSelectedImage(null)
        }
    }

    const handleAlbumAssignment = async () => {
        if (!albumId || selectedImages.size === 0) return

        setAssigning(true)
        try {
            const imageIds = Array.from(selectedImages)

            const { error } = await supabase
                .from('gallery_images')
                .update({ album_id: albumId })
                .in('id', imageIds)

            if (error) throw error

            // Refresh the images list
            await fetchImages()
            setSelectedImages(new Set())

            // Call onSelect with count of assigned images
            onSelect({ assigned: imageIds.length } as any)
            onClose()
        } catch (error) {
            console.error('Error assigning images to album:', error)
            alert('Failed to assign images to album')
        } finally {
            setAssigning(false)
        }
    }

    const handleClose = () => {
        onClose()
        setSelectedImage(null)
        setSelectedImages(new Set())
        setSearchTerm('')
        setCategoryFilter('all')
        setShowUpload(false)
    }

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (!file) return

        setUploading(true)

        try {
            const fileName = `${Date.now()}-${file.name}`
            const filePath = `gallery/${fileName}`

            const { error: uploadError } = await supabase.storage
                .from('gallery-images')
                .upload(filePath, file, {
                    cacheControl: '3600',
                    upsert: false
                })

            if (uploadError) throw uploadError

            const { data: { publicUrl } } = supabase.storage
                .from('gallery-images')
                .getPublicUrl(filePath)

            // Insert the image into the database
            const { data: insertedImage, error: insertError } = await supabase
                .from('gallery_images')
                .insert({
                    title: file.name.split('.')[0],
                    image_url: publicUrl,
                    alt_text: file.name.split('.')[0],
                    is_public: true,
                    file_size: file.size,
                    file_type: file.type
                })
                .select()
                .single()

            if (insertError) throw insertError

            const newImage: GalleryImage = insertedImage

            setImages(prev => [newImage, ...prev])
            setFilteredImages(prev => [newImage, ...prev])
            setSelectedImage(newImage)
            setShowUpload(false)
        } catch (error) {
            console.error('Error uploading image:', error)
            alert('Failed to upload image')
        } finally {
            setUploading(false)
        }
    }

    const formatFileSize = (bytes?: number) => {
        if (!bytes) return 'Unknown'
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(1024))
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-6xl h-[80vh] flex flex-col">
                <DialogHeader>
                    <DialogTitle className="gilroy-text-bold">
                        {mode === 'assign-to-album' ? 'Assign Images to Album' : 'Select Image from Gallery'}
                    </DialogTitle>
                    <DialogDescription>
                        {mode === 'assign-to-album'
                            ? 'Select multiple images to add to this album'
                            : 'Choose an image from your gallery to insert into the article'
                        }
                    </DialogDescription>
                </DialogHeader>

                {/* Filters and Upload */}
                <div className="flex flex-col sm:flex-row gap-4 py-4 border-b">
                    <div className="flex-1">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                placeholder="Search images by title, description, or tags..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                    </div>
                    <div className="sm:w-48">
                        <select
                            value={categoryFilter}
                            onChange={(e) => setCategoryFilter(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            {categories.map(category => (
                                <option key={category.value} value={category.value}>
                                    {category.label}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="sm:w-auto">
                        <Button
                            onClick={() => setShowUpload(true)}
                            variant="outline"
                            className="w-full"
                        >
                            <Upload className="h-4 w-4 mr-2" />
                            Upload New
                        </Button>
                    </div>
                </div>

                {/* Image Grid */}
                <div className="flex-1 overflow-y-auto">
                    {showUpload ? (
                        <div className="p-8">
                            <div className="max-w-md mx-auto">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Upload New Image</h3>
                                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-sm text-gray-600 mb-4">
                                        Select an image file to upload
                                    </p>
                                    <input
                                        type="file"
                                        accept="image/*"
                                        onChange={handleFileUpload}
                                        className="hidden"
                                        id="file-upload"
                                    />
                                    <Button
                                        onClick={() => document.getElementById('file-upload')?.click()}
                                        disabled={uploading}
                                        variant="outline"
                                    >
                                        {uploading ? (
                                            <>
                                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                Uploading...
                                            </>
                                        ) : (
                                            'Choose File'
                                        )}
                                    </Button>
                                    <Button
                                        onClick={() => setShowUpload(false)}
                                        variant="ghost"
                                        size="sm"
                                        className="mt-4"
                                    >
                                        Cancel
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ) : isLoading ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
                                <p className="text-gray-500">Loading images...</p>
                            </div>
                        </div>
                    ) : filteredImages.length === 0 ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <ImageIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No images found</h3>
                                <p className="text-gray-500">
                                    {searchTerm || categoryFilter !== 'all'
                                        ? 'Try adjusting your search or filters'
                                        : 'Upload some images to get started'
                                    }
                                </p>
                            </div>
                        </div>
                    ) : (
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4">
                            {filteredImages.map((image) => (
                                <div
                                    key={image.id}
                                    onClick={() => handleImageSelect(image)}
                                    className={`group relative bg-white rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${mode === 'assign-to-album'
                                        ? selectedImages.has(image.id)
                                            ? 'border-blue-500 ring-2 ring-blue-200'
                                            : 'border-gray-200 hover:border-gray-300'
                                        : selectedImage?.id === image.id
                                            ? 'border-blue-500 ring-2 ring-blue-200'
                                            : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                >
                                    {/* Selection indicator */}
                                    <div className="absolute top-2 left-2 z-10">
                                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${mode === 'assign-to-album'
                                            ? selectedImages.has(image.id)
                                                ? 'bg-blue-500 border-blue-500'
                                                : 'bg-white border-gray-300 group-hover:border-gray-400'
                                            : selectedImage?.id === image.id
                                                ? 'bg-blue-500 border-blue-500'
                                                : 'bg-white border-gray-300 group-hover:border-gray-400'
                                            }`}>
                                            {(mode === 'assign-to-album' ? selectedImages.has(image.id) : selectedImage?.id === image.id) && (
                                                <Check className="w-3 h-3 text-white" />
                                            )}
                                        </div>
                                    </div>

                                    {/* Image */}
                                    <div className="aspect-square relative overflow-hidden rounded-t-lg">
                                        {image.image_url ? (
                                            <Image
                                                src={image.image_url}
                                                alt={image.alt_text || image.title}
                                                fill
                                                className="object-cover group-hover:scale-105 transition-transform duration-200"
                                            />
                                        ) : (
                                            <div className="flex items-center justify-center h-full bg-gray-100">
                                                <ImageIcon className="h-8 w-8 text-gray-400" />
                                            </div>
                                        )}
                                    </div>

                                    {/* Image Info */}
                                    <div className="p-3">
                                        <h3 className="font-medium text-gray-900 text-sm truncate mb-1">
                                            {image.title}
                                        </h3>
                                        <div className="flex items-center gap-2 mb-2">
                                            {image.category && (
                                                <Badge variant="outline" className="text-xs">
                                                    {categories.find(c => c.value === image.category)?.label || image.category}
                                                </Badge>
                                            )}
                                            {image.is_featured && (
                                                <Badge variant="default" className="text-xs bg-yellow-100 text-yellow-800">
                                                    Featured
                                                </Badge>
                                            )}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                            {formatFileSize(image.file_size)}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Footer Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                    <div className="text-sm text-gray-500">
                        {mode === 'assign-to-album' ? (
                            <span>
                                {selectedImages.size > 0
                                    ? `${selectedImages.size} image${selectedImages.size > 1 ? 's' : ''} selected`
                                    : 'Click images to select them'
                                }
                            </span>
                        ) : selectedImage ? (
                            <span>Selected: <strong>{selectedImage.title}</strong></span>
                        ) : (
                            <span>Click an image to select it</span>
                        )}
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" onClick={handleClose}>
                            <X className="h-4 w-4 mr-2" />
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmSelection}
                            disabled={mode === 'assign-to-album' ? selectedImages.size === 0 : !selectedImage}
                            className="bg-blue-600 hover:bg-blue-700"
                        >
                            {assigning ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Assigning...
                                </>
                            ) : (
                                <>
                                    <Check className="h-4 w-4 mr-2" />
                                    {mode === 'assign-to-album' ? 'Assign to Album' : 'Insert Image'}
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}