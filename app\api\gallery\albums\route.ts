import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const includeImages = searchParams.get('include_images') === 'true';
        const publishedOnly = searchParams.get('published_only') !== 'false'; // Default to true

        const supabase = createClient();

        // Build the query
        let query = supabase
            .from('gallery_albums')
            .select('*')
            .order('order_index', { ascending: true })
            .order('created_at', { ascending: false });

        // Filter by published status if requested
        if (publishedOnly) {
            query = query.eq('is_published', true);
        }

        const { data: albums, error } = await query;

        if (error) {
            console.error('Error fetching albums:', error);
            return NextResponse.json({ error: 'Failed to fetch albums' }, { status: 500 });
        }

        // Set up base cache tags for albums list
        const baseTags = ['gallery-albums', 'albums-public']

        // If images are requested, fetch them for each album
        if (includeImages && albums) {
            const albumsWithImages = await Promise.all(
                albums.map(async (album) => {
                    const { data: images, error: imageError } = await supabase
                        .from('gallery_images')
                        .select('*')
                        .eq('album_id', album.id)
                        .order('order_index', { ascending: true })
                        .order('created_at', { ascending: false });

                    if (imageError) {
                        console.error(`Error fetching images for album ${album.id}:`, imageError);
                        return { ...album, images: [] };
                    }

                    return { ...album, images: images || [] };
                })
            );

            const resp = NextResponse.json({ albums: albumsWithImages });

            // Cache headers (24h TTL similar to images)
            resp.headers.set('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate=43200');
            resp.headers.set('CDN-Cache-Control', 'public, s-maxage=86400');
            resp.headers.set('Cache-Tag', baseTags.join(', '));

            return resp;
        }

        const resp = NextResponse.json({ albums: albums || [] });

        resp.headers.set('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate=43200');
        resp.headers.set('CDN-Cache-Control', 'public, s-maxage=86400');
        resp.headers.set('Cache-Tag', baseTags.join(', '));

        return resp;
    } catch (error) {
        console.error('Error in albums API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { title, description, slug, coverImageUrl, coverImageAlt, isFeatured, isPublished, orderIndex } = body;

        if (!title || !slug) {
            return NextResponse.json({ error: 'Title and slug are required' }, { status: 400 });
        }

        const supabase = createClient();

        // Check if slug already exists
        const { data: existingAlbum } = await supabase
            .from('gallery_albums')
            .select('id')
            .eq('slug', slug)
            .single();

        if (existingAlbum) {
            return NextResponse.json({ error: 'Album with this slug already exists' }, { status: 409 });
        }

        // Create the album
        const { data: album, error } = await supabase
            .from('gallery_albums')
            .insert({
                title,
                description: description || null,
                slug,
                cover_image_url: coverImageUrl || null,
                cover_image_alt: coverImageAlt || null,
                is_featured: isFeatured || false,
                is_published: isPublished !== false, // Default to true
                order_index: orderIndex || 0
            })
            .select()
            .single();

        if (error) {
            console.error('Error creating album:', error);
            return NextResponse.json({ error: 'Failed to create album' }, { status: 500 });
        }

        return NextResponse.json({ album });
    } catch (error) {
        console.error('Error in albums POST API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
} 