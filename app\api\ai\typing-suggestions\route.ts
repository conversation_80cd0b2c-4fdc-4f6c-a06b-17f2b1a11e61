import { NextRequest, NextResponse } from 'next/server'

// Helper function to convert common color values to human-readable descriptions
function getCommonColorDescription(colorValue: string): string | null {
  // Handle common hex colors
  if (colorValue?.startsWith('#')) {
    const commonColors: Record<string, string> = {
      '#000000': 'black',
      '#ffffff': 'white',
      '#ff0000': 'red',
      '#00ff00': 'green',
      '#0000ff': 'blue',
      '#ffff00': 'yellow',
      '#ff00ff': 'magenta',
      '#00ffff': 'cyan',
      '#c0c0c0': 'silver',
      '#808080': 'gray',
      '#800000': 'maroon',
      '#808000': 'olive',
      '#008000': 'dark green',
      '#800080': 'purple',
      '#008080': 'teal',
      '#000080': 'navy blue'
    }
    
    const lowerColor = colorValue.toLowerCase()
    if (commonColors[lowerColor]) {
      return commonColors[lowerColor]
    }
  }
  
  // Handle basic grayscale HSL colors
  if (colorValue?.startsWith('hsl(')) {
    const hslMatch = colorValue.match(/hsl\((\d+(?:\.\d+)?),\s*(\d+(?:\.\d+)?)%,\s*(\d+(?:\.\d+)?)%\)/)
    if (hslMatch) {
      const saturation = parseFloat(hslMatch[2])
      const lightness = parseFloat(hslMatch[3])
      
      // Only handle grayscale colors (low saturation)
      if (saturation < 10) {
        if (lightness > 95) return 'white'
        else if (lightness > 80) return 'light gray'
        else if (lightness > 60) return 'gray'
        else if (lightness > 30) return 'dark gray'
        else if (lightness > 5) return 'very dark gray'
        else return 'black'
      }
    }
  }
  
  // Return null if no common color match found
  return null
}

interface SuggestionContext {
  fullText: string
  currentLine: string
  lineNumber: number
  cursorPositionInLine: number
  totalLines: number
  previousLines: string[]
  nextLines: string[]
  isAtEndOfLine: boolean
  isAtEndOfText: boolean
  // Add contact context for intelligent suggestions
  contactContext?: {
    name: string
    email: string
    message: string
    serviceType?: string
    serviceQuestions?: Record<string, string>
    selectedColor?: string
    vehicleType?: string
    vehicleMake?: string
    vehicleModel?: string
    vehicleYear?: number
  }
}

export async function POST(request: NextRequest) {
  try {
    const { context }: { context: SuggestionContext } = await request.json()

    // Create enhanced prompt with business and contact context
    const createPrompt = (ctx: SuggestionContext) => {
      const { 
        fullText, 
        currentLine, 
        cursorPositionInLine, 
        previousLines, 
        contactContext
      } = ctx

      // Get context around current line
      const contextBefore = previousLines.slice(-2).join('\n') // Last 2 lines before current
      
      // Analyze current line content
      const linePrefix = currentLine.substring(0, cursorPositionInLine)
      const lastWords = linePrefix.trim().split(/\s+/).slice(-3).join(' ')
      
             // Build contact context information
       let customerInfo = ''
       if (contactContext) {
         const colorDescription = contactContext.selectedColor 
           ? getCommonColorDescription(contactContext.selectedColor) || `${contactContext.selectedColor} (describe in human terms)`
           : null
           
         const vehicleInfo = [
           contactContext.vehicleYear,
           contactContext.vehicleMake,
           contactContext.vehicleModel,
           contactContext.vehicleType && `(${contactContext.vehicleType})`
         ].filter(Boolean).join(' ')
           
         customerInfo = `
CUSTOMER CONTEXT:
- Customer: ${contactContext.name}
- Service: ${contactContext.serviceType || 'General inquiry'}
- Their message: "${contactContext.message}"
${colorDescription ? `- Selected color: ${colorDescription}` : ''}
${vehicleInfo ? `- Vehicle: ${vehicleInfo}` : ''}
${contactContext.serviceQuestions ? `- Details: ${Object.entries(contactContext.serviceQuestions).map(([key, value]) => `${key}: ${value}`).join(', ')}` : ''}
`
       }

      // Determine email stage and appropriate suggestions
      const emailStage = determineEmailStage(fullText, linePrefix)
      
      return `You are a professional customer service representative for JCC Customs and Commercial, specialists in campervan, caravan, and motorhome services including repairs, resprays, rust treatment, and conversions.

${customerInfo}

BUSINESS KNOWLEDGE:
- Services: Vehicle repairs, full resprays, color changes, rust treatment, Raptor coating, night heaters, interior conversions
- Expertise: High-quality workmanship, custom fabrication, insurance work
- Process: Quote → Schedule → Work → Quality check → Delivery
- Common needs: Color matching, paint codes, vehicle condition assessment, timeline estimates

CURRENT EMAIL CONTEXT:
Email stage: ${emailStage}
Previous context: ${contextBefore || '(email start)'}
Current line: "${currentLine}"
Text to continue: "${linePrefix}"
Last words: "${lastWords}"

TASK: Suggest 1-4 words that naturally continue from the cursor position. Consider:
1. The customer's specific needs and service type
2. Professional automotive service terminology
3. Natural conversation flow for this email stage
4. JCC's helpful, knowledgeable tone

RULES:
- NO colons (:) unless for time/ratios
- NO line breaks - stay on current line
- Match existing tone and style
- Use automotive/service industry terminology when appropriate
- Be specific to the customer's inquiry
- Avoid generic phrases - be contextually relevant

Current text to continue: "${linePrefix}"`
    }

    // Helper function to determine what stage of the email we're in
    const determineEmailStage = (fullText: string, linePrefix: string): string => {
      const lowerText = fullText.toLowerCase()
      const lowerPrefix = linePrefix.toLowerCase()
      
      if (lowerText.includes('dear ') || lowerText.includes('hello ') || lowerText.includes('hi ')) {
        if (lowerText.includes('thank')) return 'acknowledgment'
        return 'greeting'
      }
      
      if (lowerText.includes('quote') || lowerText.includes('estimate') || lowerText.includes('price')) {
        return 'pricing_discussion'
      }
      
      if (lowerText.includes('schedule') || lowerText.includes('appointment') || lowerText.includes('book')) {
        return 'scheduling'
      }
      
      if (lowerText.includes('best regards') || lowerText.includes('kind regards') || lowerText.includes('sincerely')) {
        return 'closing'
      }
      
      if (lowerPrefix.includes('we can') || lowerPrefix.includes('we would') || lowerPrefix.includes('we will')) {
        return 'service_offering'
      }
      
      if (lowerPrefix.includes('please') || lowerPrefix.includes('could you') || lowerPrefix.includes('would you')) {
        return 'request_information'
      }
      
      return 'main_content'
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: process.env.OPENROUTER_MODEL,
        messages: [
          {
            role: 'user',
            content: createPrompt(context)
          }
        ],
        max_tokens: 30,
        temperature: 0.4,
        top_p: 0.8,
        stop: ['\n', ':', '---', '```'],
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`)
    }

    const data = await response.json()
    let suggestion = data.choices?.[0]?.message?.content?.trim() || ''

    // Clean up the suggestion more aggressively
    suggestion = suggestion
      .replace(/^["']|["']$/g, '') // Remove quotes
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\n.*$/, '') // Remove everything after first newline
      .replace(/[:]{1,}.*$/, '') // Remove colons and everything after
      .replace(/^(and|or|but|so|the|a|an)\s+/i, '') // Remove common starting words
      .replace(/\s{2,}/g, ' ') // Replace multiple spaces with single space
      .substring(0, 50) // Limit length

    // Additional validation
    const isValidSuggestion = (text: string) => {
      if (!text || text.length < 1) return false
      if (text.includes('\n')) return false
      if (text.includes(':') && !text.match(/\d+:\d+/)) return false // Allow time format only
      if (text.length > 50) return false
      if (/^[^a-zA-Z0-9]/.test(text)) return false // Don't start with special chars
      // Avoid repeating previous line content
      const lastLine = context.previousLines?.[context.previousLines.length - 1]?.trim().toLowerCase() || ''
      if (lastLine && lastLine.startsWith(text.toLowerCase())) return false
      return true
    }

    // Only return if it's a meaningful and clean suggestion
    if (isValidSuggestion(suggestion)) {
      return NextResponse.json({ suggestion })
    }

    return NextResponse.json({ suggestion: '' })

  } catch (error) {
    console.error('Error in typing suggestions:', error)
    return NextResponse.json({ suggestion: '' }, { status: 500 })
  }
} 