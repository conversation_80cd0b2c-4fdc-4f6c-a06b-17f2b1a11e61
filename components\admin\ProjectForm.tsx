'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { createClient } from '@/lib/supabase'
import { projectSchema, type ProjectInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Save, Send } from 'lucide-react'

interface ProjectFormProps {
  project?: {
    id: string
    title: string
    description?: string
    customer_name: string
    customer_email: string
    customer_phone?: string
    status: 'planning' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled'
    progress_percentage: number
    estimated_completion?: string
    actual_completion?: string
    project_password?: string
  }
}

export default function ProjectForm({ project }: ProjectFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const supabase = createClient()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<ProjectInput>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      title: project?.title || '',
      description: project?.description || '',
      customerName: project?.customer_name || '',
      customerEmail: project?.customer_email || '',
      customerPhone: project?.customer_phone || '',
      status: project?.status || 'planning',
      progressPercentage: project?.progress_percentage || 0,
      estimatedCompletion: project?.estimated_completion ? 
        new Date(project.estimated_completion).toISOString().split('T')[0] : '',
      actualCompletion: project?.actual_completion ? 
        new Date(project.actual_completion).toISOString().split('T')[0] : ''
    }
  })

  const watchedStatus = watch('status')

  const onSubmit = async (data: ProjectInput) => {
    setLoading(true)
    setError('')

    try {
      const projectData = {
        title: data.title,
        description: data.description,
        customer_name: data.customerName,
        customer_email: data.customerEmail,
        customer_phone: data.customerPhone,
        status: data.status,
        progress_percentage: data.progressPercentage,
        estimated_completion: data.estimatedCompletion ? 
          new Date(data.estimatedCompletion).toISOString() : null,
        actual_completion: data.actualCompletion ? 
          new Date(data.actualCompletion).toISOString() : null,
        updated_at: new Date().toISOString()
      }

      if (project) {
        // Update existing project
        const { error: updateError } = await supabase
          .from('projects')
          .update(projectData)
          .eq('id', project.id)

        if (updateError) {
          setError('Failed to update project')
          return
        }
      } else {
        // Create new project
        const { error: insertError } = await supabase
          .from('projects')
          .insert({
            ...projectData,
            created_at: new Date().toISOString()
          })

        if (insertError) {
          setError('Failed to create project')
          return
        }
      }

      router.push('/admin/projects')
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }



  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Project Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Project Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Enter project title"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-600">{errors.title.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Enter project description"
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  value={watchedStatus} 
                  onValueChange={(value: 'planning' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled') => 
                    setValue('status', value)
                  }
                >
                  <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-600">{errors.status.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="progress">Progress (%)</Label>
                <Input
                  id="progress"
                  type="number"
                  min="0"
                  max="100"
                  {...register('progressPercentage', { valueAsNumber: true })}
                  className={errors.progressPercentage ? 'border-red-500' : ''}
                />
                {errors.progressPercentage && (
                  <p className="text-sm text-red-600">{errors.progressPercentage.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="estimated_completion">Estimated Completion</Label>
              <Input
                id="estimated_completion"
                type="date"
                {...register('estimatedCompletion')}
                className={errors.estimatedCompletion ? 'border-red-500' : ''}
              />
              {errors.estimatedCompletion && (
                <p className="text-sm text-red-600">{errors.estimatedCompletion.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Customer Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="customer_name">Customer Name *</Label>
              <Input
                id="customer_name"
                {...register('customerName')}
                placeholder="Enter customer name"
                className={errors.customerName ? 'border-red-500' : ''}
              />
              {errors.customerName && (
                <p className="text-sm text-red-600">{errors.customerName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer_email">Customer Email *</Label>
              <Input
                id="customer_email"
                type="email"
                {...register('customerEmail')}
                placeholder="Enter customer email"
                className={errors.customerEmail ? 'border-red-500' : ''}
              />
              {errors.customerEmail && (
                <p className="text-sm text-red-600">{errors.customerEmail.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer_phone">Customer Phone</Label>
              <Input
                id="customer_phone"
                {...register('customerPhone')}
                placeholder="Enter customer phone"
                className={errors.customerPhone ? 'border-red-500' : ''}
              />
              {errors.customerPhone && (
                <p className="text-sm text-red-600">{errors.customerPhone.message}</p>
              )}
            </div>

            {/* Customer Portal Access */}
            {project && (
              <div className="space-y-2">
                <Label>Customer Portal Access</Label>
                <div className="p-3 bg-gray-50 rounded-md">
                  {project.project_password ? (
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        Customer can access project progress at:
                      </p>
                      <code className="text-xs bg-white p-2 rounded border block">
                        {typeof window !== 'undefined' && `${window.location.origin}/project/${project.id}`}
                      </code>
                      <p className="text-xs text-gray-500">
                        Password: {project.project_password}
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (typeof window !== 'undefined') {
                            navigator.clipboard.writeText(
                              `${window.location.origin}/project/${project.id}`
                            )
                          }
                        }}
                      >
                        Copy Link
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        Customer portal access will be available after project creation
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {error && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push('/admin/projects')}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : project ? 'Update Project' : 'Create Project'}
        </Button>
      </div>
    </form>
  )
} 