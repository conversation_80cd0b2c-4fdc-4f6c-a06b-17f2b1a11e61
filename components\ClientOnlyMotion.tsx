'use client'

import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

interface ClientOnlyMotionProps {
  children: React.ReactNode
  className?: string
  initial?: any
  animate?: any
  transition?: any
  whileHover?: any
  whileTap?: any
  [key: string]: any
}

export default function ClientOnlyMotion({ 
  children, 
  className,
  initial,
  animate,
  transition,
  whileHover,
  whileTap,
  ...props 
}: ClientOnlyMotionProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      className={className}
      initial={initial}
      animate={animate}
      transition={transition}
      whileHover={whileHover}
      whileTap={whileTap}
      {...props}
    >
      {children}
    </motion.div>
  )
} 