'use client'

import { useState, useRef } from 'react'
import { createClient } from '@/lib/supabase'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
    Upload,
    Loader2,
    X,
    Image as ImageIcon,
    Check
} from 'lucide-react'
import Image from 'next/image'

interface BlogImageUploadProps {
    isOpen: boolean
    onClose: () => void
    onUpload: (imageData: { url: string; alt: string; title: string }) => void
}

export default function BlogImageUpload({
    isOpen,
    onClose,
    onUpload
}: BlogImageUploadProps) {
    const [file, setFile] = useState<File | null>(null)
    const [previewUrl, setPreviewUrl] = useState<string | null>(null)
    const [title, setTitle] = useState('')
    const [altText, setAltText] = useState('')
    const [isUploading, setIsUploading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const fileInputRef = useRef<HTMLInputElement>(null)

    const supabase = createClient()

    const handleFileSelect = (selectedFile: File) => {
        if (!selectedFile.type.startsWith('image/')) {
            setError('Please select only image files')
            return
        }
        if (selectedFile.size > 10 * 1024 * 1024) { // 10MB limit
            setError('Image size must be less than 10MB')
            return
        }

        setFile(selectedFile)
        setError(null)

        // Create preview URL
        const url = URL.createObjectURL(selectedFile)
        setPreviewUrl(url)

        // Auto-fill title from filename if empty
        if (!title) {
            const filename = selectedFile.name.replace(/\.[^/.]+$/, '')
            setTitle(filename.charAt(0).toUpperCase() + filename.slice(1))
        }

        // Auto-fill alt text if empty
        if (!altText) {
            setAltText(title || selectedFile.name.replace(/\.[^/.]+$/, ''))
        }
    }

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault()
        const droppedFiles = e.dataTransfer.files
        if (droppedFiles.length > 0) {
            handleFileSelect(droppedFiles[0])
        }
    }

    const handleUpload = async () => {
        if (!file) return

        setIsUploading(true)
        setError(null)

        try {
            // Generate unique filename
            const fileExtension = file.name.split('.').pop()
            const filename = `blog-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`

            // Upload to Supabase storage
            const { data, error: uploadError } = await supabase.storage
                .from('gallery-images') // Using the same bucket as gallery
                .upload(`blog/${filename}`, file, {
                    cacheControl: '3600',
                    upsert: false
                })

            if (uploadError) throw uploadError

            // Get public URL
            const { data: { publicUrl } } = supabase.storage
                .from('gallery-images')
                .getPublicUrl(`blog/${filename}`)

            // Call the onUpload callback with image data
            onUpload({
                url: publicUrl,
                alt: altText || title,
                title: title
            })

            // Reset form and close
            handleClose()
        } catch (err: any) {
            console.error('Upload error:', err)
            setError(err.message || 'Failed to upload image')
        } finally {
            setIsUploading(false)
        }
    }

    const handleClose = () => {
        onClose()
        setFile(null)
        setPreviewUrl(null)
        setTitle('')
        setAltText('')
        setError(null)
        if (previewUrl) {
            URL.revokeObjectURL(previewUrl)
        }
    }

    const removeFile = () => {
        setFile(null)
        if (previewUrl) {
            URL.revokeObjectURL(previewUrl)
            setPreviewUrl(null)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle className="gilroy-text-bold">Upload Image</DialogTitle>
                    <DialogDescription>
                        Upload a new image for your blog article
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                    {/* File Upload Area */}
                    <div className="space-y-4">
                        {!file ? (
                            <div
                                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                onClick={() => fileInputRef.current?.click()}
                            >
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
                                    className="hidden"
                                />
                                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                                <p className="text-lg font-medium text-gray-900 mb-2">
                                    Drop image here or click to select
                                </p>
                                <p className="text-sm text-gray-500">
                                    JPEG, PNG, WebP, GIF (max 10MB)
                                </p>
                            </div>
                        ) : (
                            <div className="relative border border-gray-200 rounded-lg p-4">
                                <button
                                    onClick={removeFile}
                                    className="absolute top-2 right-2 p-1 bg-red-100 hover:bg-red-200 rounded-full text-red-600 transition-colors z-10"
                                >
                                    <X className="h-4 w-4" />
                                </button>

                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-24 h-24 relative rounded-lg overflow-hidden bg-gray-100">
                                        {previewUrl && (
                                            <Image
                                                src={previewUrl}
                                                alt="Preview"
                                                fill
                                                className="object-cover"
                                            />
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="font-medium text-gray-900 truncate">
                                            {file.name}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {(file.size / 1024 / 1024).toFixed(2)} MB
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {file.type}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Image Details */}
                    {file && (
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="image-title" className="text-sm font-medium">
                                    Image Title
                                </Label>
                                <Input
                                    id="image-title"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    placeholder="Enter image title"
                                    className="mt-1"
                                />
                            </div>

                            <div>
                                <Label htmlFor="image-alt" className="text-sm font-medium">
                                    Alt Text (for accessibility)
                                </Label>
                                <Textarea
                                    id="image-alt"
                                    value={altText}
                                    onChange={(e) => setAltText(e.target.value)}
                                    placeholder="Describe the image for screen readers"
                                    rows={2}
                                    className="mt-1"
                                />
                            </div>
                        </div>
                    )}

                    {/* Error Message */}
                    {error && (
                        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-red-600 text-sm">{error}</p>
                        </div>
                    )}

                    {/* Footer Actions */}
                    <div className="flex items-center justify-end gap-3 pt-4 border-t">
                        <Button variant="outline" onClick={handleClose} disabled={isUploading}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleUpload}
                            disabled={!file || isUploading || !title.trim() || !altText.trim()}
                            className="bg-blue-600 hover:bg-blue-700"
                        >
                            {isUploading ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Uploading...
                                </>
                            ) : (
                                <>
                                    <Check className="h-4 w-4 mr-2" />
                                    Upload & Insert
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
} 