'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Menu, X, ArrowLeft, CheckCircle, Palette, Shield, Monitor, Camera, ShipWheel, ZoomIn, XIcon } from 'lucide-react';

import JCCStickyFooter from '@/components/footer';

// Services performed data
const servicesPerformed = [
  {
    title: 'Full Respray',
    description: 'Complete paint system transformation bringing this Peugeot Boxer back to showroom condition',
    icon: Palette
  },
  {
    title: 'Rust Treatment',
    description: 'Professional rust removal and prevention to ensure long-lasting protection',
    icon: Shield
  },
  {
    title: 'Body Repairs',
    description: 'Expert panel work and bodywork restoration for a flawless finish',
    icon: Shield
  },
  {
    title: 'Interior Upgrades',
    description: 'Modern interior enhancements for comfort and functionality',
    icon: Monitor
  },
  {
    title: 'Safety Features',
    description: 'Added safety equipment for peace of mind on every journey',
    icon: Camera
  },
  {
    title: 'Finishing Touches',
    description: 'Professional detailing and quality control for the perfect result',
    icon: ShipWheel
  }
];

// Feature highlights
const featureHighlights = [
  {
    title: 'Professional Finish',
    description: 'Expert craftsmanship delivering a showroom-quality paint finish that will last for years.'
  },
  {
    title: 'Rust Protection',
    description: 'Comprehensive rust treatment and prevention to protect your investment long-term.'
  },
  {
    title: 'Enhanced Value',
    description: 'Professional respray significantly increases vehicle value and extends its lifespan.'
  },
  {
    title: 'Custom Solutions',
    description: 'Tailored approach to meet your specific needs and requirements for the perfect result.'
  }
];

export default function PeugeotBoxerRespray() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Gallery images array
  const galleryImages = [
    '/images/sticky-scroll-component-images/1.png',
    '/images/sticky-scroll-component-images/2.jpeg',
    '/images/sticky-scroll-component-images/3.jpeg',
    '/images/sticky-scroll-component-images/4.jpeg',
    '/images/sticky-scroll-component-images/5.jpeg',
    '/images/sticky-scroll-component-images/6.jpeg',
    '/images/sticky-scroll-component-images/7.jpeg',
    '/images/sticky-scroll-component-images/8.jpeg',
    '/images/sticky-scroll-component-images/9.jpeg'
  ];

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      setIsScrolled(currentScrollY > 100);
      
      if (currentScrollY < 100) {
        setIsHeaderVisible(true);
      } else if (currentScrollY > lastScrollY) {
        setIsHeaderVisible(false);
      } else {
        setIsHeaderVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 transform ${
        isHeaderVisible ? 'translate-y-0' : '-translate-y-full'
      } ${
        isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-transparent'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20 md:h-24">
            {/* Left Navigation */}
            <nav className="hidden md:flex space-x-8 flex-1 justify-start">
              <Link 
                href="/repairs"
                className={`text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
              >
                Repairs
              </Link>
              <Link 
                href="/resprays"
                className={`text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
              >
                Resprays
              </Link>
              <Link 
                href="/rust"
                className={`text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
              >
                Rust
              </Link>
            </nav>

            {/* Logo - Centered */}
            <div className="flex-shrink-0 absolute left-1/2 transform -translate-x-1/2">
              <Link href="/">
                <Image
                  src="/images/logo-main.png"
                  alt="JCC Logo"
                  width={80}
                  height={80}
                  className="h-12 w-auto md:h-14 cursor-pointer"
                />
              </Link>
            </div>

            {/* Right Navigation */}
            <nav className="hidden md:flex space-x-8 flex-1 justify-end">
              <Link 
                href="/raptor"
                className={`text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
              >
                Raptor Paint
              </Link>
              <Link 
                href="/night-heaters"
                className={`text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
              >
                Night Heaters
              </Link>
              <Link 
                href="/#contact"
                className={`text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
              >
                Contact
              </Link>
            </nav>

            {/* Mobile Menu Button */}
            <button
              className={`md:hidden ${isScrolled ? 'text-black' : 'text-white'}`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className={`md:hidden border-t ${isScrolled ? 'bg-white' : 'bg-black/90 backdrop-blur-md'}`}>
            <div className="px-2 pt-2 pb-3 space-y-1">
              <Link
                href="/repairs"
                className={`block px-3 py-2 text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Repairs
              </Link>
              <Link
                href="/resprays"
                className={`block px-3 py-2 text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Resprays
              </Link>
              <Link
                href="/rust"
                className={`block px-3 py-2 text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Rust
              </Link>
              <Link
                href="/raptor"
                className={`block px-3 py-2 text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Raptor Paint
              </Link>
              <Link
                href="/night-heaters"
                className={`block px-3 py-2 text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Night Heaters
              </Link>
              <Link
                href="/#contact"
                className={`block px-3 py-2 text-base font-medium uppercase tracking-wider transition-colors ${
                  isScrolled ? 'text-black hover:text-gray-600' : 'text-white hover:text-gray-300'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Contact
              </Link>
            </div>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <div className="w-full h-full bg-black"></div>
        </div>
        
        {/* Grid Pattern Overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)] z-10"></div>
        
        <div className="relative z-20 text-center max-w-6xl mx-auto px-4">
          {/* Back Button */}
          <div className="mb-8">
            <Link 
              href="/resprays"
              className="inline-flex items-center space-x-3 text-white/80 hover:text-white transition-colors group"
            >
              <ArrowLeft size={20} className="group-hover:-translate-x-1 transition-transform" />
              <span className="hipster-text text-sm uppercase tracking-wider">Back to Resprays</span>
            </Link>
          </div>

          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold uppercase tracking-tight text-white mb-6 leading-none">
              Peugeot Boxer
              <br />
              <span className="text-gray-300">Professional Respray</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </div>
           
          <p className="text-2xl md:text-3xl font-light mb-6 max-w-4xl mx-auto leading-relaxed text-gray-300">
            Excellence in every detail!
          </p>
           
          <p className="text-xl md:text-2xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
            A complete professional respray transformation of this hardworking Peugeot Boxer. From rust treatment to showroom-quality finish - we brought this commercial vehicle back to its prime condition.
          </p>
           
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <button 
              onClick={() => scrollToSection('transformation')}
              className="bg-white text-black px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See The Transformation</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See The Transformation
              </span>
            </button>
            <Link 
              href="/#contact"
              className="border-2 border-white text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Start Your Project</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Start Your Project
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Project Overview */}
      <section className="py-32 bg-black text-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold uppercase tracking-tight mb-8 text-white">
              Professional Excellence
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-12"></div>
            <p className="text-2xl md:text-3xl font-light text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Excellence in every detail!
            </p>
          </div>

          {/* Content */}
          <div className="max-w-4xl mx-auto text-center space-y-12">
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed font-light">
              This hardworking Peugeot Boxer came to us looking tired and weathered from years of reliable service. The owner wanted to restore it to its former glory with a professional-grade respray.
            </p>
            
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed font-light">
              Our expert team at JCC delivered exactly what was needed - a complete transformation that not only looks incredible but also provides long-lasting protection for years of continued service.
            </p>

            {/* Services List */}
            <div className="pt-16">
              <h3 className="text-3xl md:text-4xl font-bold uppercase tracking-tight mb-12 text-white">
                Our Expert Work
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto">
                {servicesPerformed.map((service, index) => (
                  <div key={index} className="flex items-start space-x-4 text-left">
                    <div className="flex-shrink-0 w-8 h-8 bg-white text-black flex items-center justify-center mt-1">
                      <service.icon size={20} />
                    </div>
                    <div>
                      <h4 className="text-lg md:text-xl font-semibold text-white mb-2 tracking-normal">
                        {service.title}
                      </h4>
                      <p className="text-base md:text-lg text-gray-300 leading-relaxed font-normal">
                        {service.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Bottom Quote */}
            <div className="pt-16 border-t border-gray-700">
              <p className="text-2xl md:text-3xl font-light text-white italic">
                &ldquo;Professional quality that exceeds expectations.&rdquo;
              </p>
              <p className="text-lg text-gray-400 mt-4">
                Every step of the process was handled with care and attention to detail for the perfect finish.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Project Gallery - Masonry Layout */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          

          {/* Masonry Grid */}
          <div className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-4 space-y-4">
            {galleryImages.map((image, index) => (
              <div 
                key={index} 
                className="break-inside-avoid mb-4 group cursor-pointer relative overflow-hidden"
                onClick={() => setSelectedImage(image)}
              >
                <div className="relative">
                  <Image
                    src={image}
                    alt={`Peugeot Boxer respray ${index + 1}`}
                    width={400}
                    height={600}
                    className="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <ZoomIn size={32} className="text-white" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Full View Modal */}
        {selectedImage && (
          <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4">
            {/* Close Button - Fixed Position */}
            <button
              onClick={() => setSelectedImage(null)}
              className="fixed top-4 right-4 z-[60] bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors backdrop-blur-sm"
            >
              <XIcon size={24} />
            </button>
            
            {/* Image Container */}
            <div className="relative w-full h-full flex items-center justify-center">
              <Image
                src={selectedImage}
                alt="Peugeot Boxer respray detail"
                width={1200}
                height={800}
                className="max-w-[95vw] max-h-[95vh] w-auto h-auto object-contain"
                style={{
                  maxWidth: '95vw',
                  maxHeight: '95vh',
                  width: 'auto',
                  height: 'auto'
                }}
              />
            </div>
            
            {/* Click outside to close */}
            <div 
              className="absolute inset-0 z-40"
              onClick={() => setSelectedImage(null)}
            />
          </div>
        )}
      </section>

      {/* Feature Highlights */}
      <section className="py-20 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="hipster-heading text-4xl md:text-5xl font-black uppercase tracking-tight text-white mb-8">
              Built to Last
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-8"></div>
            <p className="hipster-text text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Every aspect of our respray process is designed for durability and long-lasting professional results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featureHighlights.map((feature, index) => (
              <div
                key={index}
                className="bg-white text-black p-8"
              >
                <h3 className="text-2xl md:text-3xl font-bold uppercase tracking-tight mb-4 text-black">
                  {feature.title}
                </h3>
                <p className="text-lg md:text-xl text-gray-700 leading-relaxed font-light">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          {/* Technical Specs */}
          <div className="mt-20 bg-white text-black p-12">
            <h3 className="text-3xl md:text-4xl font-bold uppercase tracking-tight mb-12 text-black text-center">
              Our Process
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
              <div>
                <h4 className="text-xl md:text-2xl font-bold uppercase tracking-tight mb-4 text-black">
                  Preparation
                </h4>
                <ul className="space-y-3">
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Complete surface preparation</span>
                  </li>
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Rust treatment and removal</span>
                  </li>
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Professional masking</span>
                  </li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-xl md:text-2xl font-bold uppercase tracking-tight mb-4 text-black">
                  Paint System
                </h4>
                <ul className="space-y-3">
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Premium primer application</span>
                  </li>
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Multiple base coat layers</span>
                  </li>
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>High-quality clear coat</span>
                  </li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-xl md:text-2xl font-bold uppercase tracking-tight mb-4 text-black">
                  Finishing
                </h4>
                <ul className="space-y-3">
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Professional polishing</span>
                  </li>
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Quality control inspection</span>
                  </li>
                  <li className="text-base md:text-lg flex items-start space-x-3 font-light">
                    <CheckCircle size={20} className="mt-1 flex-shrink-0" />
                    <span>Final detailing</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Before & After Comparison */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="hipster-heading text-4xl md:text-5xl font-black uppercase tracking-tight text-black mb-8">
              The Transformation
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="hipster-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              From tired and weathered to showroom fresh - see the incredible difference our professional respray makes.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Before */}
            <div className="bg-white p-8 shadow-lg">
              <h3 className="hipster-heading text-2xl font-black uppercase tracking-tight mb-6 text-black text-center">
                Before
              </h3>
              <div className="bg-gray-200 aspect-video flex items-center justify-center mb-6">
                <p className="hipster-text text-gray-500 text-center">
                  Before Images
                  <br />
                  <span className="text-sm">Coming Soon</span>
                </p>
              </div>
              <ul className="space-y-3">
                <li className="hipster-text text-gray-700">• Faded and weathered paint</li>
                <li className="hipster-text text-gray-700">• Surface rust and corrosion</li>
                <li className="hipster-text text-gray-700">• Scratches and minor damage</li>
                <li className="hipster-text text-gray-700">• Tired overall appearance</li>
                <li className="hipster-text text-gray-700">• Reduced vehicle value</li>
              </ul>
            </div>

            {/* After */}
            <div className="bg-black text-white p-8 shadow-lg">
              <h3 className="hipster-heading text-2xl font-black uppercase tracking-tight mb-6 text-white text-center">
                After
              </h3>
              <div className="bg-gray-700 aspect-video flex items-center justify-center mb-6">
                <p className="hipster-text text-gray-300 text-center">
                  After Images
                  <br />
                  <span className="text-sm">Coming Soon</span>
                </p>
              </div>
              <ul className="space-y-3">
                <li className="hipster-text text-gray-300">• Showroom-quality finish</li>
                <li className="hipster-text text-gray-300">• Complete rust protection</li>
                <li className="hipster-text text-gray-300">• Flawless surface condition</li>
                <li className="hipster-text text-gray-300">• Professional appearance</li>
                <li className="hipster-text text-gray-300">• Enhanced vehicle value</li>
              </ul>
            </div>
          </div>

          {/* Customer Satisfaction */}
          <div className="text-center bg-black text-white py-16 px-8">
            <h3 className="hipster-heading text-3xl md:text-4xl font-black uppercase tracking-tight mb-6 text-white">
              Professional Results
            </h3>
            <p className="hipster-text text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Our commitment to excellence shows in every detail. This Peugeot Boxer now looks better than the day it left the factory.
            </p>
            <div className="inline-block bg-white/10 px-8 py-4">
              <p className="hipster-text text-white font-bold text-lg">
                &ldquo;Outstanding quality and attention to detail!&rdquo;
              </p>
              <p className="hipster-text text-gray-300 text-sm mt-2">- Satisfied Peugeot Boxer Owner</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="hipster-heading text-4xl md:text-5xl font-black uppercase tracking-tight mb-8 text-black">
            Ready for Your Professional Respray?
          </h2>
          <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
          <p className="hipster-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Whether your vehicle needs a simple refresh or a complete transformation like this Peugeot Boxer, our expert team delivers professional results that exceed expectations.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link 
              href="/#contact"
              className="bg-black text-white px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Your Quote Today</span>
              <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Your Quote Today
              </span>
            </Link>
            
            <Link 
              href="/resprays"
              className="border-2 border-black text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">View More Projects</span>
              <div className="absolute inset-0 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                View More Projects
              </span>
            </Link>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
}
