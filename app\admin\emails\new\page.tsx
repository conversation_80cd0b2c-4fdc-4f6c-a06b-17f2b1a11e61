import { createServerSupabaseClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import EmailCampaignForm from '@/components/admin/EmailCampaignForm'

export default async function NewEmailCampaignPage() {
  const supabase = await createServerSupabaseClient()

  // Get subscriber count for reference
  const { count: subscriberCount } = await supabase
    .from('subscribers')
    .select('*', { count: 'exact', head: true })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <h1 className="text-3xl font-bold text-gray-900 gilroy-text-bold">Create Email Campaign</h1>
        <p className="text-gray-600 mt-2 gilroy-text">
          Create a new email campaign to send to your {subscriberCount || 0} subscribers
        </p>
      </div>

      {/* Form */}
      <EmailCampaignForm />
    </div>
  )
} 