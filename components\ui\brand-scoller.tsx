"use client";

export const BrandScroller = () => {
  const brands = [
    { name: "ADM Plumbing & Heating", logo: "/images/logos/adm-plumbing-and-heating-logo.png" },
    { name: "Adria", logo: "/images/logos/adria.svg" },
    { name: "Amazon Prime", logo: "/images/logos/amazon-prime-logo.png" },
    { name: "Auto Trail", logo: "/images/logos/Auto-Trail-logo.png" },
    { name: "Bailey of Bristol", logo: "/images/logos/bailey-of-bristol-logo.png" },
    { name: "BHA Housing", logo: "/images/logos/BHA-Housing-Social-Icon-Logo.png" },
    { name: "DHL", logo: "/images/logos/dhl.svg" },
    { name: "DPD", logo: "/images/logos/dpd-logo.svg" },
    { name: "NFU", logo: "/images/logos/nfu-logo.png" },
    { name: "Swift Caravans", logo: "/images/logos/swift-caravans.png" },
  ];

  return (
    <div className="relative w-[85vw] mx-auto h-24 py-8 overflow-hidden [mask-image:linear-gradient(to_right,transparent_0%,black_10%,black_90%,transparent_100%)]">
      <div className="absolute top-1/2 -translate-y-1/2 flex animate-scroll whitespace-nowrap">
        {brands.concat(brands).map((brand, index) => (
          <div key={index} className="inline-flex items-center justify-center w-40 h-20 mx-12 flex-shrink-0">
            <img
              src={brand.logo}
              alt={`${brand.name} logo`}
              className="max-w-full max-h-full object-contain filter grayscale invert opacity-70 hover:opacity-90 transition-opacity duration-300"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export const BrandScrollerReverse = () => {
  const brands = [
    { name: "ADM Plumbing & Heating", logo: "/images/logos/adm-plumbing-and-heating-logo.png" },
    { name: "Adria", logo: "/images/logos/adria.svg" },
    { name: "Amazon Prime", logo: "/images/logos/amazon-prime-logo.png" },
    { name: "Auto Trail", logo: "/images/logos/Auto-Trail-logo.png" },
    { name: "Bailey of Bristol", logo: "/images/logos/bailey-of-bristol-logo.png" },
    { name: "BHA Housing", logo: "/images/logos/BHA-Housing-Social-Icon-Logo.png" },
    { name: "DHL", logo: "/images/logos/dhl.svg" },
    { name: "DPD", logo: "/images/logos/dpd-logo.svg" },
    { name: "NFU", logo: "/images/logos/nfu-logo.png" },
    { name: "Swift Caravans", logo: "/images/logos/swift-caravans.png" },
  ];

  return (
    <div className="relative w-[85vw] mx-auto h-24 py-8 overflow-hidden [mask-image:linear-gradient(to_right,transparent_0%,black_10%,black_90%,transparent_100%)]">
      <div className="absolute top-1/2 -translate-y-1/2 flex animate-scroll-reverse whitespace-nowrap">
        {brands.concat(brands).map((brand, index) => (
          <div key={index} className="inline-flex items-center justify-center w-40 h-20 mx-12 flex-shrink-0">
            <img
              src={brand.logo}
              alt={`${brand.name} logo`}
              className="max-w-full max-h-full object-contain filter grayscale invert opacity-70 hover:opacity-90 transition-opacity duration-300"
            />
          </div>
        ))}
      </div>
    </div>
  );
};