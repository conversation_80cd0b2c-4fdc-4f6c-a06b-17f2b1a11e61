import { createServerSupabaseClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import ContactList from '@/components/admin/ContactList'
import { MessageSquare, Clock, CheckCircle, XCircle } from 'lucide-react'

export default async function ContactsPage() {
  const supabase = await createServerSupabaseClient()

  // Get all contacts with pagination
  const { data: contacts, error } = await supabase
    .from('contact_submissions')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching contacts:', error)
  }

  // Get stats
  const newCount = contacts?.filter(c => c.status === 'new').length || 0
  const respondedCount = contacts?.filter(c => c.status === 'responded').length || 0
  const totalCount = contacts?.length || 0

  const stats = [
    {
      name: 'Total Contacts',
      value: totalCount,
      icon: MessageSquare,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: 'New',
      value: newCount,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      name: 'Responded',
      value: respondedCount,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Contact Forms</h1>
        <p className="text-gray-600 mt-2">Manage customer inquiries and responses</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => (
          <Card key={stat.name}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Contact List */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Submissions</CardTitle>
          <CardDescription>View and respond to customer inquiries</CardDescription>
        </CardHeader>
        <CardContent>
          <ContactList contacts={contacts || []} />
        </CardContent>
      </Card>
    </div>
  )
} 