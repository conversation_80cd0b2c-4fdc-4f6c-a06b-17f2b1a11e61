import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { invalidateBlogCache } from '@/lib/cache-utils'

export async function POST(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { id } = await params

        if (!id) {
            return NextResponse.json({ error: 'Article ID is required' }, { status: 400 })
        }

        const supabase = await createServerSupabaseClient()

        // Check if article exists and is a draft
        const { data: article, error: fetchError } = await supabase
            .from('blog_articles')
            .select('id, status')
            .eq('id', id)
            .single()

        if (fetchError || !article) {
            return NextResponse.json({ error: 'Article not found' }, { status: 404 })
        }

        if (article.status === 'published') {
            return NextResponse.json({ error: 'Article is already published' }, { status: 400 })
        }

        // Update article to published status
        const { error: updateError } = await supabase
            .from('blog_articles')
            .update({
                status: 'published',
                published_at: new Date().toISOString()
            })
            .eq('id', id)

        if (updateError) {
            throw updateError
        }

        // Invalidate blog cache
        await invalidateBlogCache()

        return NextResponse.json({
            success: true,
            message: 'Article published successfully'
        })

    } catch (error: any) {
        console.error('Error publishing article:', error)
        return NextResponse.json(
            { error: error.message || 'Failed to publish article' },
            { status: 500 }
        )
    }
} 