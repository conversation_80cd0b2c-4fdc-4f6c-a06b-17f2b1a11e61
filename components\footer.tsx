"use client"
import { motion } from "framer-motion"
import Image from 'next/image'
import Link from 'next/link'


// Animation variants for reusability
const containerVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.6 },
  },
}

const linkVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 },
  },
}



const backgroundVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 2,
    },
  },
}

// JCC Footer data
const footerData = {
  sections: [
    { 
      title: "Services", 
      links: [
        { name: "Accident Repairs", href: "/repairs" },
        { name: "Full Resprays", href: "/resprays" },
        { name: "Rust Treatment", href: "/rust" },
        { name: "Raptor Paint", href: "/raptor" },
        { name: "Night Heaters", href: "/night-heaters" },
        { name: "Welding & Fabrication", href: "/rust" },
        { name: "Glass Fitting", href: "#contact" }
      ]
    },
    { 
      title: "Company", 
      links: [
        { name: "About JCC", href: "#about" },
        { name: "Our Story", href: "#about" },
        { name: "Awards", href: "#about" },
        { name: "Gallery", href: "#services" },
        { name: "Testimonials", href: "#contact" },
        { name: "FAQ", href: "#faq" }
      ]
    },
    { 
      title: "Contact", 
      links: [
        { name: "Get Quote", href: "#contact" },
        { name: "Book Appointment", href: "#contact" },
        { name: "WhatsApp", href: "#contact" },
        { name: "Location", href: "#contact" },
        { name: "Opening Hours", href: "#contact" }
      ]
    }
  ],
  title: "JCC Customs",
  subtitle: "Award-winning specialists",
  copyright: "©2024 JCC Customs & Commercials LTD",
}

// Reusable components
const NavSection = ({ title, links, index }: { title: string; links: { name: string; href: string }[]; index: number }) => (
  <motion.div variants={itemVariants} custom={index} className="flex flex-col gap-2">
    <motion.h3
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 + index * 0.1, duration: 0.5 }}
      className="mb-4 hipster-heading text-lg font-black uppercase tracking-wider text-white border-b border-white/20 pb-2 hover:text-gray-300 transition-colors duration-300"
    >
      {title}
    </motion.h3>
    {links.map((link, linkIndex) => (
      <motion.div
        key={linkIndex}
        variants={linkVariants}
        custom={linkIndex}
      >
        <Link
          href={link.href}
          className="hipster-text text-sm text-gray-300 hover:text-white transition-colors duration-300 group relative block"
        >
          <motion.span
            whileHover={{
              x: 8,
              transition: { type: "spring", stiffness: 300, damping: 20 },
            }}
            className="relative inline-block"
          >
            {link.name}
            <motion.span
              className="absolute bottom-0 left-0 h-0.5 bg-white"
              initial={{ width: 0 }}
              whileHover={{ width: "100%" }}
              transition={{ duration: 0.3 }}
            />
          </motion.span>
        </Link>
      </motion.div>
    ))}
  </motion.div>
)



export default function JCCStickyFooter() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="relative h-[70vh]" style={{ clipPath: "polygon(0% 0, 100% 0%, 100% 100%, 0 100%)" }}>
      <div className="relative h-[calc(100vh+70vh)] -top-[100vh]">
        <div className="h-[70vh] sticky top-[calc(100vh-70vh)]">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="bg-black py-6 md:py-12 px-4 md:px-12 h-full w-full flex flex-col justify-between relative overflow-hidden"
          >
            {/* Subtle Background Pattern */}
            <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff08_1px,transparent_1px),linear-gradient(to_bottom,#ffffff08_1px,transparent_1px)] bg-[size:60px_60px] pointer-events-none" />

            {/* Animated Background Elements */}
            <motion.div
              variants={backgroundVariants}
              className="absolute top-0 right-0 w-48 h-48 md:w-96 md:h-96 bg-white/5 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 4,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut",
              }}
            />

            <motion.div
              variants={backgroundVariants}
              className="absolute bottom-0 left-0 w-48 h-48 md:w-96 md:h-96 bg-white/5 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.05, 0.2, 0.05],
              }}
              transition={{
                duration: 5,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut",
                delay: 1,
              }}
            />

            {/* Navigation Section */}
            <motion.div variants={containerVariants} className="relative z-10">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-12">
                {/* Company Info */}
                <motion.div variants={itemVariants} className="md:col-span-1">
                  <p className="hipster-text text-sm text-gray-300 mb-6 leading-relaxed">
                    Award-winning specialists in campervan, caravan & motorhome repairs, resprays & restoration.
                  </p>
                </motion.div>

                {/* Services, Company, Contact columns */}
                {footerData.sections.map((section, index) => (
                  <NavSection key={section.title} title={section.title} links={section.links} index={index} />
                ))}
              </div>
            </motion.div>

            {/* Footer Bottom Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8, ease: "easeOut" }}
              className="flex flex-col lg:flex-row justify-between items-start lg:items-end relative z-10 gap-6 mt-8"
            >
              <div className="flex-1">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1, duration: 0.8, ease: "easeOut" }}
                  className="mb-6"
                >
                  <div className="bg-white p-4 rounded inline-block">
                    <Image
                      src="/images/logo-footer.png"
                      alt="JCC Logo"
                      width={120}
                      height={120}
                      className="h-20 w-auto"
                    />
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: "auto" }}
                  transition={{ delay: 1.2, duration: 0.6 }}
                  className="flex items-center gap-3 md:gap-4"
                >
                  <div className="w-8 md:w-12 h-0.5 bg-gradient-to-r from-white to-gray-400" />
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.4, duration: 0.5 }}
                    className="hipster-text text-gray-300 text-xs md:text-sm hover:text-white transition-colors duration-300"
                  >
                    {footerData.subtitle}
                  </motion.p>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.6, duration: 0.6 }}
                className="text-left lg:text-right"
              >
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.8, duration: 0.5 }}
                  className="hipster-text text-gray-400 text-xs md:text-sm mb-3 hover:text-white transition-colors duration-300"
                >
                  {footerData.copyright}
                </motion.p>

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2, duration: 0.5 }}
                  className="flex gap-4 text-left lg:justify-end"
                >
                  <button 
                    onClick={() => scrollToSection('contact')}
                    className="hipster-text text-xs text-gray-400 hover:text-white transition-colors"
                  >
                    Privacy Policy
                  </button>
                  <button 
                    onClick={() => scrollToSection('contact')}
                    className="hipster-text text-xs text-gray-400 hover:text-white transition-colors"
                  >
                    Terms of Service
                  </button>
                  <button 
                    onClick={() => scrollToSection('contact')}
                    className="hipster-text text-xs text-gray-400 hover:text-white transition-colors"
                  >
                    Get Quote
                  </button>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
