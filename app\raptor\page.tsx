'use client';

import { useState, useEffect } from 'react';


import Image from 'next/image';
import Link from 'next/link';
import { Shield, Zap, Droplets, CheckCircle, ArrowRight, Layers, Target } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import JCCStickyFooter from '@/components/footer';
import Header from '@/components/header';

// Raptor texture options
const textureOptions = [
  {
    id: 'smooth',
    name: 'Smooth Finish',
    description: 'Clean, sleek appearance with maximum protection',
    image: '/images/sticky-scroll-component-images/1.png',
    applications: ['Body panels', 'Bumpers', 'Professional vehicles'],
    durability: 95,
    coverage: 'Full body coating'
  },
  {
    id: 'textured',
    name: 'Textured Finish',
    description: 'Rugged grip surface for ultimate durability',
    image: '/images/sticky-scroll-component-images/4.jpeg',
    applications: ['Load beds', 'Wheel arches', 'Work vehicles'],
    durability: 100,
    coverage: 'Heavy-duty areas'
  },
  {
    id: 'fine',
    name: 'Fine Texture',
    description: 'Perfect balance of protection and appearance',
    image: '/images/sticky-scroll-component-images/7.jpeg',
    applications: ['Trim pieces', 'Side panels', 'Mixed applications'],
    durability: 90,
    coverage: 'Selective coating'
  }
];

// Protection benefits
const protectionBenefits = [
  {
    icon: Shield,
    title: 'Impact Protection',
    description: 'Absorbs stone chips and minor impacts that would damage regular paint',
    strength: 'Extreme'
  },
  {
    icon: Droplets,
    title: 'Weather Resistance',
    description: 'UV stable coating that won\'t fade, crack, or peel in harsh conditions',
    strength: 'Maximum'
  },
  {
    icon: Zap,
    title: 'Chemical Resistant',
    description: 'Resists fuel, oil, and cleaning chemicals that damage standard finishes',
    strength: 'Superior'
  },
  {
    icon: Layers,
    title: 'Scratch Resistant',
    description: 'Self-healing properties repair minor scratches and scuffs automatically',
    strength: 'Advanced'
  }
];

// Application areas with pricing
const applicationAreas = [
  {
    area: 'Bumpers Only',
    description: 'Front and rear bumper protection',
    timeframe: '1 day',
    price: 'From £350',
    includes: ['Surface prep', 'Raptor coating', 'Quality finish'],
    popular: false
  },
  {
    area: 'Wheel Arches & Trims',
    description: 'High-impact protection zones',
    timeframe: '2 days',
    price: 'From £650',
    includes: ['Detailed masking', 'Multi-coat application', 'Perfect edges'],
    popular: true
  },
  {
    area: 'Full Vehicle',
    description: 'Complete Raptor transformation',
    timeframe: '5-7 days',
    price: 'From £2000',
    includes: ['Full strip & prep', 'Professional application', 'Quality guarantee'],
    popular: false
  }
];

// Texture Comparison Component
const TextureComparison = ({ selectedTexture, onTextureSelect }: {
  selectedTexture: string | null,
  onTextureSelect: (id: string) => void
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {textureOptions.map((texture) => (
        <motion.div
          key={texture.id}
          className={`bg-white border-2 cursor-pointer transition-all duration-300 ${
            selectedTexture === texture.id 
              ? 'border-black shadow-2xl transform scale-105' 
              : 'border-gray-200 hover:border-gray-400 hover:shadow-lg'
          }`}
          onClick={() => onTextureSelect(texture.id)}
          whileHover={{ y: -5 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="relative h-48 overflow-hidden">
            <Image
              src={texture.image}
              alt={texture.name}
              fill
              className="object-cover"
            />
            <div className="absolute top-4 right-4 bg-black text-white px-3 py-1 rounded-full">
              <span className="gilroy-text-bold text-xs font-bold uppercase tracking-wider">
                {texture.durability}% TOUGH
              </span>
            </div>
          </div>
          
          <div className="p-6">
            <h3 className="gilroy-text-bold text-xl font-bold mb-2 text-black uppercase tracking-wider">
              {texture.name}
            </h3>
            <p className="gilroy-text text-gray-600 mb-4 text-sm leading-relaxed">
              {texture.description}
            </p>
            
            <div className="space-y-2">
              <div className="gilroy-text-bold text-xs text-gray-500 uppercase tracking-wider font-semibold">
                Best For:
              </div>
              {texture.applications.slice(0, 2).map((app, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-black rounded-full"></div>
                  <span className="gilroy-text text-sm text-gray-700">{app}</span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default function RaptorPage() {
  const [selectedTexture, setSelectedTexture] = useState<string | null>('textured');

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      <Header onContactClick={() => scrollToSection('contact')} />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80"
            alt="Rugged Vehicle with Protective Coating"
            fill
            className="object-cover"
            priority
          />
        </div>
        
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black/70 z-5"></div>
        
        <div className="relative z-20 text-center max-w-6xl mx-auto px-4">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >

            
            <h1 className="gilroy-text-bold text-6xl md:text-8xl lg:text-9xl font-black uppercase tracking-tight text-white mb-6 leading-none">
              Raptor
              <br />
              <span className="text-gray-400">Coating</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </motion.div>
           
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="gilroy-text text-xl md:text-2xl font-light mb-6 max-w-4xl mx-auto leading-relaxed text-gray-300"
          >
            Military-grade protection that laughs at stone chips
          </motion.p>
           
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="gilroy-text text-lg md:text-xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
          >
            Transform your vehicle into an unstoppable machine with Raptor's legendary protective coating.
          </motion.p>
           
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center"
          >
            <button 
              onClick={() => scrollToSection('textures')}
              className="gilroy-text-bold bg-white text-black px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See Textures</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See Textures
              </span>
            </button>
            <Link 
              href="/#contact"
              className="gilroy-text-bold border-2 border-white text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Quote</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Quote
              </span>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Interactive Texture Selection */}
      <section id="textures" className="py-32 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black">
              Choose Your Texture
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              From smooth professional finishes to rugged textured surfaces - pick the perfect Raptor coating for your needs
            </p>
          </div>

          <TextureComparison 
            selectedTexture={selectedTexture}
            onTextureSelect={setSelectedTexture}
          />

          {/* Detailed Texture Info */}
          <AnimatePresence mode="wait">
            {selectedTexture && (
              <motion.div
                key={selectedTexture}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.5 }}
                className="mt-16 bg-black text-white rounded-lg p-8 overflow-hidden"
              >
                {(() => {
                  const texture = textureOptions.find(t => t.id === selectedTexture);
                  if (!texture) return null;
                  
                  return (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      <div>
                        <h3 className="gilroy-text-bold text-3xl font-bold mb-4 text-white">{texture.name}</h3>
                        <p className="gilroy-text text-xl text-gray-300 mb-6 leading-relaxed">
                          {texture.description}
                        </p>
                        
                        <div className="mb-6">
                          <h4 className="gilroy-text-bold text-lg font-bold text-white uppercase tracking-wider">Perfect Applications:</h4>
                          <ul className="mt-2 space-y-1">
                            {texture.applications.map((app, index) => (
                              <li key={index} className="flex items-center space-x-2">
                                <CheckCircle size={16} className="text-white" />
                                <span className="gilroy-text text-gray-300">{app}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                      
                      <div className="bg-white/10 rounded-lg p-6">
                        <h4 className="gilroy-text-bold text-2xl font-bold mb-4 text-white">Specification</h4>
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Durability Rating:</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-24 bg-gray-700 h-2 rounded-full overflow-hidden">
                                <div 
                                  className="bg-white h-full transition-all duration-1000"
                                  style={{ width: `${texture.durability}%` }}
                                />
                              </div>
                              <span className="gilroy-text-bold text-white font-bold">{texture.durability}%</span>
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Coverage Type:</span>
                            <span className="gilroy-text-bold text-white font-semibold">{texture.coverage}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Finish Quality:</span>
                            <span className="gilroy-text-bold text-white font-semibold">Professional Grade</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="gilroy-text text-gray-300">Warranty:</span>
                            <span className="gilroy-text-bold text-white font-semibold">5 Year Coverage</span>
                          </div>
                        </div>
                        
                        <button 
                          onClick={() => scrollToSection('contact')}
                          className="gilroy-text-bold w-full mt-6 bg-white text-black py-3 px-6 font-bold uppercase tracking-wider relative overflow-hidden group"
                        >
                          <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get This Texture</span>
                          <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                          <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                            Get This Texture
                          </span>
                        </button>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* Protection Benefits Grid */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[80vh]">
            {/* Left Content */}
            <div className="bg-black text-white p-12 lg:p-16 flex flex-col justify-center">
              <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 leading-none text-white">
                Ultimate
                <br />
                Protection
              </h2>
              <div className="w-24 h-1 bg-white mx-auto mb-8"></div>
              
              <div className="space-y-6 mb-10">
                <p className="gilroy-text text-lg text-gray-300 leading-relaxed">
                  Raptor isn't just paint - it's armor for your vehicle. Used by military and commercial fleets worldwide, this polyurethane coating provides unmatched protection against everything life throws at your vehicle.
                </p>
                
                <p className="gilroy-text text-lg text-gray-300 leading-relaxed">
                  Whether you're protecting a work truck, upgrading a weekend warrior, or giving your daily driver that rugged look - Raptor delivers results that last.
                </p>
              </div>
              
              <div className="bg-white/10 rounded-lg p-6 mb-10">
                <h3 className="gilroy-text-bold text-xl font-bold mb-4 text-white uppercase tracking-wider">Why Choose Raptor?</h3>
                <ul className="space-y-2">
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>10x stronger than regular paint</span>
                  </li>
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>UV stable - won't fade or crack</span>
                  </li>
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>Chemical resistant coating</span>
                  </li>
                  <li className="gilroy-text text-gray-300 flex items-center space-x-2">
                    <CheckCircle size={16} className="text-white" />
                    <span>5-year warranty included</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Right Content - Vertically Stacked Sandwich Cards */}
            <div className="bg-gray-100 p-12 lg:p-16 flex flex-col justify-center">
              <div className="space-y-4 max-w-md mx-auto w-full">
                {protectionBenefits.map((benefit, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md hover:border-black transition-all duration-300 group overflow-hidden"
                    whileHover={{ scale: 1.02 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    {/* Top section with icon and title */}
                    <div className="bg-black text-white p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-white text-black rounded-full flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                          <benefit.icon size={20} />
                        </div>
                        <div className="flex-1">
                          <h3 className="gilroy-text text-lg font-bold text-white">{benefit.title}</h3>
                          <span className="gilroy-text text-xs text-gray-300 uppercase tracking-wider">{benefit.strength}</span>
                        </div>
                      </div>
                    </div>

                    {/* Bottom section with description */}
                    <div className="p-4 bg-white">
                      <p className="gilroy-text text-sm text-gray-600 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Application Areas & Pricing */}
      <section id="applications" className="py-32 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-white">
              Application Options
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-8"></div>
            <p className="gilroy-text text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Choose the perfect Raptor application for your vehicle and budget
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {applicationAreas.map((area, index) => (
              <motion.div
                key={index}
                className={`relative bg-white text-black p-8 transition-all duration-300 ${
                  area.popular ? 'ring-4 ring-white transform scale-105' : 'hover:transform hover:scale-102'
                }`}
                whileHover={{ y: -5 }}
              >
                {area.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-black text-white px-6 py-2 rounded-full">
                    <span className="gilroy-text-bold text-sm font-bold uppercase tracking-wider">Most Popular</span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="gilroy-text-bold text-2xl font-bold mb-2 text-black uppercase tracking-wider">
                    {area.area}
                  </h3>
                  <p className="gilroy-text text-gray-600 text-sm leading-relaxed">
                    {area.description}
                  </p>
                </div>

                <div className="my-6">
                  <div className="gilroy-text-bold text-4xl font-black text-black mb-2">
                    {area.price}
                  </div>
                  <div className="gilroy-text text-sm text-gray-500 uppercase tracking-wider">
                    {area.timeframe}
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="gilroy-text-bold text-sm font-bold text-black uppercase tracking-wider">Includes:</h4>
                  <ul className="mt-2 space-y-1">
                    {area.includes.map((item, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <CheckCircle size={14} className="text-black" />
                        <span className="gilroy-text text-sm text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <button className="gilroy-text-bold w-full bg-black text-white py-3 px-6 font-bold uppercase tracking-wider relative overflow-hidden group">
                  <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Choose This Option</span>
                  <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                  <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                    Choose This Option
                  </span>
                </button>
              </motion.div>
            ))}
          </div>

          <div className="mt-20 text-center bg-white/10 py-16 px-8 rounded-lg">
            <h3 className="gilroy-text-bold text-3xl md:text-4xl font-black uppercase tracking-tight mb-6 text-white">
              Ready For Raptor?
            </h3>
            <div className="w-20 h-1 bg-white mx-auto mb-8"></div>
            <p className="gilroy-text text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              Transform your vehicle into an unstoppable machine. Get your free Raptor quote today.
            </p>
            <Link 
              href="/#contact"
              className="gilroy-text-bold inline-flex items-center space-x-2 bg-white text-black px-8 py-4 font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Raptor Quote</span>
              <ArrowRight size={20} className="relative z-10 group-hover:opacity-0 transition-opacity duration-300" />
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Raptor Quote
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="gilroy-text-bold text-4xl md:text-5xl font-black uppercase tracking-tight mb-8 text-black">
            Ready To Go Raptor?
          </h2>
          <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
          <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Join thousands of satisfied customers who've upgraded their vehicles with Raptor's legendary protection
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link 
              href="/#contact"
              className="gilroy-text-bold bg-black text-white px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Your Quote</span>
              <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Your Quote
              </span>
            </Link>
            <Link 
              href="/gallery"
              className="gilroy-text-bold border-2 border-black text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See Examples</span>
              <div className="absolute inset-0 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See Examples
              </span>
            </Link>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
} 