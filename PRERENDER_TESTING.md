# Prerender.io Testing Guide

## Overview
This document explains how to test the Prerender.io integration on your JCC Customs and Commercial website.

## What is Prerender.io?
Prerender.io is a service that pre-renders your JavaScript website for search engines and social media bots, ensuring proper SEO indexing and social media link previews.

## How It Works
1. **For Bots**: When search engines (Google, Bing) or social media crawlers visit your site, they receive pre-rendered HTML content
2. **For Users**: Regular visitors receive the full interactive Next.js application
3. **Automatic Detection**: The middleware automatically detects bot traffic and serves appropriate content

## Testing the Integration

### Method 1: Using curl (Command Line)
Test with Googlebot user agent:
```bash
curl -H "User-Agent: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)" https://your-domain.com/test-prerender
```

Test with regular user agent:
```bash
curl -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" https://your-domain.com/test-prerender
```

### Method 2: Browser Developer Tools
1. Open Chrome DevTools (F12)
2. Go to Network tab
3. Click the gear icon and select "Use large request rows"
4. Go to Console tab and run:
```javascript
// Change user agent to Googlebot
Object.defineProperty(navigator, 'userAgent', {
  get: function () { return 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)'; },
  configurable: true
});
// Reload the page
location.reload();
```

### Method 3: Online Tools
Use tools like:
- [Google Search Console URL Inspection Tool](https://search.google.com/search-console)
- [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
- [Twitter Card Validator](https://cards-dev.twitter.com/validator)

## What to Look For

### Bot Response (Prerendered)
- Response should include `X-Prerendered: true` header
- HTML should be fully rendered with content visible in source
- No JavaScript execution required
- Meta tags should be properly populated

### User Response (Normal)
- No `X-Prerendered` header
- Interactive Next.js application
- Client-side JavaScript functionality works

## Test Pages
- **Main Test Page**: `/test-prerender`
- **Homepage**: `/`
- **Service Pages**: `/repairs`, `/resprays`, etc.

## Troubleshooting

### Common Issues
1. **No prerendering happening**: Check middleware logs in console
2. **Token errors**: Verify `PRERENDER_TOKEN` in environment variables
3. **Timeout errors**: Check network connectivity to Prerender.io service

### Debug Logs
The middleware logs prerender requests to the console:
```
Prerendering request for bot: [user-agent]
URL: [requested-url]
```

### Environment Variables
Ensure these are set in your `.env.local`:
```
PRERENDER_TOKEN=XADebKRlrtksKq04HnAS
```

## Configuration

### Bot User Agents Detected
The middleware detects these bots automatically:
- Googlebot
- Bingbot
- Facebookexternalhit
- Twitterbot
- LinkedInBot
- And many more...

### Excluded Routes
These routes are NOT prerendered:
- `/admin/*` - Admin panel
- `/api/*` - API routes
- `/_next/*` - Next.js internal files
- Static files (`.js`, `.css`, `.png`, etc.)

## Performance
- **Cache Duration**: 5 minutes on CDN
- **Timeout**: 10 seconds maximum
- **Fallback**: If prerender fails, normal page is served

## Support
If you encounter issues:
1. Check the console logs for error messages
2. Verify your Prerender.io account status
3. Test with the `/test-prerender` page first
4. Contact Prerender.io support if needed 