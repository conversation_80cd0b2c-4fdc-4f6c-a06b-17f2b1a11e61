'use client';
import { React<PERSON>enis } from 'lenis/react';
import React, { forwardRef } from 'react';
import { BrandScroller } from './ui/brand-scoller';

const JCCStickyScroll = forwardRef<HTMLElement>((props, ref) => {
  return (
    <ReactLenis root>
      <main className='bg-black' ref={ref}>
        <div className='wrapper'>
          {/* Hero section with responsive height */}
          <section className='text-white h-screen md:h-screen w-full bg-black grid place-content-center sticky top-0'>
            <div className='absolute bottom-0 left-0 right-0 top-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]'></div>

            <div className='relative z-10 text-center px-4 md:px-8'>
              <h1 className='text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-black uppercase tracking-tight leading-[120%] mb-4 md:mb-6 gilroy-text-bold'>
                Our Work
                <br />
                <span className='text-xl sm:text-2xl md:text-3xl lg:text-5xl font-light gilroy-text'>
                  Speaks For Itself
                </span>
              </h1>
              <p className='text-base sm:text-lg md:text-xl lg:text-2xl font-light opacity-80 max-w-2xl mx-auto mb-8 md:mb-12 leading-relaxed gilroy-text'>
                From accident repairs to full resprays, see the quality craftsmanship that makes JCC the trusted choice
              </p>
              
              {/* Trusted by section text */}
              <p className='text-xs sm:text-sm md:text-base font-light opacity-60 mb-4 md:mb-6 uppercase tracking-wider gilroy-text'>
                Who we&apos;ve worked with
              </p>
            </div>
            
            {/* Brand scroller in its own full-width container */}
            <div className='relative z-10 w-full'>
              <BrandScroller />
            </div>
          </section>
        </div>

        {/* Gallery Section - Different layouts for mobile vs desktop */}
        <section className='text-white w-full bg-black'>
          {/* Mobile Layout - Simple grid without sticky behavior */}
          <div className='block md:hidden p-4'>
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
              <figure className='w-full'>
                <img
                  src='/images/sticky-scroll-component-images/1.png'
                  alt='JCC Customs professional repair work'
                  className='transition-all duration-300 w-full h-64 sm:h-72 object-cover rounded-md'
                />
              </figure>
              <figure className='w-full'>
                <img
                  src='/images/sticky-scroll-component-images/2.jpeg'
                  alt='Quality campervan restoration by JCC'
                  className='transition-all duration-300 w-full h-64 sm:h-72 object-cover rounded-md'
                />
              </figure>
              <figure className='w-full'>
                <img
                  src='/images/sticky-scroll-component-images/4.jpeg'
                  alt='Award-winning respray work'
                  className='transition-all duration-300 w-full h-64 sm:h-72 object-cover rounded-md'
                />
              </figure>
              <figure className='w-full'>
                <img
                  src='/images/sticky-scroll-component-images/5.jpeg'
                  alt='Professional motorhome repairs'
                  className='transition-all duration-300 w-full h-64 sm:h-72 object-cover rounded-md'
                />
              </figure>
              <figure className='w-full'>
                <img
                  src='/images/sticky-scroll-component-images/7.jpeg'
                  alt='Rust repair and treatment expertise'
                  className='transition-all duration-300 w-full h-64 sm:h-72 object-cover rounded-md'
                />
              </figure>
              <figure className='w-full'>
                <img
                  src='/images/sticky-scroll-component-images/8.jpeg'
                  alt='Custom campervan interiors'
                  className='transition-all duration-300 w-full h-64 sm:h-72 object-cover rounded-md'
                />
              </figure>
            </div>
          </div>

          {/* Desktop Layout - Original sticky behavior preserved */}
          <div className='hidden md:block'>
            <div className='grid grid-cols-12 gap-2 p-4'>
              <div className='grid gap-2 col-span-4'>
                <figure className='w-full'>
                  <img
                    src='/images/sticky-scroll-component-images/1.png'
                    alt='JCC Customs professional repair work'
                    className='transition-all duration-300 w-full h-96 align-bottom object-cover rounded-md'
                  />
                </figure>
                <figure className='w-full'>
                  <img
                    src='/images/sticky-scroll-component-images/2.jpeg'
                    alt='Quality campervan restoration by JCC'
                    className='transition-all duration-300 w-full h-96 align-bottom object-cover rounded-md'
                  />
                </figure>
                <figure className='w-full'>
                  <img
                    src='/images/sticky-scroll-component-images/3.jpeg'
                    alt='Expert welding and fabrication services'
                    className='transition-all duration-300 w-full h-96 align-bottom object-cover rounded-md'
                  />
                </figure>
              </div>
              <div className='sticky top-0 h-screen w-full col-span-4 gap-2 grid grid-rows-3'>
                <figure className='w-full h-full'>
                  <img
                    src='/images/sticky-scroll-component-images/4.jpeg'
                    alt='Award-winning respray work'
                    className='transition-all duration-300 h-full w-full align-bottom object-cover rounded-md'
                  />
                </figure>
                <figure className='w-full h-full'>
                  <img
                    src='/images/sticky-scroll-component-images/5.jpeg'
                    alt='Professional motorhome repairs'
                    className='transition-all duration-300 h-full w-full align-bottom object-cover rounded-md'
                  />
                </figure>
                <figure className='w-full h-full'>
                  <img
                    src='/images/sticky-scroll-component-images/6.jpeg'
                    alt='Complete campervan restoration'
                    className='transition-all duration-300 h-full w-full align-bottom object-cover rounded-md'
                  />
                </figure>
              </div>
              <div className='grid gap-2 col-span-4'>
                <figure className='w-full'>
                  <img
                    src='/images/sticky-scroll-component-images/7.jpeg'
                    alt='Rust repair and treatment expertise'
                    className='transition-all duration-300 w-full h-96 align-bottom object-cover rounded-md'
                  />
                </figure>
                <figure className='w-full'>
                  <img
                    src='/images/sticky-scroll-component-images/8.jpeg'
                    alt='Custom campervan interiors'
                    className='transition-all duration-300 w-full h-96 align-bottom object-cover rounded-md'
                  />
                </figure>
                <figure className='w-full'>
                  <img
                    src='/images/sticky-scroll-component-images/9.jpeg'
                    alt='Professional accident damage repair'
                    className='transition-all duration-300 w-full h-96 align-bottom object-cover rounded-md'
                  />
                </figure>
              </div>
            </div>
          </div>
        </section>

        <footer className='group bg-black'>
          <div className='text-center py-12 md:py-16 px-4 md:px-8'>
            <h2 className='text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-black uppercase tracking-tight text-white mb-3 md:mb-4 leading-tight gilroy-text-bold'>
              Ready to Transform
              <br />
              Your Vehicle?
            </h2>
            <p className='text-base md:text-xl text-gray-300 mb-6 md:mb-8 max-w-2xl mx-auto leading-relaxed gilroy-text'>
              Get in touch today for a free quote on your campervan, caravan, or motorhome project
            </p>
            <button
              className='bg-white text-black px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-bold uppercase tracking-wider hover:bg-gray-200 transition-colors rounded-md touch-manipulation gilroy-text-bold'
              style={{ minHeight: '48px' }}
            >
              Get Your Quote
            </button>
          </div>
        </footer>
      </main>
    </ReactLenis>
  );
});

JCCStickyScroll.displayName = 'JCCStickyScroll';

export default JCCStickyScroll;