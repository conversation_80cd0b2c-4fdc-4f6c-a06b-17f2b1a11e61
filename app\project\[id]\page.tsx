import { createServerSupabaseClient } from '@/lib/supabase'
import { notFound } from 'next/navigation'
import CustomerPortal from '@/components/customer/CustomerPortal'
import PasswordProtection from '@/components/customer/PasswordProtection'

interface ProjectPortalPageProps {
  params: Promise<{
    id: string
  }>
  searchParams: Promise<{
    password?: string
  }>
}

export default async function ProjectPortalPage({ params, searchParams }: ProjectPortalPageProps) {
  const { id } = await params
  const { password } = await searchParams
  const supabase = await createServerSupabaseClient()

  // Get project details
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .single()

  if (error || !project) {
    notFound()
  }

  // Check if project has customer portal enabled
  if (!project.customer_portal_enabled) {
    notFound()
  }

  // Check password if provided
  const isPasswordCorrect = password === project.customer_portal_password

  if (!isPasswordCorrect) {
    return <PasswordProtection projectId={id} />
  }

  // Get project todos and updates
  const [
    { data: todos },
    { data: updates }
  ] = await Promise.all([
    supabase
      .from('project_todos')
      .select('*')
      .eq('project_id', id)
      .order('created_at', { ascending: false }),
    supabase
      .from('project_updates')
      .select('*')
      .eq('project_id', id)
      .order('created_at', { ascending: false })
  ])

  return (
    <CustomerPortal 
      project={project}
      todos={todos || []}
      updates={updates || []}
    />
  )
} 