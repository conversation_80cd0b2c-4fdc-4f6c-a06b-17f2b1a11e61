'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { createClient } from '@/lib/supabase'
import { contactResponseSchema, type ContactResponseInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'
import { 
  Bold, 
  Italic, 
  List, 
  ListOrdered, 
  Quote, 
  Undo, 
  Redo,
  Mail,
  Send,
  HelpCircle,
  Palette,
  Paperclip
} from 'lucide-react'

interface Contact {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  service_type?: string
  vehicle_type?: string
  message: string
  status: 'new' | 'responded' | 'archived'
  created_at: string
  admin_response?: string
  response_sent_at?: string
  // New fields for enhanced contact form
  service_questions?: Record<string, string>
  selected_color?: string
  attachments?: string[]
  attachment_urls?: string[]
}

interface ContactResponseProps {
  contact: Contact
  open: boolean
  onOpenChange: (open: boolean) => void
  onContactUpdate: (contact: Contact) => void
}

export default function ContactResponse({ 
  contact, 
  open, 
  onOpenChange, 
  onContactUpdate 
}: ContactResponseProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const supabase = createClient()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<ContactResponseInput>({
    resolver: zodResolver(contactResponseSchema),
    defaultValues: {
      subject: `Re: Your inquiry - ${contact.name}`,
      response: '',
      responseHtml: '',
    }
  })

  const watchedSubject = watch('subject')

  const editor = useEditor({
    extensions: [StarterKit],
    content: contact.admin_response || `
      <p>Dear ${contact.name},</p>
      <p>Thank you for your inquiry regarding our services. We appreciate you taking the time to contact JCC Customs and Commercial.</p>
      <p>We will get back to you shortly with more information.</p>
      <p>Best regards,<br>
      JCC Customs and Commercial Team</p>
    `,
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none min-h-[200px] p-4 border rounded-md',
      },
    },
  })

  const onSubmit = async (data: ContactResponseInput) => {
    if (!editor) return
    
    setLoading(true)
    setError('')

    try {
      const responseContent = editor.getHTML()
      
      // Update the form with the HTML content
      setValue('responseHtml', responseContent)
      
      // Update contact with response
      const { data: updatedContact, error: updateError } = await supabase
        .from('contact_submissions')
        .update({
          admin_response: responseContent,
          response_sent_at: new Date().toISOString(),
          status: 'responded',
          updated_at: new Date().toISOString()
        })
        .eq('id', contact.id)
        .select()
        .single()

      if (updateError) {
        setError('Failed to save response')
        return
      }

      // Send email (you would integrate with your email service here)
      // For now, we'll just log the email details
      const { error: emailError } = await supabase
        .from('email_logs')
        .insert({
          to_email: contact.email,
          subject: data.subject,
          content: responseContent,
          type: 'contact_response',
          contact_id: contact.id,
          sent_at: new Date().toISOString(),
          status: 'sent'
        })

      if (emailError) {
        console.error('Failed to log email:', emailError)
      }

      onContactUpdate(updatedContact)
      onOpenChange(false)
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const MenuBar = () => {
    if (!editor) return null

    return (
      <div className="flex items-center space-x-2 p-2 border-b">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-gray-200' : ''}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-gray-200' : ''}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}
        >
          <Quote className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-gray-300" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>Respond to {contact.name}</span>
          </DialogTitle>
          <DialogDescription>
            Send a response to this customer inquiry
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Customer Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Customer Inquiry</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Name:</span> {contact.name}
              </div>
              <div>
                <span className="font-medium">Email:</span> {contact.email}
              </div>
              {contact.phone && (
                <div>
                  <span className="font-medium">Phone:</span> {contact.phone}
                </div>
              )}
              {contact.service_type && (
                <div>
                  <span className="font-medium">Service:</span> {contact.service_type}
                </div>
              )}
            </div>
            <div className="mt-3">
              <span className="font-medium">Message:</span>
              <p className="mt-1 text-gray-700">{contact.message}</p>
            </div>
            
            {/* Service-specific questions */}
            {contact.service_questions && Object.keys(contact.service_questions).length > 0 && (
              <div className="mt-3">
                <span className="font-medium flex items-center">
                  <HelpCircle className="h-4 w-4 mr-1" />
                  Service Questions:
                </span>
                <div className="mt-2 space-y-2">
                  {Object.entries(contact.service_questions).map(([question, answer]) => (
                    <div key={question} className="bg-white p-2 rounded border">
                      <p className="text-xs font-medium text-gray-600 mb-1">
                        {question.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                      <p className="text-sm text-gray-800">{answer}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Color selection */}
            {contact.selected_color && (
              <div className="mt-3">
                <span className="font-medium flex items-center">
                  <Palette className="h-4 w-4 mr-1" />
                  Selected Color:
                </span>
                <div className="mt-1 flex items-center space-x-2">
                  <div 
                    className="w-6 h-6 border border-gray-300 rounded"
                    style={{ backgroundColor: contact.selected_color }}
                  />
                  <span className="text-sm text-gray-700 font-mono">{contact.selected_color}</span>
                </div>
              </div>
            )}
            
            {/* Attachments */}
            {contact.attachment_urls && contact.attachment_urls.length > 0 && (
              <div className="mt-3">
                <span className="font-medium flex items-center">
                  <Paperclip className="h-4 w-4 mr-1" />
                  Attachments ({contact.attachment_urls.length}):
                </span>
                <div className="mt-2 grid grid-cols-3 gap-2">
                  {contact.attachment_urls.map((url, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={url}
                        alt={contact.attachments?.[index] || `Attachment ${index + 1}`}
                        className="w-full h-16 object-cover rounded border border-gray-200 cursor-pointer hover:border-gray-400 transition-colors"
                        onClick={() => window.open(url, '_blank')}
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b truncate">
                        {contact.attachments?.[index] || `Image ${index + 1}`}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Email Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Email Subject</Label>
            <Input
              id="subject"
              {...register('subject')}
              placeholder="Enter email subject"
              className={errors.subject ? 'border-red-500' : ''}
            />
            {errors.subject && (
              <p className="text-sm text-red-600">{errors.subject.message}</p>
            )}
          </div>

          {/* Rich Text Editor */}
          <div className="space-y-2">
            <Label>Response Message</Label>
            <div className="border rounded-lg overflow-hidden">
              <MenuBar />
              <EditorContent editor={editor} />
            </div>
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={loading || !watchedSubject?.trim()}
            className="flex items-center space-x-2"
          >
            <Send className="h-4 w-4" />
            <span>{loading ? 'Sending...' : 'Send Response'}</span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 