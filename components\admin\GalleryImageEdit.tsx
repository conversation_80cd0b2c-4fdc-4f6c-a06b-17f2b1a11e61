'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Edit, X, Plus, Eye, EyeOff, Save } from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { invalidateGalleryCache } from '@/lib/cache-utils'
import { z } from 'zod'
import Image from 'next/image'

interface GalleryImage {
  id: string
  title: string
  description?: string
  image_url: string
  alt_text?: string
  tags?: string[]
  is_public: boolean
  category?: string
  created_at: string
  file_size?: number
  file_type?: string
  order_index?: number
  is_featured?: boolean
}

interface GalleryImageEditProps {
  image: GalleryImage
  onSuccess?: () => void
}

const categories = [
  { value: 'accident-repairs', label: 'Accident Repairs' },
  { value: 'resprays', label: 'Full Resprays' },
  { value: 'rust-treatment', label: 'Rust Treatment' },
  { value: 'raptor-paint', label: 'Raptor Paint' },
  { value: 'restoration', label: 'Restorations' },
  { value: 'custom-work', label: 'Custom Work' },
  { value: 'before-after', label: 'Before & After' },
  { value: 'workshop', label: 'Workshop' },
]

const editSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  altText: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean(),
  isFeatured: z.boolean(),
  orderIndex: z.number().min(0).optional(),
})

type EditFormData = z.infer<typeof editSchema>

export default function GalleryImageEdit({ image, onSuccess }: GalleryImageEditProps) {
  const router = useRouter()
  const supabase = createClient()
  
  const [isOpen, setIsOpen] = useState(false)
  const [tagInput, setTagInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<EditFormData>({
    resolver: zodResolver(editSchema),
    defaultValues: {
      title: image.title,
      description: image.description || '',
      altText: image.alt_text || '',
      category: image.category || '',
      tags: image.tags || [],
      isPublic: image.is_public,
      isFeatured: image.is_featured || false,
      orderIndex: image.order_index || 0,
    }
  })

  const watchedData = watch()

  const handleAddTag = () => {
    if (tagInput.trim() && !watchedData.tags?.includes(tagInput.trim())) {
      const newTags = [...(watchedData.tags || []), tagInput.trim()]
      setValue('tags', newTags)
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = watchedData.tags?.filter(tag => tag !== tagToRemove) || []
    setValue('tags', newTags)
  }

  const onSubmit = async (data: EditFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const { error: dbError } = await supabase
        .from('gallery_images')
        .update({
          title: data.title,
          description: data.description || null,
          alt_text: data.altText || null,
          category: data.category || null,
          tags: data.tags || null,
          is_public: data.isPublic,
          is_featured: data.isFeatured,
          order_index: data.orderIndex || 0,
          updated_at: new Date().toISOString(),
        })
        .eq('id', image.id)

      if (dbError) throw dbError

      // Invalidate gallery cache
      await invalidateGalleryCache(data.category)

      setIsOpen(false)
      onSuccess?.()
      router.refresh()
    } catch (error) {
      console.error('Error updating image:', error)
      setError('Failed to update image. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (open) {
      // Reset form with current image data when opening
      reset({
        title: image.title,
        description: image.description || '',
        altText: image.alt_text || '',
        category: image.category || '',
        tags: image.tags || [],
        isPublic: image.is_public,
        isFeatured: image.is_featured || false,
        orderIndex: image.order_index || 0,
      })
      setError('')
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-blue-600 hover:text-blue-700 gilroy-text"
        >
          <Edit className="h-3 w-3 mr-1" />
          Edit
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[95vh] overflow-y-auto m-2">
        <DialogHeader>
          <DialogTitle className="gilroy-text-bold">Edit Gallery Image</DialogTitle>
          <DialogDescription>
            Update the details for this gallery image. Changes will be reflected on your website immediately.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Image Preview */}
          <div className="space-y-2">
            <Label className="gilroy-text-medium">Current Image</Label>
            <div className="relative w-full h-48 md:h-64 bg-gray-100 rounded-lg overflow-hidden border">
              <Image
                src={image.image_url}
                alt={image.alt_text || image.title}
                fill
                className="object-cover"
              />
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="title" className="gilroy-text-medium">Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Enter image title"
                className={`gilroy-text ${errors.title ? 'border-red-500' : ''}`}
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
              )}
            </div>
            
            <div>
              <Label htmlFor="category" className="gilroy-text-medium">Category</Label>
              <Select value={watchedData.category || ''} onValueChange={(value) => setValue('category', value)}>
                <SelectTrigger className="gilroy-text">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description" className="gilroy-text-medium">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Describe the image and work shown"
              rows={3}
              className={`gilroy-text ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="altText" className="gilroy-text-medium">Alt Text</Label>
            <Input
              id="altText"
              {...register('altText')}
              placeholder="Describe the image for accessibility"
              className={`gilroy-text ${errors.altText ? 'border-red-500' : ''}`}
            />
            {errors.altText && (
              <p className="text-sm text-red-600 mt-1">{errors.altText.message}</p>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-3">
            <Label className="gilroy-text-medium">Tags</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                placeholder="Add a tag"
                className="gilroy-text flex-1"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddTag()
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleAddTag}
                disabled={!tagInput.trim()}
                className="gilroy-text-medium"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Tag
              </Button>
            </div>
            {watchedData.tags && watchedData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {watchedData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="gilroy-text-medium text-sm px-3 py-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-2 hover:text-red-600 transition-colors"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Settings */}
          <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
            <h3 className="gilroy-text-medium font-medium">Image Settings</h3>
            
            {/* Visibility */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Switch
                  checked={watchedData.isPublic}
                  onCheckedChange={(checked) => setValue('isPublic', checked)}
                />
                <Label className="gilroy-text-medium flex items-center">
                  {watchedData.isPublic ? (
                    <><Eye className="h-4 w-4 mr-2 text-green-600" />Public</>
                  ) : (
                    <><EyeOff className="h-4 w-4 mr-2 text-gray-600" />Private</>
                  )}
                </Label>
              </div>
              <Badge variant={watchedData.isPublic ? "default" : "secondary"} className="gilroy-text-medium">
                {watchedData.isPublic ? 'Visible on website' : 'Admin only'}
              </Badge>
            </div>

            {/* Featured */}
            <div className="flex items-center space-x-3">
              <Switch
                checked={watchedData.isFeatured}
                onCheckedChange={(checked) => setValue('isFeatured', checked)}
              />
              <Label className="gilroy-text-medium">Featured Image</Label>
            </div>

            {/* Order Index */}
            <div>
              <Label htmlFor="orderIndex" className="gilroy-text-medium">Display Order</Label>
              <Input
                id="orderIndex"
                type="number"
                {...register('orderIndex', { valueAsNumber: true })}
                placeholder="0"
                className="gilroy-text w-32"
                min="0"
              />
              <p className="text-xs text-gray-500 mt-1">Lower numbers appear first</p>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="gilroy-text flex-1 sm:flex-none"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="gilroy-text-medium flex-1 sm:flex-none"
            >
              {isLoading ? (
                <>
                  <Save className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 