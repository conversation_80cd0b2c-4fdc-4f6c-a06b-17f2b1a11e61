'use client';

import { useState } from 'react';


import Image from 'next/image';
import Link from 'next/link';
import { Phone, Mail, MapPin, Instagram, MessageCircle, Plus, Upload, X as XIcon, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '@/components/header';
import J<PERSON><PERSON>tickyScroll from '../components/sticky-scroll';
import { Gallery4 } from '@/components/gallery4';
import JCCStickyFooter from '@/components/footer';
import ResponsiveVideo from '@/components/ui/responsive-video';

// FAQ Data
const faqData = [
  {
    id: 1,
    question: "How much do you cost?",
    answer: "All depends on the job, we break down our estimates and quotes so you can see exactly what's costing what. We're a friendly bunch and happy for you to come down for a cup of tea and a free no-obligation estimate.",
    highlight: "cup of tea"
  },
  {
    id: 2,
    question: "Do you just do vans?",
    answer: "Nope! We do anything from classic cars, 4x4s, daily run arounds and motorbikes. We're passionate about what we do and love anything with wheels!",
    highlight: "classic cars, 4x4s, daily run arounds and motorbikes"
  },
  {
    id: 3,
    question: "How long does it take?",
    answer: "We book up pretty fast so get in touch sooner rather than later. Most small jobs are done in under 24 hours. Full resprays and restorations usually take 2-4 weeks.",
    highlight: "small jobs are done in under 24 hours"
  },
  {
    id: 4,
    question: "What do you guys do?",
    answer: "Probably easier to list what we don't do! We offer body work, paint work, welding, mechanical, electrical, custom work, glass replacement, campervan interiors, ply lining, night heaters, in-car entertainment and loads more.",
    highlight: "don't do!"
  },
  {
    id: 5,
    question: "Will my vehicle be safe?",
    answer: "Absolutely! We lock our car park at night, we've got CCTV and onsite security. Your vehicle will be in safe hands with us.",
    highlight: "CCTV and onsite security"
  },
  {
    id: 6,
    question: "Do you do glass?",
    answer: "Yep! Windscreen and camper glass - we can sort stone chips and leaky units too.",
    highlight: "windscreen and camper glass"
  }
];

// Form categories and their specific questions
const formCategories = [
  {
    id: 'general',
    label: 'General Inquiry',
    description: 'General questions about our services',
    requiresImages: false,
    questions: []
  },
  {
    id: 'accident-repair',
    label: 'Accident Repair',
    description: 'Collision damage and bodywork repairs',
    requiresImages: true,
    questions: [
      { id: 'incident-date', label: 'When did the incident occur?', type: 'date', required: true },
      { id: 'damage-description', label: 'Describe the damage', type: 'textarea', required: true },
      { id: 'insurance-claim', label: 'Are you making an insurance claim?', type: 'select', options: ['Yes', 'No', 'Not sure'], required: true },
      { id: 'vehicle-driveable', label: 'Is the vehicle still driveable?', type: 'select', options: ['Yes', 'No', 'With caution'], required: true }
    ]
  },
  {
    id: 'respray',
    label: 'Full Respray',
    description: 'Complete paint restoration services',
    requiresImages: true,
    questions: [
      { id: 'respray-type', label: 'Type of respray needed', type: 'select', options: ['Full vehicle', 'Partial (specify area)', 'Touch-up work'], required: true },
      { id: 'color-preference', label: 'Preferred color/finish', type: 'text', required: true },
      { id: 'current-condition', label: 'Current paint condition', type: 'select', options: ['Good', 'Fair', 'Poor', 'Severely damaged'], required: true },
      { id: 'timeline', label: 'Desired completion timeframe', type: 'select', options: ['ASAP', '1-2 weeks', '2-4 weeks', 'Flexible'], required: true }
    ]
  },
  {
    id: 'rust-treatment',
    label: 'Rust Treatment',
    description: 'Rust removal and prevention services',
    requiresImages: true,
    questions: [
      { id: 'rust-severity', label: 'How severe is the rust?', type: 'select', options: ['Surface rust', 'Moderate rust', 'Severe rust', 'Structural damage'], required: true },
      { id: 'rust-location', label: 'Where is the rust located?', type: 'textarea', required: true },
      { id: 'previous-treatment', label: 'Has this been treated before?', type: 'select', options: ['Yes', 'No', 'Not sure'], required: true }
    ]
  },
  {
    id: 'raptor-paint',
    label: 'Raptor Paint',
    description: 'Protective coating application',
    requiresImages: true,
    questions: [
      { id: 'coverage-area', label: 'What areas need coating?', type: 'textarea', required: true },
      { id: 'raptor-color', label: 'Preferred Raptor color', type: 'select', options: ['Black', 'Grey', 'White', 'Other (specify)'], required: true },
      { id: 'vehicle-use', label: 'How do you use your vehicle?', type: 'select', options: ['Daily driver', 'Off-road', 'Commercial', 'Weekend use'], required: true }
    ]
  },
  {
    id: 'welding',
    label: 'Welding & Fabrication',
    description: 'Structural repairs and custom work',
    requiresImages: true,
    questions: [
      { id: 'welding-type', label: 'Type of work needed', type: 'select', options: ['Structural repair', 'Custom fabrication', 'Patch work', 'Other'], required: true },
      { id: 'material-type', label: 'Material type', type: 'select', options: ['Steel', 'Aluminum', 'Stainless steel', 'Not sure'], required: true },
      { id: 'safety-critical', label: 'Is this safety-critical work?', type: 'select', options: ['Yes', 'No', 'Not sure'], required: true }
    ]
  },
  {
    id: 'restoration',
    label: 'Vehicle Restoration',
    description: 'Complete restoration services',
    requiresImages: true,
    questions: [
      { id: 'restoration-scope', label: 'Scope of restoration', type: 'select', options: ['Full restoration', 'Partial restoration', 'Specific components', 'Assessment needed'], required: true },
      { id: 'vehicle-age', label: 'Vehicle age/era', type: 'text', required: true },
      { id: 'budget-range', label: 'Budget range', type: 'select', options: ['Under £5,000', '£5,000-£15,000', '£15,000-£30,000', '£30,000+', 'Discuss'], required: true },
      { id: 'timeline-flexible', label: 'Timeline flexibility', type: 'select', options: ['Very flexible', 'Somewhat flexible', 'Fixed deadline'], required: true }
    ]
  }
];

// Touch-friendly Dynamic Contact Form Component
const DynamicContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'general',
    vehicleDetails: '',
    message: '',
    images: [] as File[],
    answers: {} as Record<string, string>
  });

  const [dragActive, setDragActive] = useState(false);
  const [errors, setErrors] = useState({} as Record<string, string>);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const selectedCategory = formCategories.find(cat => cat.id === formData.category);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAnswerChange = (questionId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      answers: { ...prev.answers, [questionId]: value }
    }));
  };

  const handleImageUpload = (files: FileList | null) => {
    if (!files) return;
    
    const newImages = Array.from(files).filter(file => {
      const isImage = file.type.startsWith('image/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB limit
      return isImage && isValidSize;
    });

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...newImages].slice(0, 6) // Max 6 images
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    handleImageUpload(e.dataTransfer.files);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Valid email is required';

    if (selectedCategory?.requiresImages && formData.images.length === 0) {
      newErrors.images = 'At least one image is required for this service';
    }

    if (formData.category !== 'general' && !formData.vehicleDetails.trim()) {
      newErrors.vehicleDetails = 'Vehicle details are required';
    }

    // Validate category-specific questions
    selectedCategory?.questions.forEach(question => {
      if (question.required && !formData.answers[question.id]) {
        newErrors[question.id] = `${question.label} is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Handle form submission
      console.log('Form submitted:', formData);
      // Here you would typically send the data to your backend
      alert('Form submitted successfully! We\'ll get back to you soon.');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 md:space-y-8">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
        <div>
          <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
            Your Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            onFocus={() => setFocusedField('name')}
            onBlur={() => setFocusedField(null)}
            placeholder="Enter your name"
            className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors.name ? 'border-red-500' : focusedField === 'name' ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white transition-all duration-200 touch-manipulation`}
            style={{ fontSize: '16px' }} // Prevents zoom on iOS
          />
          {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
        </div>
        <div>
          <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
            Your Email *
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            onFocus={() => setFocusedField('email')}
            onBlur={() => setFocusedField(null)}
            placeholder="Enter your email"
            className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors.email ? 'border-red-500' : focusedField === 'email' ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white transition-all duration-200 touch-manipulation`}
            style={{ fontSize: '16px' }} // Prevents zoom on iOS
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
        </div>
      </div>

      <div>
        <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
          Phone Number
        </label>
        <input
          type="tel"
          value={formData.phone}
          onChange={(e) => handleInputChange('phone', e.target.value)}
          onFocus={() => setFocusedField('phone')}
          onBlur={() => setFocusedField(null)}
          placeholder="Enter your phone number"
          className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${focusedField === 'phone' ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white transition-all duration-200 touch-manipulation`}
          style={{ fontSize: '16px' }} // Prevents zoom on iOS
        />
      </div>

      {/* Service Category */}
      <div>
        <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
          What can we help you with? *
        </label>
        <div className="relative">
          <select
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
            onFocus={() => setFocusedField('category')}
            onBlur={() => setFocusedField(null)}
            className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${focusedField === 'category' ? 'border-black' : 'border-gray-200'} text-black focus:outline-none focus:border-black focus:bg-white appearance-none transition-all duration-200 touch-manipulation cursor-pointer`}
            style={{ fontSize: '16px' }} // Prevents zoom on iOS
          >
            {formCategories.map(category => (
              <option key={category.id} value={category.id}>
                {category.label} - {category.description}
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={20} />
        </div>
      </div>

      {/* Vehicle Details - Required for non-general inquiries */}
      {formData.category !== 'general' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
            Vehicle Details *
          </label>
          <input
            type="text"
            value={formData.vehicleDetails}
            onChange={(e) => handleInputChange('vehicleDetails', e.target.value)}
            onFocus={() => setFocusedField('vehicleDetails')}
            onBlur={() => setFocusedField(null)}
            placeholder="e.g., 2018 Ford Transit, VW Crafter, etc."
            className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors.vehicleDetails ? 'border-red-500' : focusedField === 'vehicleDetails' ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white transition-all duration-200 touch-manipulation`}
            style={{ fontSize: '16px' }} // Prevents zoom on iOS
          />
          {errors.vehicleDetails && <p className="text-red-500 text-sm mt-1">{errors.vehicleDetails}</p>}
        </motion.div>
      )}

      {/* Category-specific Questions */}
      <AnimatePresence>
        {selectedCategory?.questions.map(question => (
          <motion.div
            key={question.id}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
              {question.label} {question.required && '*'}
            </label>
            {question.type === 'select' ? (
              <div className="relative">
                <select
                  value={formData.answers[question.id] || ''}
                  onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                  onFocus={() => setFocusedField(question.id)}
                  onBlur={() => setFocusedField(null)}
                  className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors[question.id] ? 'border-red-500' : focusedField === question.id ? 'border-black' : 'border-gray-200'} text-black focus:outline-none focus:border-black focus:bg-white appearance-none transition-all duration-200 touch-manipulation cursor-pointer`}
                  style={{ fontSize: '16px' }} // Prevents zoom on iOS
                >
                  <option value="">Select an option</option>
                  {question.options?.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={20} />
              </div>
            ) : question.type === 'textarea' ? (
              <textarea
                value={formData.answers[question.id] || ''}
                onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                onFocus={() => setFocusedField(question.id)}
                onBlur={() => setFocusedField(null)}
                rows={3}
                className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors[question.id] ? 'border-red-500' : focusedField === question.id ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white resize-none transition-all duration-200 touch-manipulation`}
                placeholder={`Enter ${question.label.toLowerCase()}`}
                style={{ fontSize: '16px' }} // Prevents zoom on iOS
              />
            ) : question.type === 'date' ? (
              <input
                type="date"
                value={formData.answers[question.id] || ''}
                onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                onFocus={() => setFocusedField(question.id)}
                onBlur={() => setFocusedField(null)}
                className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors[question.id] ? 'border-red-500' : focusedField === question.id ? 'border-black' : 'border-gray-200'} text-black focus:outline-none focus:border-black focus:bg-white transition-all duration-200 touch-manipulation`}
                style={{ fontSize: '16px' }} // Prevents zoom on iOS
              />
            ) : (
              <input
                type="text"
                value={formData.answers[question.id] || ''}
                onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                onFocus={() => setFocusedField(question.id)}
                onBlur={() => setFocusedField(null)}
                className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${errors[question.id] ? 'border-red-500' : focusedField === question.id ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white transition-all duration-200 touch-manipulation`}
                placeholder={`Enter ${question.label.toLowerCase()}`}
                style={{ fontSize: '16px' }} // Prevents zoom on iOS
              />
            )}
            {errors[question.id] && <p className="text-red-500 text-sm mt-1">{errors[question.id]}</p>}
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Image Upload - Required for non-general inquiries */}
      <AnimatePresence>
        {selectedCategory?.requiresImages && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
              Upload Images * (Max 6 images, 10MB each)
            </label>
            <div
              className={`border-2 border-dashed ${dragActive ? 'border-black bg-gray-50' : errors.images ? 'border-red-500' : 'border-gray-300'} rounded-lg p-6 md:p-8 text-center transition-colors touch-manipulation`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="mx-auto h-10 w-10 md:h-12 md:w-12 text-gray-400 mb-4" />
              <p className="text-gray-600 mb-4 text-sm md:text-base">
                Drag and drop images here, or{' '}
                <label className="text-black font-bold cursor-pointer hover:underline touch-manipulation">
                  browse files
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />
                </label>
              </p>
              <p className="text-xs md:text-sm text-gray-500">
                Just snap some clear pics of the damage or area that needs work
              </p>
            </div>
            {errors.images && <p className="text-red-500 text-sm mt-1">{errors.images}</p>}
            
            {/* Image Preview */}
            {formData.images.length > 0 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-3 md:gap-4"
              >
                {formData.images.map((image, index) => (
                  <motion.div
                    key={index}
                    className="relative"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <img
                      src={URL.createObjectURL(image)}
                      alt={`Upload ${index + 1}`}
                      className="w-full h-20 md:h-24 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors touch-manipulation"
                      style={{ minWidth: '32px', minHeight: '32px' }} // Better touch target
                    >
                      <XIcon size={14} />
                    </button>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Additional Message */}
      <div>
        <label className="block gilroy-text text-sm font-bold text-gray-700 uppercase tracking-wider mb-3">
          Additional Details
        </label>
        <textarea
          value={formData.message}
          onChange={(e) => handleInputChange('message', e.target.value)}
          onFocus={() => setFocusedField('message')}
          onBlur={() => setFocusedField(null)}
          placeholder="Any additional information you'd like to share..."
          rows={4}
          className={`w-full px-4 py-4 md:py-4 text-base md:text-base bg-gray-50 border-2 ${focusedField === 'message' ? 'border-black' : 'border-gray-200'} text-black placeholder-gray-500 focus:outline-none focus:border-black focus:bg-white resize-none transition-all duration-200 touch-manipulation`}
          style={{ fontSize: '16px' }} // Prevents zoom on iOS
        />
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        className="w-full bg-black text-white px-8 py-5 md:py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
        style={{ minHeight: '56px' }} // Better touch target
      >
        <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">
          {selectedCategory?.requiresImages ? 'Get My Quote' : 'Send It Over'}
        </span>
        <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
        <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
          {selectedCategory?.requiresImages ? 'Get My Quote' : 'Send It Over'}
        </span>
      </button>

      {/* Form Info */}
      <div className="text-center text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
        <p className="mb-2">
          <strong>Response Time:</strong> We&apos;ll get back to you within 24 hours
        </p>
        {selectedCategory?.requiresImages && (
          <p>
            <strong>What happens next:</strong> We&apos;ll take a look at your pics and details to give you a proper estimate
          </p>
        )}
      </div>
    </form>
  );
};

// Touch-friendly FAQ Accordion Item Component
const FAQItem = ({ item, isOpen, onToggle }: {
  item: typeof faqData[0],
  isOpen: boolean,
  onToggle: () => void
}) => {
  const renderAnswer = (text: string, highlight: string) => {
    if (!highlight) return text;
    
    const parts = text.split(highlight);
    return (
      <>
        {parts[0]}
        <span className="font-bold text-black">{highlight}</span>
        {parts[1]}
      </>
    );
  };

  return (
    <motion.div
      initial={false}
      className="border-b border-gray-200 last:border-b-0"
    >
      <button
        onClick={onToggle}
        className="w-full text-left py-6 md:py-8 px-6 md:px-8 lg:px-12 flex items-center justify-between group hover:bg-gray-50 transition-colors duration-200 touch-manipulation active:bg-gray-100"
        style={{ minHeight: '72px' }} // Better touch target
      >
        <div className="flex items-center space-x-4 md:space-x-6 flex-1">
          <motion.div
            className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-black text-white flex items-center justify-center font-bold text-sm md:text-lg"
            animate={{
              backgroundColor: isOpen ? "#000" : "#000",
              color: isOpen ? "#fff" : "#fff"
            }}
            transition={{ duration: 0.3 }}
          >
            {String(item.id).padStart(2, '0')}
          </motion.div>
          <h3 className="gilroy-text-bold text-lg md:text-xl lg:text-2xl xl:text-3xl font-black uppercase tracking-tight text-black group-hover:text-gray-700 transition-colors duration-200 leading-tight">
            {item.question}
          </h3>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="flex-shrink-0 ml-2 md:ml-4 p-2" // Add padding for better touch target
        >
          <Plus size={20} className="md:w-6 md:h-6 text-black" />
        </motion.div>
      </button>
      
      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.4,
              ease: "easeInOut",
              opacity: { duration: 0.3 }
            }}
            className="overflow-hidden"
          >
            <motion.div
              initial={{ y: -10 }}
              animate={{ y: 0 }}
              exit={{ y: -10 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="px-6 md:px-8 lg:px-12 pb-6 md:pb-8"
            >
              <div className="ml-14 md:ml-18 pl-4 md:pl-6 border-l-2 border-gray-200">
                <p className="gilroy-text text-base md:text-lg text-gray-700 leading-relaxed">
                  {renderAnswer(item.answer, item.highlight)}
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default function Home() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Header */}
      <Header onContactClick={() => scrollToSection('contact')} />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <img
            src="/images/hero-image.png"
            alt="Campervan workshop background"
            className="w-full h-full object-cover"
          />
        </div>
        
        {/* Grid Pattern Overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)] z-10"></div>
        
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/50 to-black/80 z-10"></div>
        
        <div className="relative z-20 text-center max-w-5xl mx-auto px-4">
          <motion.div 
            className="mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div 
              className="mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Image
                src="/images/logo-main.png"
                alt="JCC Customs & Commercials"
                width={600}
                height={200}
                className="w-auto h-24 md:h-32 lg:h-40 mx-auto max-w-full"
              />
            </motion.div>
            <div className="w-24 h-1 bg-white mx-auto my-6"></div>
          </motion.div>
         
          <motion.p 
            className="gilroy-text text-xl md:text-3xl font-light mb-4 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Your friendly campervan, caravan & motorhome specialists
          </motion.p>
         
          <motion.p 
            className="gilroy-text text-lg md:text-xl text-gray-300 mb-12 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            Award-winning repairs, resprays & restorations right here in Norwich
          </motion.p>
         
          <motion.div
            className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center px-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <button
              onClick={() => scrollToSection('services')}
              className="bg-white text-black px-6 md:px-8 py-4 md:py-4 text-base md:text-lg font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
              style={{ minHeight: '56px' }} // Better touch target
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">What We Do</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                What We Do
              </span>
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="border-2 border-white text-white px-6 md:px-8 py-4 md:py-4 text-base md:text-lg font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
              style={{ minHeight: '56px' }} // Better touch target
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get A Quote</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get A Quote
              </span>
            </button>
          </motion.div>
        </div>
      </section>

      {/* Sticky Scroll Gallery */}
      <JCCStickyScroll />

      {/* Services Section */}
      <section id="services" className="bg-white pt-20">

        
        {/* Services Gallery */}
        <Gallery4 
          title="What We're Great At"
          description="From bumps and scrapes to full makeovers, we'll get your ride looking amazing again."
          items={[
            {
              id: "accident-repairs",
              title: "Accident Repairs",
              description: "Had a bump? No worries! We'll fix the damage and get you back on the road looking like nothing ever happened.",
              href: "#contact",
              image: "/images/sticky-scroll-component-images/1.png"
            },
            {
              id: "full-resprays", 
              title: "Full Resprays",
              description: "Want a fresh new look? We'll give your vehicle a stunning paint job that'll turn heads. We're 2023 winners of @sagolaofficial Spraying competition!",
              href: "/resprays",
              image: "/images/sticky-scroll-component-images/2.jpeg"
            },
            {
              id: "rust-treatment",
              title: "Rust Treatment", 
              description: "Rust giving you grief? We'll sort it out properly and stop it coming back. Your van will thank you for it!",
              href: "/rust",
              image: "/images/sticky-scroll-component-images/3.jpeg"
            },
            {
              id: "raptor-paint",
              title: "Raptor Paint",
              description: "Love getting off the beaten track? Raptor coating will keep your van looking tough and protected, no matter where you take it.",
              href: "/raptor", 
              image: "/images/sticky-scroll-component-images/4.jpeg"
            },
            {
              id: "welding-fabrication",
              title: "Welding & Fabrication",
              description: "Need something custom made or fixed? We're handy with the welding torch and love creating exactly what you need.",
              href: "/rust",
              image: "/images/sticky-scroll-component-images/5.jpeg"
            },
            {
              id: "restoration",
              title: "Vehicle Restoration", 
              description: "Got a classic that needs some TLC? We'll bring it back to life and make it shine like it did back in the day.",
              href: "#contact",
              image: "/images/sticky-scroll-component-images/6.jpeg"
            }
          ]}
        />
        
        {/* View More Section */}
        <div className="text-center py-16 md:py-20 bg-white px-4">
          <h3 className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl font-black uppercase tracking-tight mb-6">
            Need Something Else?
          </h3>
          <p className="gilroy-text text-base md:text-lg text-gray-600 mb-8 max-w-xl mx-auto">
            We do loads more stuff for campervans, caravans, and motorhomes - just ask!
          </p>
          <button
            className="bg-black text-white px-6 md:px-8 py-4 md:py-4 text-base md:text-lg font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
            style={{ minHeight: '56px' }} // Better touch target
          >
            <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See What Else We Do</span>
            <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
            <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
              See What Else We Do
            </span>
          </button>
        </div>
      </section>

     {/* About Section */}
     <section id="about" className="py-20 md:py-32 bg-gray-100">
       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
         <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[60vh] lg:min-h-[80vh]">
           {/* Left Content */}
           <div className="bg-white p-8 md:p-12 lg:p-16 flex flex-col">
             <div className="flex flex-col justify-between flex-1">
               <div>
                 <h2 className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-black uppercase tracking-tight mb-6 md:mb-8 leading-none">
                   Why Choose Us?
                 </h2>
                 
                 <div className="space-y-4 md:space-y-6 mb-8 md:mb-10">
                   <p className="gilroy-text text-base md:text-lg text-gray-700 leading-relaxed">
                     We&apos;re passionate about what we do and it shows in every job. Our team are proper craftsmen who take pride in their work - we won&apos;t let a vehicle leave here unless it&apos;s absolutely spot on.
                   </p>
                   
                   <p className="gilroy-text text-base md:text-lg text-gray-700 leading-relaxed">
                     We&apos;ll look after you from start to finish. No confusing jargon, no hidden costs - just honest advice and brilliant work. Plus, we can collect and deliver your vehicle so you don&apos;t have to worry about a thing.
                   </p>
                 </div>
               </div>

               {/* Award Badge */}
               <div className="relative bg-black text-white p-6 md:p-8 inline-block overflow-hidden">
                 {/* Decorative corner lines */}
                 <div className="absolute top-0 left-0 w-6 h-6 md:w-8 md:h-8 border-l-2 border-t-2 border-white/30"></div>
                 <div className="absolute top-0 right-0 w-6 h-6 md:w-8 md:h-8 border-r-2 border-t-2 border-white/30"></div>
                 <div className="absolute bottom-0 left-0 w-6 h-6 md:w-8 md:h-8 border-l-2 border-b-2 border-white/30"></div>
                 <div className="absolute bottom-0 right-0 w-6 h-6 md:w-8 md:h-8 border-r-2 border-b-2 border-white/30"></div>
                 
                 {/* Content */}
                 <div className="relative z-10">
                   <div className="text-center mb-3 md:mb-4">
                     <div className="inline-block bg-white/10 px-3 md:px-4 py-1.5 md:py-2 mb-2 md:mb-3">
                       <span className="gilroy-text text-xs font-bold uppercase tracking-widest text-white/80">
                         Award Winner
                       </span>
                     </div>
                     <h3 className="gilroy-text-bold text-xl md:text-2xl font-black uppercase tracking-tight text-white mb-2">
                       2023 Champion
                     </h3>
                   </div>
                   
                   <div className="text-center">
                     <p className="gilroy-text text-sm text-white/90 mb-1">
                       @sagolaofficial
                     </p>
                     <p className="gilroy-text text-xs text-white/70 uppercase tracking-wider">
                       Spraying Competition
                     </p>
                   </div>
                 </div>
               </div>
             </div>
           </div>

           {/* Right Image with Overlay */}
           <div className="relative bg-black overflow-hidden min-h-[50vh] lg:min-h-auto">
             {/* Background Image */}
             <div
               className="absolute inset-0 bg-cover bg-center"
               style={{
                 backgroundImage: `url('/images/award.png')`,
               }}
             ></div>
             
             {/* Dark Overlay */}
             <div className="absolute inset-0 bg-black/60"></div>
             
             {/* Content Overlay */}
             <div className="relative z-10 h-full flex flex-col justify-end p-8 md:p-12 lg:p-16">
               {/* Large Quote */}
               <div className="mb-6 md:mb-8">
                 <h3 className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-black text-white leading-tight mb-4 md:mb-6">
                   &ldquo;Quality work.<br/>
                   <span className="text-gray-300">Friendly service.</span><br/>
                   <span className="text-white">That&apos;s the JCC way.</span>&rdquo;
                 </h3>
               </div>
             </div>
             
             {/* Subtle Author Credit - Bottom Right */}
             <div className="absolute bottom-4 md:bottom-6 right-4 md:right-6 text-right">
               <p className="gilroy-text text-white/90 font-medium text-xs md:text-sm">Jon Churchill</p>
               <p className="gilroy-text text-white/60 text-xs uppercase tracking-wider">Founder @JCC Customs & Commercials</p>
             </div>
           </div>
         </div>
       </div>
     </section>

     {/* Introduction Video Section */}
     <section className="py-20 md:py-32 bg-black text-white">
       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
         <div className="text-center mb-12 md:mb-16">
           <h2 className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-black uppercase tracking-tight text-white mb-6 md:mb-8">
             See Us In Action
           </h2>
           <div className="w-20 md:w-24 h-1 bg-white mx-auto mb-6 md:mb-8"></div>
           <p className="gilroy-text text-lg md:text-xl lg:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
             Take a tour of our workshop and see why we're Norwich's favourite vehicle specialists
           </p>
           <p className="gilroy-text text-sm md:text-base text-gray-400 max-w-2xl mx-auto mt-4">
             Video starts muted - click to hear the full tour
           </p>
         </div>
         
         <div className="max-w-6xl mx-auto">
           <ResponsiveVideo 
             src="/videos/website/JCC_Introduction_video_tour.mp4"
             aspectRatio="auto"
             priority={true}
             maxHeight="70vh"
             muted={true}
             className="shadow-2xl"
           />
         </div>
       </div>
     </section>

      {/* FAQ Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16 md:mb-20">
            <h2 className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-black uppercase tracking-tight text-black mb-6 md:mb-8">
              Got Questions?
            </h2>
            <div className="w-20 md:w-24 h-1 bg-black mx-auto mb-6 md:mb-8"></div>
            <p className="gilroy-text text-lg md:text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We&apos;ve got answers! Here&apos;s everything you need to know about working with us.
            </p>
          </div>
          
          {/* FAQ Accordion */}
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden"
               role="tablist"
          >
            {faqData.map((item) => (
                <FAQItem
                    key={item.id}
                    item={item}
                    isOpen={openFAQ === item.id}
                    onToggle={() => setOpenFAQ(openFAQ === item.id ? null : item.id)}
                />
            ))}
          </div>
          
          {/* Bottom CTA */}
          <div className="mt-16 md:mt-20 text-center bg-black text-white py-12 md:py-16 px-6 md:px-8 rounded-lg">
            <h3 className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl font-black uppercase tracking-tight mb-4 md:mb-6 text-white">
              Still got questions?
            </h3>
            <p className="gilroy-text text-lg md:text-xl text-gray-300 mb-6 md:mb-8 max-w-2xl mx-auto leading-relaxed">
              Give us a call or pop round for a chat. We&apos;re always happy to help!
            </p>
            <Link 
              href="/contact"
              className="inline-block bg-white text-black px-8 md:px-10 py-4 md:py-4 text-base md:text-lg font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
              style={{ minHeight: '56px' }} // Better touch target
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Let&apos;s Chat</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Let&apos;s Chat
              </span>
            </Link>
          </div>
        </div>
      </section>



      {/* Sticky Footer */}
      <JCCStickyFooter />
    </div>
  );
}
