'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Upload, FileText, Users, AlertCircle, CheckCircle, Info } from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { subscriberImportSchema, type SubscriberImportInput } from '@/lib/validations'

interface SubscriberImportFormProps {
  onSuccess?: () => void
}

export default function SubscriberImportForm({ onSuccess }: SubscriberImportFormProps) {
  const router = useRouter()
  const supabase = createClient()
  
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [previewData, setPreviewData] = useState<any[]>([])
  const [fileName, setFileName] = useState('')

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    resolver: zodResolver(subscriberImportSchema),
    defaultValues: {
      overwriteExisting: false,
      sendWelcomeEmail: false,
    }
  })

  const watchedData = watch()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setValue('csvFile', file)
      setFileName(file.name)
      
      // Preview CSV data
      const reader = new FileReader()
      reader.onload = (event) => {
        const text = event.target?.result as string
        const lines = text.split('\n').filter(line => line.trim())
        const headers = lines[0]?.split(',').map(h => h.trim())
        
        if (headers && headers.length >= 2) {
          const preview = lines.slice(1, 6).map(line => {
            const values = line.split(',').map(v => v.trim())
            return headers.reduce((obj, header, index) => {
              obj[header] = values[index] || ''
              return obj
            }, {} as any)
          })
          setPreviewData(preview)
        }
      }
      reader.readAsText(file)
    }
  }

  const onSubmit = async (data: any) => {
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const reader = new FileReader()
      reader.onload = async (event) => {
        const text = event.target?.result as string
        const lines = text.split('\n').filter(line => line.trim())
        const headers = lines[0]?.split(',').map(h => h.trim().toLowerCase())
        
        if (!headers || headers.length < 2) {
          throw new Error('CSV must have at least 2 columns (email and name)')
        }

        const emailIndex = headers.findIndex(h => h.includes('email'))
        const nameIndex = headers.findIndex(h => h.includes('name'))

        if (emailIndex === -1) {
          throw new Error('CSV must have an email column')
        }

        const subscribers = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim())
          return {
            email: values[emailIndex],
            name: nameIndex >= 0 ? values[nameIndex] : '',
            status: 'active' as const,
            created_at: new Date().toISOString(),
          }
        }).filter(sub => sub.email && sub.email.includes('@'))

        if (subscribers.length === 0) {
          throw new Error('No valid email addresses found in CSV')
        }

        // Check for existing subscribers if not overwriting
        let existingEmails: string[] = []
        if (!data.overwriteExisting) {
          const { data: existing } = await supabase
            .from('subscribers')
            .select('email')
            .in('email', subscribers.map(s => s.email))
          
          existingEmails = existing?.map(e => e.email) || []
        }

        // Filter out existing subscribers if not overwriting
        const newSubscribers = data.overwriteExisting 
          ? subscribers 
          : subscribers.filter(sub => !existingEmails.includes(sub.email))

        if (newSubscribers.length === 0) {
          throw new Error('All email addresses already exist. Enable "Overwrite Existing" to update them.')
        }

        // Insert subscribers
        const { error: insertError } = await supabase
          .from('subscribers')
          .upsert(newSubscribers, { onConflict: 'email' })

        if (insertError) throw insertError

        // Send welcome emails if requested
        if (data.sendWelcomeEmail) {
          // This would typically trigger a welcome email campaign
          // For now, we'll just log it
          console.log('Welcome emails would be sent to:', newSubscribers.length, 'subscribers')
        }

        setSuccess(`Successfully imported ${newSubscribers.length} subscribers`)
        reset()
        setPreviewData([])
        setFileName('')
        setTimeout(() => {
          setIsOpen(false)
          onSuccess?.()
          router.refresh()
        }, 2000)
      }

      reader.readAsText(data.csvFile)
    } catch (error) {
      console.error('Error importing subscribers:', error)
      setError(error instanceof Error ? error.message : 'Failed to import subscribers')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="gilroy-text-medium">
          <Upload className="h-4 w-4 mr-2" />
          Import Subscribers
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="gilroy-text-bold">Import Subscribers from CSV</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
              <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {success && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <p className="text-sm text-green-600">{success}</p>
            </div>
          )}

          {/* Instructions */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 gilroy-text-medium">CSV Format Requirements</h4>
                <ul className="mt-2 text-sm text-blue-700 space-y-1">
                  <li>• Must include an "email" column</li>
                  <li>• Optional "name" column for subscriber names</li>
                  <li>• First row should contain column headers</li>
                  <li>• Maximum file size: 5MB</li>
                </ul>
              </div>
            </div>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="csvFile" className="gilroy-text-medium">CSV File *</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <input
                id="csvFile"
                type="file"
                accept=".csv,text/csv"
                onChange={handleFileChange}
                className="hidden"
              />
              <label htmlFor="csvFile" className="cursor-pointer">
                <div className="flex flex-col items-center">
                  <FileText className="h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-2 gilroy-text">
                    {fileName || 'Click to select CSV file'}
                  </p>
                  <p className="text-xs text-gray-500 gilroy-text">
                    CSV files only (max 5MB)
                  </p>
                </div>
              </label>
            </div>
            {errors.csvFile && (
              <p className="text-sm text-red-600">{errors.csvFile.message}</p>
            )}
          </div>

          {/* Preview */}
          {previewData.length > 0 && (
            <div className="space-y-2">
              <Label className="gilroy-text-medium">Preview (first 5 rows)</Label>
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      {Object.keys(previewData[0]).map((header) => (
                        <th key={header} className="px-3 py-2 text-left font-medium text-gray-700 gilroy-text-medium">
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {previewData.map((row, index) => (
                      <tr key={index}>
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="px-3 py-2 text-gray-900 gilroy-text">
                            {String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Switch
                checked={watchedData.overwriteExisting}
                onCheckedChange={(checked) => setValue('overwriteExisting', checked)}
              />
              <div>
                <Label className="gilroy-text-medium">Overwrite Existing Subscribers</Label>
                <p className="text-sm text-gray-600 gilroy-text">
                  Update existing subscribers instead of skipping them
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Switch
                checked={watchedData.sendWelcomeEmail}
                onCheckedChange={(checked) => setValue('sendWelcomeEmail', checked)}
              />
              <div>
                <Label className="gilroy-text-medium">Send Welcome Email</Label>
                <p className="text-sm text-gray-600 gilroy-text">
                  Send welcome emails to newly imported subscribers
                </p>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsOpen(false)
                reset()
                setPreviewData([])
                setFileName('')
                setError('')
                setSuccess('')
              }}
              className="gilroy-text"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !fileName}
              className="gilroy-text-medium"
            >
              {isLoading ? 'Importing...' : 'Import Subscribers'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 