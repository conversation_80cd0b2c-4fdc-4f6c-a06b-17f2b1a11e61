import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { searchParams } = new URL(request.url)

    // Get query parameters
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const search = searchParams.get('search')
    const limit = searchParams.get('limit')
    const albumId = searchParams.get('album_id')
    const excludeAlbums = searchParams.get('exclude_albums') === 'true'

    // Build cache tags based on request parameters
    const cacheTags = ['gallery-images', 'gallery-public']
    if (category && category !== 'all') {
      cacheTags.push(`gallery-category-${category}`)
    }
    if (featured === 'true') {
      cacheTags.push('gallery-featured')
    }

    // Build query
    let query = supabase
      .from('gallery_images')
      .select('*')
      .eq('is_public', true)
      .order('order_index', { ascending: true })
      .order('created_at', { ascending: false })

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    if (featured === 'true') {
      query = query.eq('is_featured', true)
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }

    if (limit) {
      query = query.limit(parseInt(limit))
    }

    // Album filtering options
    if (albumId) {
      query = query.eq('album_id', albumId)
    } else if (excludeAlbums) {
      query = query.is('album_id', null)
    }

    const { data: images, error } = await query

    if (error) {
      console.error('Error fetching gallery images:', error)
      return NextResponse.json(
        { error: 'Failed to fetch gallery images' },
        { status: 500 }
      )
    }

    // Transform data to match frontend interface
    const transformedImages = images?.map(image => ({
      id: image.id,
      title: image.title,
      description: image.description || '',
      category: image.category || 'all',
      type: 'image' as const,
      src: image.image_url,
      thumbnail: image.thumbnail_url || image.image_url,
      tags: image.tags || [],
      featured: image.is_featured || false,
      alt_text: image.alt_text,
      created_at: image.created_at,
      order_index: image.order_index,
      album_id: image.album_id
    })) || []

    // Create response with cache headers
    const response = NextResponse.json({
      images: transformedImages,
      total: transformedImages.length
    })

    // Set cache headers (24 hours for public gallery data)
    response.headers.set('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate=43200')
    response.headers.set('CDN-Cache-Control', 'public, s-maxage=86400')

    // After creating response, attach cache tags header for smart webhook invalidation
    const tagsHeader = cacheTags.join(', ')
    response.headers.set('Cache-Tag', tagsHeader)

    return response

  } catch (error) {
    console.error('Error in gallery API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 