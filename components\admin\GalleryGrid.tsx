'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Images, 
  Edit, 
  Trash2,
  Eye,
  EyeOff,
  Calendar,
  Tag,
  Download,
  MoreVertical,
  Loader2,
  CheckSquare,
  Square
} from 'lucide-react'
import Image from 'next/image'
import { createClient } from '@/lib/supabase'
import { invalidateGalleryCache } from '@/lib/cache-utils'
import GalleryImageEdit from './GalleryImageEdit'

interface GalleryImage {
  id: string
  title: string
  description?: string
  image_url: string
  alt_text?: string
  tags?: string[]
  is_public: boolean
  created_at: string
  file_size?: number
  file_type?: string
  order_index?: number
  is_featured?: boolean
}

export default function GalleryGrid() {
  const router = useRouter()
  const supabase = createClient()
  const [images, setImages] = useState<GalleryImage[]>([])
  const [isLoadingImages, setIsLoadingImages] = useState(true)
  const [selectedImages, setSelectedImages] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isLoading, setIsLoading] = useState(false)
  const [bulkAction, setBulkAction] = useState<string | null>(null)

  const fetchImages = async () => {
    try {
      const { data, error } = await supabase
        .from('gallery_images')
        .select('*')
        .order('order_index')

      if (error) throw error
      setImages(data as GalleryImage[])
    } catch (error) {
      console.error('Error fetching images:', error)
      alert('Failed to fetch images. Please try again.')
    }
  }

  useEffect(() => {
    const loadImages = async () => {
      setIsLoadingImages(true)
      await fetchImages()
      setIsLoadingImages(false)
    }

    loadImages()
    const interval = setInterval(fetchImages, 30000) // Poll every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const handleSelectImage = (imageId: string) => {
    setSelectedImages(prev => 
      prev.includes(imageId) 
        ? prev.filter(id => id !== imageId)
        : [...prev, imageId]
    )
  }

  const handleSelectAll = () => {
    if (selectedImages.length === images.length) {
      setSelectedImages([])
    } else {
      setSelectedImages(images.map(img => img.id))
    }
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const handleBulkAction = async (action: 'make_public' | 'make_private' | 'delete') => {
    if (selectedImages.length === 0) return
    
    setIsLoading(true)
    setBulkAction(action)
    
    try {
      if (action === 'delete') {
        // Delete images from storage and database
        const imagesToDelete = images.filter(img => selectedImages.includes(img.id))
        
        // Delete from storage first
        for (const image of imagesToDelete) {
          if (image.image_url.includes('supabase')) {
            const fileName = image.image_url.split('/').pop()
            if (fileName) {
              await supabase.storage
                .from('gallery-images')
                .remove([fileName])
            }
          }
        }
        
        // Delete from database
        const { error } = await supabase
          .from('gallery_images')
          .delete()
          .in('id', selectedImages)
          
        if (error) throw error
        
      } else {
        // Update visibility
        const isPublic = action === 'make_public'
        const { error } = await supabase
          .from('gallery_images')
          .update({ is_public: isPublic })
          .in('id', selectedImages)
          
        if (error) throw error
      }
      
      // Invalidate gallery cache
      await invalidateGalleryCache()

      // Clear selection and refresh
      setSelectedImages([])
      await fetchImages()
      
    } catch (error) {
      console.error('Bulk action error:', error)
      alert('Failed to perform bulk action. Please try again.')
    } finally {
      setIsLoading(false)
      setBulkAction(null)
    }
  }

  const handleSingleAction = async (imageId: string, action: 'toggle_visibility' | 'delete') => {
    setIsLoading(true)
    
    try {
      if (action === 'delete') {
        const image = images.find(img => img.id === imageId)
        if (image) {
          // Delete from storage
          if (image.image_url.includes('supabase')) {
            const fileName = image.image_url.split('/').pop()
            if (fileName) {
              await supabase.storage
                .from('gallery-images')
                .remove([fileName])
            }
          }
          
          // Delete from database
          const { error } = await supabase
            .from('gallery_images')
            .delete()
            .eq('id', imageId)
            
          if (error) throw error
        }
      } else if (action === 'toggle_visibility') {
        const image = images.find(img => img.id === imageId)
        if (image) {
          const { error } = await supabase
            .from('gallery_images')
            .update({ is_public: !image.is_public })
            .eq('id', imageId)
            
          if (error) throw error
        }
      }
      
      // Invalidate gallery cache
      await invalidateGalleryCache()
      
      // Refresh images list
      await fetchImages()
      
    } catch (error) {
      console.error('Action error:', error)
      alert('Failed to perform action. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingImages) {
    return (
      <div className="p-12 text-center">
        <Loader2 className="h-16 w-16 text-blue-500 mx-auto mb-4 animate-spin" />
        <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium mb-2">
          Loading images...
        </h3>
        <p className="text-gray-500 gilroy-text mb-6">
          Please wait while we fetch the images from the gallery.
        </p>
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <div className="p-12 text-center">
        <Images className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium mb-2">
          No images yet
        </h3>
        <p className="text-gray-500 gilroy-text mb-6">
          Upload your first images to start building your gallery
        </p>
        <Button className="gilroy-text-medium">
          <Images className="h-4 w-4 mr-2" />
          Upload Images
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedImages.length > 0 && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center justify-between sm:justify-start">
              <p className="text-sm text-blue-700 gilroy-text-medium">
                {selectedImages.length} image{selectedImages.length !== 1 ? 's' : ''} selected
              </p>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedImages([])}
                className="text-blue-600 hover:text-blue-700 sm:ml-4"
              >
                Clear
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button 
                size="sm" 
                variant="outline" 
                className="gilroy-text flex-1 sm:flex-none"
                onClick={() => handleBulkAction('make_public')}
                disabled={isLoading}
              >
                {bulkAction === 'make_public' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Eye className="h-4 w-4 mr-2" />
                )}
                Make Public
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                className="gilroy-text flex-1 sm:flex-none"
                onClick={() => handleBulkAction('make_private')}
                disabled={isLoading}
              >
                {bulkAction === 'make_private' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <EyeOff className="h-4 w-4 mr-2" />
                )}
                Make Private
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                className="gilroy-text text-red-600 hover:text-red-700 flex-1 sm:flex-none"
                onClick={() => {
                  if (confirm(`Are you sure you want to delete ${selectedImages.length} image${selectedImages.length !== 1 ? 's' : ''}? This action cannot be undone.`)) {
                    handleBulkAction('delete')
                  }
                }}
                disabled={isLoading}
              >
                {bulkAction === 'delete' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4 mr-2" />
                )}
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Selection Controls */}
      <div className="flex items-center justify-between px-6 py-3 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <input
            type="checkbox"
            checked={selectedImages.length === images.length && images.length > 0}
            onChange={handleSelectAll}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-600 gilroy-text">
            Select all ({images.length} images)
          </span>
        </div>
        <div className="text-sm text-gray-500 gilroy-text">
          {images.length} total images
        </div>
      </div>

      {/* Grid View */}
      {viewMode === 'grid' && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 p-6">
          {images.map((image) => (
            <div key={image.id} className="group relative bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-200">
              {/* Selection Checkbox */}
              <div className="absolute top-2 left-2 z-10">
                <input
                  type="checkbox"
                  checked={selectedImages.includes(image.id)}
                  onChange={() => handleSelectImage(image.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              {/* Visibility Badge */}
              <div className="absolute top-2 right-2 z-10">
                <Badge className={`${image.is_public ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'} gilroy-text-medium`}>
                  {image.is_public ? (
                    <><Eye className="h-3 w-3 mr-1" />Public</>
                  ) : (
                    <><EyeOff className="h-3 w-3 mr-1" />Private</>
                  )}
                </Badge>
              </div>

              {/* Image */}
              <div className="aspect-square relative overflow-hidden rounded-t-lg">
                <Image
                  src={image.image_url}
                  alt={image.alt_text || image.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-200"
                />
              </div>

              {/* Image Info */}
              <div className="p-4">
                <h3 className="font-medium text-gray-900 gilroy-text-medium truncate">
                  {image.title}
                </h3>
                {image.description && (
                  <p className="text-sm text-gray-600 gilroy-text mt-1 line-clamp-2">
                    {image.description}
                  </p>
                )}
                
                {/* Tags */}
                {image.tags && image.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {image.tags.slice(0, 2).map((tag: string) => (
                      <Badge key={tag} variant="outline" className="text-xs gilroy-text">
                        {tag}
                      </Badge>
                    ))}
                    {image.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs gilroy-text">
                        +{image.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Meta Info */}
                <div className="flex items-center justify-between mt-3 text-xs text-gray-500 gilroy-text">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date(image.created_at).toLocaleDateString()}</span>
                  </div>
                  <span>{formatFileSize(image.file_size)}</span>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center space-x-1">
                    <GalleryImageEdit image={image} />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-700 gilroy-text px-2"
                      onClick={() => handleSingleAction(image.id, 'toggle_visibility')}
                      disabled={isLoading}
                      title={image.is_public ? 'Make private' : 'Make public'}
                    >
                      {image.is_public ? (
                        <EyeOff className="h-3 w-3" />
                      ) : (
                        <Eye className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-700 gilroy-text px-2"
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
                          handleSingleAction(image.id, 'delete')
                        }
                      }}
                      disabled={isLoading}
                      title="Delete image"
                    >
                      {isLoading ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Trash2 className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                  <div className="text-xs text-gray-500 gilroy-text">
                    {image.order_index ? `#${image.order_index}` : ''}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* List View */}
      {viewMode === 'list' && (
        <div className="divide-y divide-gray-100">
          {images.map((image) => (
            <div key={image.id} className="p-6 hover:bg-gray-50 transition-colors duration-150">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedImages.includes(image.id)}
                  onChange={() => handleSelectImage(image.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                
                <div className="flex-shrink-0 w-16 h-16 relative rounded-lg overflow-hidden">
                  <Image
                    src={image.image_url}
                    alt={image.alt_text || image.title}
                    fill
                    className="object-cover"
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium text-gray-900 gilroy-text-medium truncate">
                      {image.title}
                    </h3>
                    <Badge className={`${image.is_public ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'} gilroy-text-medium`}>
                      {image.is_public ? 'Public' : 'Private'}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 gilroy-text mt-1 truncate">
                    {image.description || 'No description'}
                  </p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 gilroy-text">
                    <span>{new Date(image.created_at).toLocaleDateString()}</span>
                    <span>{formatFileSize(image.file_size)}</span>
                    <span>{image.file_type}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <GalleryImageEdit image={image} />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-gray-700 gilroy-text"
                    onClick={() => handleSingleAction(image.id, 'toggle_visibility')}
                    disabled={isLoading}
                    title={image.is_public ? 'Make private' : 'Make public'}
                  >
                    {image.is_public ? (
                      <><EyeOff className="h-4 w-4 mr-1" />Hide</>
                    ) : (
                      <><Eye className="h-4 w-4 mr-1" />Show</>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700 gilroy-text"
                    onClick={() => {
                      if (confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
                        handleSingleAction(image.id, 'delete')
                      }
                    }}
                    disabled={isLoading}
                    title="Delete image"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
} 