import { createServerSupabaseClient } from '@/lib/supabase'

// Force dynamic rendering for admin pages that use cookies
export const dynamic = 'force-dynamic'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import ProjectList from '@/components/admin/ProjectList'
import { FolderOpen, Plus, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import Link from 'next/link'

export default async function ProjectsPage() {
  const supabase = await createServerSupabaseClient()

  // Get all projects
  const { data: projects, error } = await supabase
    .from('projects')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching projects:', error)
  }

  // Get stats
  const activeCount = projects?.filter(p => p.status === 'active').length || 0
  const completedCount = projects?.filter(p => p.status === 'completed').length || 0
  const totalCount = projects?.length || 0

  const stats = [
    {
      name: 'Total Projects',
      value: totalCount,
      icon: FolderOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: 'Active',
      value: activeCount,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      name: 'Completed',
      value: completedCount,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-2">Manage customer projects and track progress</p>
        </div>
        <Link href="/admin/projects/new">
          <Button className="flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>New Project</span>
          </Button>
        </Link>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => (
          <Card key={stat.name}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Project List */}
      <Card>
        <CardHeader>
          <CardTitle>All Projects</CardTitle>
          <CardDescription>View and manage customer projects</CardDescription>
        </CardHeader>
        <CardContent>
          <ProjectList projects={projects || []} />
        </CardContent>
      </Card>
    </div>
  )
} 