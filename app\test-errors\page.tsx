'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Bug, Zap, Server, Database, Network, Clock, Shield } from 'lucide-react';

export default function TestErrorsPage() {
  const [errorType, setErrorType] = useState<string | null>(null);

  // Different error types to test
  const testRenderError = () => {
    setErrorType('render');
    // This will cause a render error
    throw new Error('Test render error - component failed to render');
  };

  const testAsyncError = async () => {
    setErrorType('async');
    // Simulate async error
    await new Promise(resolve => setTimeout(resolve, 100));
    throw new Error('Test async error - promise rejected');
  };

  const testTypeError = () => {
    setErrorType('type');
    // This will cause a TypeError
    const obj: any = null;
    obj.someProperty.nestedProperty = 'test';
  };

  const testReferenceError = () => {
    setErrorType('reference');
    // This will cause a ReferenceError
    // @ts-expect-error - intentionally accessing undefined variable for testing
    undefinedVariable.someMethod();
  };

  const testInfiniteLoop = () => {
    setErrorType('infinite');
    // This will cause a stack overflow
    const recursiveFunction = (): any => {
      return recursiveFunction();
    };
    recursiveFunction();
  };

  const testNetworkError = async () => {
    setErrorType('network');
    // Simulate network error
    try {
      const response = await fetch('/api/nonexistent-endpoint');
      if (!response.ok) {
        throw new Error(`Network error: ${response.status}`);
      }
    } catch (error) {
      throw new Error('Test network error - failed to fetch data');
    }
  };

  const testHydrationError = () => {
    setErrorType('hydration');
    // This will cause hydration mismatch by showing different content on server vs client
    const isClient = typeof window !== 'undefined';
    const serverValue = 'server-rendered-content';
    const clientValue = 'client-rendered-content';
    
    return (
      <div>
        <p>Environment: {isClient ? clientValue : serverValue}</p>
        <p>Timestamp: {isClient ? Date.now() : 'server-time'}</p>
      </div>
    );
  };

  const testChunkLoadError = () => {
    setErrorType('chunk');
    // Simulate chunk loading error
    throw new Error('Loading chunk 123 failed. (ChunkLoadError)');
  };

  const testMemoryError = () => {
    setErrorType('memory');
    // This will cause memory issues
    const bigArray = [];
    for (let i = 0; i < 10000000; i++) {
      bigArray.push(new Array(1000).fill('memory-test'));
    }
    return bigArray;
  };

  const testDatabaseError = () => {
    setErrorType('database');
    // Simulate database error
    throw new Error('Database connection failed - unable to connect to Supabase');
  };

  const errorTests = [
    {
      title: 'Render Error',
      description: 'Test component render failure',
      icon: Bug,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      action: testRenderError,
      severity: 'High'
    },
    {
      title: 'Async Error',
      description: 'Test promise rejection',
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      action: testAsyncError,
      severity: 'Medium'
    },
    {
      title: 'Type Error',
      description: 'Test null/undefined access',
      icon: AlertTriangle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      action: testTypeError,
      severity: 'Medium'
    },
    {
      title: 'Reference Error',
      description: 'Test undefined variable access',
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      action: testReferenceError,
      severity: 'Medium'
    },
    {
      title: 'Stack Overflow',
      description: 'Test infinite recursion',
      icon: Server,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      action: testInfiniteLoop,
      severity: 'Critical'
    },
    {
      title: 'Network Error',
      description: 'Test fetch failure',
      icon: Network,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      action: testNetworkError,
      severity: 'Medium'
    },
    {
      title: 'Chunk Load Error',
      description: 'Test module loading failure',
      icon: Database,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      action: testChunkLoadError,
      severity: 'High'
    },
    {
      title: 'Memory Error',
      description: 'Test memory exhaustion',
      icon: Shield,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
      action: testMemoryError,
      severity: 'Critical'
    },
    {
      title: 'Database Error',
      description: 'Test database connection failure',
      icon: Database,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      action: testDatabaseError,
      severity: 'High'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'bg-red-500 text-white';
      case 'High': return 'bg-orange-500 text-white';
      case 'Medium': return 'bg-yellow-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  // If hydration error test is active, render the problematic component
  if (errorType === 'hydration') {
    return testHydrationError();
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Error Boundary Test Suite
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Test different error types to verify our error boundaries are working correctly.
            Each test will trigger a specific error condition.
          </p>
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 font-medium">
              ⚠️ Warning: These tests will intentionally break the application to test error handling.
            </p>
          </div>
        </div>

        {/* Current Error Status */}
        {errorType && (
          <div className="mb-8 p-6 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-red-800">
                  Testing: {errorType} error
                </h3>
                <p className="text-red-600">
                  If you can see this, the error boundary caught the error successfully!
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Test Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {errorTests.map((test, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg ${test.bgColor}`}>
                    <test.icon className={`h-6 w-6 ${test.color}`} />
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(test.severity)}`}>
                    {test.severity}
                  </span>
                </div>
                <CardTitle className="text-lg">{test.title}</CardTitle>
                <CardDescription>{test.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={test.action}
                  variant="outline"
                  className="w-full hover:bg-gray-50"
                >
                  Trigger {test.title}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Navigation Actions */}
        <div className="mt-12 flex justify-center space-x-4">
          <Button
            onClick={() => window.location.href = '/'}
            variant="outline"
          >
            Back to Home
          </Button>
          <Button
            onClick={() => window.location.href = '/not-found-test'}
            variant="outline"
          >
            Test 404 Page
          </Button>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
          >
            Refresh Page
          </Button>
        </div>

        {/* Instructions */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-4">
            Testing Instructions
          </h3>
          <div className="space-y-2 text-blue-700">
            <p>• Click any error test button to trigger that specific error type</p>
            <p>• Check if the error boundary displays the fallback UI</p>
            <p>• Verify error logging in the browser console</p>
            <p>• Test the retry functionality if available</p>
            <p>• Navigate to a non-existent URL to test the 404 page</p>
            <p>• Check network tab for any failed requests</p>
          </div>
        </div>

        {/* Error Boundary Information */}
        <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Error Boundary Coverage
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Gracefully Degrading Error Boundary:</h4>
              <ul className="space-y-1">
                <li>• Hydration errors</li>
                <li>• Client-side rendering errors</li>
                <li>• Component lifecycle errors</li>
                <li>• Event handler errors</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Global Error Boundary:</h4>
              <ul className="space-y-1">
                <li>• Unhandled promise rejections</li>
                <li>• Root-level errors</li>
                <li>• Critical application failures</li>
                <li>• Server-side errors</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 