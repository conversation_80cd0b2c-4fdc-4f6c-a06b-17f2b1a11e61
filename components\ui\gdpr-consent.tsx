'use client'

import { useState, useEffect } from 'react'
import { Button } from './button'
import { X, BarChart3 } from 'lucide-react'

interface GDPRConsentProps {
  onConsentChange: (consent: boolean) => void
}

export default function GDPRConsent({ onConsentChange }: GDPRConsentProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    // Check if user has already made a consent choice
    const consentChoice = localStorage.getItem('gdpr-consent')
    if (consentChoice === null) {
      // Show popup after a brief delay for better UX
      setTimeout(() => {
        setIsVisible(true)
        setIsAnimating(true)
      }, 1000)
    } else {
      // Apply existing consent choice
      onConsentChange(consentChoice === 'accepted')
    }
  }, [onConsentChange])

  const handleAccept = () => {
    localStorage.setItem('gdpr-consent', 'accepted')
    onConsentChange(true)
    closePopup()
  }

  const handleDecline = () => {
    localStorage.setItem('gdpr-consent', 'declined')
    onConsentChange(false)
    closePopup()
  }

  const closePopup = () => {
    setIsAnimating(false)
    setTimeout(() => setIsVisible(false), 300)
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black/20 transition-opacity duration-300 ${
          isAnimating ? 'opacity-100' : 'opacity-0'
        }`}
      />
      
      {/* Popup */}
      <div className="fixed bottom-0 left-0 right-0 p-3 sm:p-4 pointer-events-auto">
        <div 
          className={`max-w-2xl mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 transform transition-all duration-300 ${
            isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
          }`}
        >
          <div className="p-4 sm:p-5">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="p-1.5 sm:p-2 bg-primary/10 rounded-lg">
                  <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-base sm:text-lg font-bold text-foreground gilroy-text-bold">
                    Privacy & Analytics
                  </h3>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={closePopup}
                className="h-7 w-7 sm:h-8 sm:w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 flex-shrink-0"
              >
                <X className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="mb-4">
              <p className="text-sm sm:text-base text-foreground gilroy-text leading-relaxed">
                We use analytics to improve your experience. Data is anonymized and used solely for website optimization.
              </p>
            </div>

            {/* Actions */}
            <div className="flex flex-col xs:flex-row gap-2 sm:gap-3">
              <Button
                onClick={handleAccept}
                className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground gilroy-text-medium text-sm sm:text-base h-9 sm:h-10"
              >
                Accept Analytics
              </Button>
              <Button
                onClick={handleDecline}
                variant="outline"
                className="flex-1 gilroy-text-medium text-sm sm:text-base h-9 sm:h-10"
              >
                Decline
              </Button>
            </div>

            {/* Footer */}
            <div className="mt-3 pt-3 border-t border-border">
              <p className="text-xs text-muted-foreground gilroy-text text-center">
                Essential cookies are used for basic functionality. 
                <span className="text-primary hover:underline cursor-pointer ml-1">
                  Privacy policy
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 