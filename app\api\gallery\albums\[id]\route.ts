import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

interface RouteParams {
    params: Promise<{
        id: string;
    }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
    try {
        const { searchParams } = new URL(request.url);
        const includeImages = searchParams.get('include_images') !== 'false'; // Default to true
        const resolvedParams = await params;

        const supabase = createClient();

        // Fetch the album
        const { data: album, error } = await supabase
            .from('gallery_albums')
            .select('*')
            .eq('id', resolvedParams.id)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({ error: 'Album not found' }, { status: 404 });
            }
            console.error('Error fetching album:', error);
            return NextResponse.json({ error: 'Failed to fetch album' }, { status: 500 });
        }

        // Fetch associated images if requested
        if (includeImages) {
            const { data: images, error: imageError } = await supabase
                .from('gallery_images')
                .select('*')
                .eq('album_id', resolvedParams.id)
                .order('order_index', { ascending: true })
                .order('created_at', { ascending: false });

            if (imageError) {
                console.error('Error fetching album images:', imageError);
                return NextResponse.json({
                    album: { ...album, images: [] }
                });
            }

            return NextResponse.json({
                album: { ...album, images: images || [] }
            });
        }

        return NextResponse.json({ album });
    } catch (error) {
        console.error('Error in album GET API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
    try {
        const body = await request.json();
        const { title, description, slug, coverImageUrl, coverImageAlt, isFeatured, isPublished, orderIndex } = body;
        const resolvedParams = await params;

        if (!title || !slug) {
            return NextResponse.json({ error: 'Title and slug are required' }, { status: 400 });
        }

        const supabase = createClient();

        // Check if slug is taken by another album
        const { data: existingAlbum } = await supabase
            .from('gallery_albums')
            .select('id')
            .eq('slug', slug)
            .neq('id', resolvedParams.id)
            .single();

        if (existingAlbum) {
            return NextResponse.json({ error: 'Album with this slug already exists' }, { status: 409 });
        }

        // Update the album
        const { data: album, error } = await supabase
            .from('gallery_albums')
            .update({
                title,
                description: description || null,
                slug,
                cover_image_url: coverImageUrl || null,
                cover_image_alt: coverImageAlt || null,
                is_featured: isFeatured || false,
                is_published: isPublished !== false,
                order_index: orderIndex || 0
            })
            .eq('id', resolvedParams.id)
            .select()
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({ error: 'Album not found' }, { status: 404 });
            }
            console.error('Error updating album:', error);
            return NextResponse.json({ error: 'Failed to update album' }, { status: 500 });
        }

        return NextResponse.json({ album });
    } catch (error) {
        console.error('Error in album PUT API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
    try {
        const resolvedParams = await params;
        const supabase = createClient();

        // First, remove album association from any images (set album_id to null)
        const { error: updateError } = await supabase
            .from('gallery_images')
            .update({ album_id: null })
            .eq('album_id', resolvedParams.id);

        if (updateError) {
            console.error('Error removing album association from images:', updateError);
            return NextResponse.json({ error: 'Failed to update associated images' }, { status: 500 });
        }

        // Now delete the album
        const { error } = await supabase
            .from('gallery_albums')
            .delete()
            .eq('id', resolvedParams.id);

        if (error) {
            console.error('Error deleting album:', error);
            return NextResponse.json({ error: 'Failed to delete album' }, { status: 500 });
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Error in album DELETE API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
} 