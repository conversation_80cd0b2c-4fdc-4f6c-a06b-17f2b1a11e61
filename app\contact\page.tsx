'use client';

import { useState, useRef, useEffect } from 'react';

import Link from 'next/link';
import { Phone, Mail, MapPin, Instagram, MessageCircle, Upload, X, Check, Loader2, AlertCircle, CheckCircle, XCircle, ArrowRight, Sparkles, Clock, Globe, Shield, Zap } from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '@/components/header';
import JCCStickyFooter from '@/components/footer';

// Validation utility functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePhone = (phone: string): boolean => {
  // UK phone number validation - allows various formats
  const phoneRegex = /^(?:(?:\+44\s?|0)(?:\d{2}\s?\d{4}\s?\d{4}|\d{3}\s?\d{3}\s?\d{4}|\d{4}\s?\d{6}|\d{5}\s?\d{5}))$/;
  const cleanPhone = phone.replace(/\s/g, '');
  return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
};

const validateName = (name: string): boolean => {
  // Name should be at least 2 characters, no numbers, allow letters, spaces, hyphens, apostrophes
  const nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
  return nameRegex.test(name.trim());
};

const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digits
  const cleaned = phone.replace(/\D/g, '');
  
  // Format UK phone numbers
  if (cleaned.length === 11 && cleaned.startsWith('0')) {
    return cleaned.replace(/(\d{5})(\d{3})(\d{3})/, '$1 $2 $3');
  } else if (cleaned.length === 10) {
    return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
  }
  
  return phone;
};

// File validation utilities
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/tiff'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILES = 5;

const validateFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file type
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Please upload images only (JPEG, PNG, GIF, WebP, BMP, TIFF).`
    };
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size too large. Maximum size is 10MB.`
    };
  }

  // Check file name for potential issues
  const fileName = file.name.toLowerCase();
  const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js'];
  const hasSuspiciousExtension = suspiciousExtensions.some(ext => fileName.includes(ext));
  
  if (hasSuspiciousExtension) {
    return {
      isValid: false,
      error: `File name contains suspicious content. Please rename your file.`
    };
  }

  // Check for minimum dimensions (optional - would need to read image)
  // This is a basic check - for production, you might want to validate actual image dimensions
  if (file.size < 1024) { // Less than 1KB is suspicious for an image
    return {
      isValid: false,
      error: `File seems too small to be a valid image.`
    };
  }

  return { isValid: true };
};

// Form validation errors interface
interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  serviceType?: string;
  message?: string;
  questions?: Record<string, string>;
  files?: string;
  vehicleMake?: string;
  vehicleModel?: string;
  vehicleYear?: string;
  vehicleType?: string;
}

// Color Wheel Component
const ColorWheel = ({ onColorChange, selectedColor }: { onColorChange: (color: string) => void; selectedColor: string }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isMouseDown, setIsMouseDown] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 10;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw color wheel
    for (let angle = 0; angle < 360; angle++) {
      const startAngle = (angle - 2) * Math.PI / 180;
      const endAngle = angle * Math.PI / 180;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.lineWidth = radius;
      ctx.strokeStyle = `hsl(${angle}, 100%, 50%)`;
      ctx.stroke();
    }

    // Draw inner circle for brightness/saturation
    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius * 0.7);
    gradient.addColorStop(0, 'white');
    gradient.addColorStop(1, 'transparent');
    
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.7, 0, 2 * Math.PI);
    ctx.fillStyle = gradient;
    ctx.fill();

    // Draw black gradient for darkness
    const blackGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius * 0.7);
    blackGradient.addColorStop(0, 'transparent');
    blackGradient.addColorStop(1, 'black');
    
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.7, 0, 2 * Math.PI);
    ctx.fillStyle = blackGradient;
    ctx.globalCompositeOperation = 'multiply';
    ctx.fill();
    ctx.globalCompositeOperation = 'source-over';
  }, []);

  const getColorFromPosition = (x: number, y: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return '#000000';

    const rect = canvas.getBoundingClientRect();
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const canvasX = (x - rect.left) * (canvas.width / rect.width);
    const canvasY = (y - rect.top) * (canvas.height / rect.height);

    const dx = canvasX - centerX;
    const dy = canvasY - centerY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const radius = Math.min(centerX, centerY) - 10;

    // Check if click is outside the wheel
    if (distance > radius * 0.7) {
      // Outer ring - pure hue
      const angle = Math.atan2(dy, dx) * 180 / Math.PI;
      const hue = (angle + 360) % 360;
      return `hsl(${hue}, 100%, 50%)`;
    } else {
      // Inner circle - calculate based on distance from center
      const angle = Math.atan2(dy, dx) * 180 / Math.PI;
      const hue = (angle + 360) % 360;
      
      // Normalize distance (0 = center, 1 = edge of inner circle)
      const normalizedDistance = distance / (radius * 0.7);
      
      // At center: white (high lightness, low saturation)
      // At edge: full color (medium lightness, high saturation)
      // Very close to center should be white/gray
      if (normalizedDistance < 0.1) {
        // Very center - white to light gray
        const lightness = Math.max(80, 100 - normalizedDistance * 200);
        return `hsl(0, 0%, ${lightness}%)`;
      } else {
        // Calculate saturation and lightness based on distance
        const saturation = Math.min(100, normalizedDistance * 100);
        const lightness = Math.max(10, 70 - normalizedDistance * 60);
        
        // For very dark areas (high distance), ensure we can get true black
        if (normalizedDistance > 0.8) {
          const blackness = (normalizedDistance - 0.8) / 0.2; // 0 to 1
          const finalLightness = lightness * (1 - blackness);
          return `hsl(${hue}, ${saturation}%, ${Math.max(0, finalLightness)}%)`;
        }
        
        return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
      }
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsMouseDown(true);
    const color = getColorFromPosition(e.clientX, e.clientY);
    onColorChange(color);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isMouseDown) {
      const color = getColorFromPosition(e.clientX, e.clientY);
      onColorChange(color);
    }
  };

  const handleMouseUp = () => {
    setIsMouseDown(false);
  };

  // Touch events for mobile support
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsMouseDown(true);
    const touch = e.touches[0];
    const color = getColorFromPosition(touch.clientX, touch.clientY);
    onColorChange(color);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isMouseDown) {
      const touch = e.touches[0];
      const color = getColorFromPosition(touch.clientX, touch.clientY);
      onColorChange(color);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    setIsMouseDown(false);
  };

  // Common predefined colors for automotive use
  const commonColors = [
    { name: 'Jet Black', color: '#000000' },
    { name: 'Pure White', color: '#FFFFFF' },
    { name: 'Silver', color: '#C0C0C0' },
    { name: 'Charcoal Grey', color: '#36454F' },
    { name: 'Racing Red', color: '#FF0000' },
    { name: 'Deep Blue', color: '#003366' },
    { name: 'Forest Green', color: '#228B22' },
    { name: 'Bright Orange', color: '#FF6600' },
    { name: 'Golden Yellow', color: '#FFD700' },
    { name: 'Burgundy', color: '#800020' },
    { name: 'Navy Blue', color: '#000080' },
    { name: 'Olive Green', color: '#808000' }
  ];

  return (
    <div className="w-full max-w-lg mx-auto space-y-6">
      {/* Color Wheel */}
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <canvas
            ref={canvasRef}
            width={200}
            height={200}
            className="cursor-crosshair border border-gray-300 rounded-full w-48 h-48 sm:w-52 sm:h-52 md:w-56 md:h-56 touch-none select-none"
            style={{ touchAction: 'none' }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          />
        </div>
        
        {/* Selected Color Display */}
        <div className="flex items-center space-x-4 p-3 bg-white rounded-lg border border-gray-200">
          <div 
            className="w-12 h-12 border-2 border-gray-300 rounded-lg flex-shrink-0"
            style={{ backgroundColor: selectedColor }}
          />
          <div className="text-sm gilroy-text">
            <p className="font-medium text-gray-900">Selected Color:</p>
            <p className="text-gray-600 font-mono text-xs">{selectedColor}</p>
          </div>
        </div>
      </div>

      {/* Predefined Colors */}
      <div className="space-y-3">
        <h5 className="text-base font-bold text-gray-900 gilroy-text-bold">
          Popular Colors
        </h5>
        <p className="text-sm text-gray-600 gilroy-text">
          Click any color below for quick selection:
        </p>
        
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
          {commonColors.map((colorOption, index) => (
            <button
              key={index}
              type="button"
              onClick={() => onColorChange(colorOption.color)}
              className={`group relative w-full aspect-square rounded-lg border-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                selectedColor.toLowerCase() === colorOption.color.toLowerCase() 
                  ? 'border-black ring-2 ring-black ring-offset-2' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              style={{ backgroundColor: colorOption.color }}
              title={colorOption.name}
            >
              {/* Checkmark for selected color */}
              {selectedColor.toLowerCase() === colorOption.color.toLowerCase() && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <Check size={16} className="text-black" />
                  </div>
                </div>
              )}
              
              {/* Color name tooltip on hover */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-10">
                {colorOption.name}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="text-center text-sm text-gray-500 gilroy-text">
        <p>Use the color wheel above for custom colors, or select from popular colors below.</p>
      </div>
    </div>
  );
};

// Service-specific questions
const serviceQuestions = {
  repairs: [
    { id: 'damage-type', label: 'What type of damage needs repair?', type: 'select', options: ['Dents', 'Scratches', 'Rust damage', 'Accident damage', 'Panel replacement', 'Multiple issues'] },
    { id: 'damage-extent', label: 'How extensive is the damage?', type: 'select', options: ['Minor (cosmetic)', 'Moderate (functional)', 'Major (structural)', 'Complete overhaul needed'] },
    { id: 'insurance-claim', label: 'Is this an insurance claim?', type: 'select', options: ['Yes', 'No', 'Considering it'] }
  ],
  resprays: [
    { id: 'respray-type', label: 'What type of respray do you need?', type: 'select', options: ['Full vehicle', 'Partial panels', 'Touch-up work', 'Color change', 'Repair and respray'] },
    { id: 'current-condition', label: 'Current paint condition?', type: 'select', options: ['Good (just want change)', 'Fair (some wear)', 'Poor (faded/damaged)', 'Bare metal/primer'] },
    { id: 'color-preference', label: 'Do you have a color in mind?', type: 'select', options: ['Yes, specific color', 'Similar to current', 'Open to suggestions', 'Custom color match'] }
  ],
  rust: [
    { id: 'rust-severity', label: 'How severe is the rust?', type: 'select', options: ['Surface rust only', 'Through to metal', 'Structural damage', 'Extensive throughout'] },
    { id: 'rust-location', label: 'Where is the rust located?', type: 'select', options: ['Body panels', 'Chassis/frame', 'Wheel arches', 'Door frames', 'Floor/sills', 'Multiple areas'] },
    { id: 'prevention-interest', label: 'Interested in rust prevention?', type: 'select', options: ['Yes, full protection', 'Yes, basic protection', 'Just repair needed', 'Tell me more'] }
  ],
  raptor: [
    { id: 'raptor-area', label: 'What areas need Raptor coating?', type: 'select', options: ['Full vehicle', 'Lower panels', 'Wheel arches', 'Bumpers', 'Custom areas'] },
    { id: 'raptor-purpose', label: 'Main purpose of coating?', type: 'select', options: ['Protection', 'Appearance', 'Both', 'Repair coverage'] },
    { id: 'texture-preference', label: 'Texture preference?', type: 'select', options: ['Standard texture', 'Smooth finish', 'Heavy texture', 'Not sure'] }
  ],
  heaters: [
    { id: 'heater-type', label: 'What type of heater?', type: 'select', options: ['Diesel air heater', 'Diesel coolant heater', 'Electric heater', 'Not sure'] },
    { id: 'vehicle-size', label: 'Vehicle size?', type: 'select', options: ['Small van/car', 'Medium van', 'Large van/motorhome', 'Commercial vehicle'] },
    { id: 'installation-complexity', label: 'Installation complexity?', type: 'select', options: ['Basic installation', 'Custom installation', 'Full system', 'Consultation needed'] }
  ],
  general: [
    { id: 'service-interest', label: 'What services interest you?', type: 'select', options: ['Repairs', 'Resprays', 'Rust treatment', 'Raptor coating', 'Night heaters', 'Multiple services'] },
    { id: 'project-timeline', label: 'Project timeline?', type: 'select', options: ['Urgent', 'Within a month', 'Planning ahead', 'Just exploring'] },
    { id: 'budget-range', label: 'Rough budget range?', type: 'select', options: ['Under £500', '£500-1000', '£1000-2500', '£2500+', 'Need estimate'] }
  ]
};

// Error Alert Component
const ErrorAlert = ({ message }: { message: string }) => (
  <div className="flex items-center space-x-2 text-red-600 text-sm mt-1">
    <AlertCircle size={16} />
    <span>{message}</span>
  </div>
);

// Validation Status Component
const ValidationStatus = ({ 
  isValid, 
  hasError, 
  isActive 
}: { 
  isValid: boolean; 
  hasError: boolean; 
  isActive: boolean;
}) => {
  if (!isActive) return null;
  
  if (hasError) {
    return (
      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
        <XCircle size={20} className="text-red-500" />
      </div>
    );
  }
  
  if (isValid) {
    return (
      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
        <CheckCircle size={20} className="text-green-500" />
      </div>
    );
  }
  
  return null;
};

const DynamicContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    serviceType: 'general',
    message: '',
    vehicleMake: '',
    vehicleModel: '',
    vehicleYear: '',
    vehicleType: ''
  });
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [images, setImages] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isDragging, setIsDragging] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Real-time validation
  const validateField = (field: string, value: string): string | undefined => {
    switch (field) {
      case 'name':
        if (!value.trim()) return 'Name is required';
        if (value.trim().length < 2) return 'Name must be at least 2 characters';
        if (!validateName(value)) return 'Name can only contain letters, spaces, hyphens, and apostrophes';
        return undefined;
      case 'vehicleMake':
        if (value && value.trim().length < 2) return 'Make must be at least 2 characters';
        return undefined;
      case 'vehicleModel':
        if (value && value.trim().length < 1) return 'Model is required when make is provided';
        return undefined;
      case 'vehicleYear':
        if (value) {
          const year = parseInt(value);
          const currentYear = new Date().getFullYear();
          if (isNaN(year) || year < 1900 || year > currentYear + 1) {
            return `Year must be between 1900 and ${currentYear + 1}`;
          }
        }
        return undefined;

      
      case 'email':
        if (!value.trim()) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        return undefined;
      
      case 'phone':
        if (!value.trim()) return 'Phone number is required';
        if (!validatePhone(value)) return 'Please enter a valid UK phone number';
        return undefined;
      
      case 'message':
        if (value.length > 1000) return 'Message must be less than 1000 characters';
        return undefined;
      
      default:
        return undefined;
    }
  };

  const handleInputChange = (field: string, value: string) => {
    let processedValue = value;
    
    // Format phone number as user types
    if (field === 'phone') {
      processedValue = formatPhoneNumber(value);
    }
    
    setFormData(prev => ({ ...prev, [field]: processedValue }));
    
    // Mark field as touched when user starts typing
    if (!touched[field] && value.length > 0) {
      setTouched(prev => ({ ...prev, [field]: true }));
    }
    
    // Real-time validation - validate immediately for better UX
    const error = validateField(field, processedValue);
    setErrors(prev => ({ ...prev, [field]: error }));
    
    if (field === 'serviceType') {
      setAnswers({}); // Clear answers when service type changes
      setSelectedColor('#ff0000'); // Reset color when service type changes
      setErrors(prev => ({ ...prev, questions: undefined })); // Clear question errors
    }
  };

  const handleBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    const error = validateField(field, formData[field as keyof typeof formData]);
    setErrors(prev => ({ ...prev, [field]: error }));
  };

    const handleAnswerChange = (questionId: string, value: string) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }));
    
    // Mark as touched and validate immediately
    setTouched(prev => ({ ...prev, [questionId]: true }));
    
    // Real-time validation for questions
    if (value) {
      // Clear error if value is selected
      setErrors(prev => {
        const newQuestions = { ...prev.questions };
        delete newQuestions[questionId];
        return {
          ...prev,
          questions: Object.keys(newQuestions).length > 0 ? newQuestions : undefined
        };
      });
    } else {
      // Check if this is a required question
      const questions = serviceQuestions[formData.serviceType as keyof typeof serviceQuestions] || [];
      const questionIndex = questions.findIndex(q => q.id === questionId);
      
      if (questionIndex < 2) { // First 2 questions are required
        setErrors(prev => ({
          ...prev,
          questions: { ...prev.questions, [questionId]: 'This field is required' }
        }));
      }
    }
  };

  const handleImageUpload = (files: FileList | null) => {
    if (!files) return;
    
    const newFiles: File[] = [];
    const fileErrors: string[] = [];
    
    // Check if adding these files would exceed the limit
    if (images.length + files.length > MAX_FILES) {
      setErrors(prev => ({ 
        ...prev, 
        files: `Maximum ${MAX_FILES} files allowed. You can upload ${MAX_FILES - images.length} more.` 
      }));
      return;
    }
    
    Array.from(files).forEach(file => {
      const validation = validateFile(file);
      if (validation.isValid) {
        newFiles.push(file);
      } else {
        fileErrors.push(`${file.name}: ${validation.error}`);
      }
    });
    
    if (fileErrors.length > 0) {
      setErrors(prev => ({ 
        ...prev, 
        files: fileErrors.join('; ') 
      }));
    } else {
      setErrors(prev => ({ ...prev, files: undefined }));
    }
    
    if (newFiles.length > 0) {
      setImages(prev => [...prev, ...newFiles]);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    // Clear file errors when user removes files
    setErrors(prev => ({ ...prev, files: undefined }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setIsDragging(true);
    } else if (e.type === 'dragleave') {
      setIsDragging(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleImageUpload(e.dataTransfer.files);
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    // Validate basic fields
    (['name', 'email', 'phone'] as const).forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) {
        newErrors[field] = error;
      }
    });
    
    // Validate message length
    if (formData.message.length > 1000) {
      newErrors.message = 'Message must be less than 1000 characters';
    }
    
    // Check if service-specific questions are answered
    const questions = serviceQuestions[formData.serviceType as keyof typeof serviceQuestions] || [];
    const requiredQuestions = questions.slice(0, 2); // First 2 questions are required
    const questionErrors: Record<string, string> = {};
    
    for (const question of requiredQuestions) {
      if (!answers[question.id]) {
        questionErrors[question.id] = 'This field is required';
      }
    }
    
    if (Object.keys(questionErrors).length > 0) {
      newErrors.questions = questionErrors;
    }
    
    setErrors(newErrors);
    
    // Mark all fields as touched for error display
    setTouched({
      name: true,
      email: true,
      phone: true,
      message: true,
      ...Object.fromEntries(requiredQuestions.map(q => [q.id, true]))
    });
    
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      // Scroll to first error
      const firstErrorElement = document.querySelector('.text-red-600');
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    
    try {
      // Prepare form data
      const formDataToSubmit = new FormData();
      formDataToSubmit.append('name', formData.name);
      formDataToSubmit.append('email', formData.email);
      formDataToSubmit.append('phone', formData.phone);
      formDataToSubmit.append('serviceType', formData.serviceType);
      formDataToSubmit.append('message', formData.message);
      formDataToSubmit.append('serviceQuestions', JSON.stringify(answers));
      
      // Add vehicle details
      formDataToSubmit.append('vehicleMake', formData.vehicleMake);
      formDataToSubmit.append('vehicleModel', formData.vehicleModel);
      formDataToSubmit.append('vehicleYear', formData.vehicleYear);
      formDataToSubmit.append('vehicleType', formData.vehicleType);
      
      // Add color for raptor and resprays services
      if (formData.serviceType === 'raptor' || formData.serviceType === 'resprays') {
        formDataToSubmit.append('selectedColor', selectedColor);
      } else {
        formDataToSubmit.append('selectedColor', '');
      }
      
      // Add files
      images.forEach((file) => {
        formDataToSubmit.append('files', file);
      });
      
      // Submit to API
      const response = await fetch('/api/contact', {
        method: 'POST',
        body: formDataToSubmit,
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit form');
      }
      
      setSubmitStatus('success');
      
      // Reset form after success
      setTimeout(() => {
        setSubmitStatus('idle');
        setFormData({ 
          name: '', 
          email: '', 
          phone: '', 
          serviceType: 'general', 
          message: '',
          vehicleMake: '',
          vehicleModel: '',
          vehicleYear: '',
          vehicleType: ''
        });
        setAnswers({});
        setImages([]);
        setSelectedColor('#ff0000');
        setErrors({});
        setTouched({});
      }, 3000);
      
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
      setErrors({ 
        ...errors, 
        message: error instanceof Error ? error.message : 'Failed to submit form. Please try again.' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentQuestions = serviceQuestions[formData.serviceType as keyof typeof serviceQuestions] || [];

  if (submitStatus === 'success') {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-12"
      >
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <Check size={32} className="text-white" />
        </div>
        <h3 className="text-2xl font-bold mb-4 gilroy-text-bold">Message Sent!</h3>
        <p className="text-gray-600 mb-4 gilroy-text">
          Thanks for getting in touch. We&apos;ll get back to you within 24 hours.
        </p>
        <p className="text-sm text-gray-500 gilroy-text">
          Need a quicker response? Give us a call on 01603 336 439
        </p>
      </motion.div>
    );
  }

  if (submitStatus === 'error') {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-12"
      >
        <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <XCircle size={32} className="text-white" />
        </div>
        <h3 className="text-2xl font-bold mb-4 gilroy-text-bold">Something went wrong</h3>
        <p className="text-gray-600 mb-4 gilroy-text">
          {errors.message || 'Failed to submit your message. Please try again.'}
        </p>
        <button
          onClick={() => {
            setSubmitStatus('idle');
            setErrors({});
          }}
          className="bg-black text-white px-6 py-3 font-bold uppercase tracking-wider gilroy-text-bold hover:bg-gray-800 transition-colors"
        >
          Try Again
        </button>
      </motion.div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6" noValidate>
      {/* Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
            Name *
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              onBlur={() => handleBlur('name')}
              className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
                errors.name 
                  ? 'border-red-500 bg-red-50' 
                  : formData.name && !errors.name && touched.name
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-300 hover:border-gray-400'
              }`}
              placeholder="Your full name"
              required
            />
            <ValidationStatus
              isValid={formData.name.length > 0 && !errors.name}
              hasError={!!errors.name}
              isActive={touched.name || formData.name.length > 0}
            />
          </div>
          {errors.name && <ErrorAlert message={errors.name} />}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
            Email *
          </label>
          <div className="relative">
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              onBlur={() => handleBlur('email')}
              className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
                errors.email 
                  ? 'border-red-500 bg-red-50' 
                  : formData.email && !errors.email && touched.email
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-300 hover:border-gray-400'
              }`}
              placeholder="<EMAIL>"
              required
            />
            <ValidationStatus
              isValid={formData.email.length > 0 && !errors.email}
              hasError={!!errors.email}
              isActive={touched.email || formData.email.length > 0}
            />
          </div>
          {errors.email && <ErrorAlert message={errors.email} />}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
          Phone *
        </label>
        <div className="relative">
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            onBlur={() => handleBlur('phone')}
            className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
              errors.phone 
                ? 'border-red-500 bg-red-50' 
                : formData.phone && !errors.phone && touched.phone
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-300 hover:border-gray-400'
            }`}
            placeholder="01603 123 456"
            required
          />
          <ValidationStatus
            isValid={formData.phone.length > 0 && !errors.phone}
            hasError={!!errors.phone}
            isActive={touched.phone || formData.phone.length > 0}
          />
        </div>
        {errors.phone && <ErrorAlert message={errors.phone} />}
        <p className="text-xs text-gray-500 mt-1 gilroy-text">UK format preferred</p>
      </div>

      {/* Dynamic Questions */}
      <div className="space-y-4">
        <h4 className="font-bold text-gray-900 gilroy-text-bold">
          Help us understand your needs:
        </h4>
        
        {/* Service Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
            What services interest you? *
          </label>
          <select
            value={formData.serviceType}
            onChange={(e) => handleInputChange('serviceType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-black focus:border-transparent gilroy-text hover:border-gray-400 transition-colors"
            required
          >
            <option value="general">General Enquiry</option>
            <option value="repairs">Vehicle Repairs</option>
            <option value="resprays">Resprays & Paint</option>
            <option value="rust">Rust Treatment</option>
            <option value="raptor">Raptor Coating</option>
            <option value="heaters">Night Heaters</option>
          </select>
        </div>

        {/* Vehicle Information Section */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="font-bold text-gray-900 mb-4 gilroy-text-bold">
            Vehicle Information (Optional)
          </h4>
          <p className="text-sm text-gray-600 mb-4 gilroy-text">
            Providing vehicle details helps us give you a more accurate quote and better service.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Vehicle Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
                Vehicle Type
              </label>
              <select
                value={formData.vehicleType}
                onChange={(e) => handleInputChange('vehicleType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-black focus:border-transparent gilroy-text hover:border-gray-400 transition-colors"
              >
                <option value="">Select vehicle type...</option>
                <option value="car">Car</option>
                <option value="van">Van</option>
                <option value="campervan">Campervan</option>
                <option value="motorhome">Motorhome</option>
                <option value="caravan">Caravan</option>
                <option value="commercial">Commercial Vehicle</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Vehicle Make */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
                Make
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.vehicleMake}
                  onChange={(e) => handleInputChange('vehicleMake', e.target.value)}
                  onBlur={() => handleBlur('vehicleMake')}
                  className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
                    errors.vehicleMake 
                      ? 'border-red-500 bg-red-50' 
                      : formData.vehicleMake && !errors.vehicleMake && touched.vehicleMake
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="e.g., Ford, Volkswagen, Mercedes"
                />
                <ValidationStatus
                  isValid={formData.vehicleMake.length > 0 && !errors.vehicleMake}
                  hasError={!!errors.vehicleMake}
                  isActive={touched.vehicleMake || formData.vehicleMake.length > 0}
                />
              </div>
              {errors.vehicleMake && <ErrorAlert message={errors.vehicleMake} />}
            </div>

            {/* Vehicle Model */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
                Model
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.vehicleModel}
                  onChange={(e) => handleInputChange('vehicleModel', e.target.value)}
                  onBlur={() => handleBlur('vehicleModel')}
                  className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
                    errors.vehicleModel 
                      ? 'border-red-500 bg-red-50' 
                      : formData.vehicleModel && !errors.vehicleModel && touched.vehicleModel
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="e.g., Transit, Crafter, Sprinter"
                />
                <ValidationStatus
                  isValid={formData.vehicleModel.length > 0 && !errors.vehicleModel}
                  hasError={!!errors.vehicleModel}
                  isActive={touched.vehicleModel || formData.vehicleModel.length > 0}
                />
              </div>
              {errors.vehicleModel && <ErrorAlert message={errors.vehicleModel} />}
            </div>

            {/* Vehicle Year */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
                Year
              </label>
              <div className="relative">
                <input
                  type="number"
                  min="1900"
                  max={new Date().getFullYear() + 1}
                  value={formData.vehicleYear}
                  onChange={(e) => handleInputChange('vehicleYear', e.target.value)}
                  onBlur={() => handleBlur('vehicleYear')}
                  className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
                    errors.vehicleYear 
                      ? 'border-red-500 bg-red-50' 
                      : formData.vehicleYear && !errors.vehicleYear && touched.vehicleYear
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="e.g., 2020"
                />
                <ValidationStatus
                  isValid={formData.vehicleYear.length > 0 && !errors.vehicleYear}
                  hasError={!!errors.vehicleYear}
                  isActive={touched.vehicleYear || formData.vehicleYear.length > 0}
                />
              </div>
              {errors.vehicleYear && <ErrorAlert message={errors.vehicleYear} />}
            </div>
          </div>
        </div>

        {/* Service-Specific Questions */}
        {currentQuestions.length > 0 && currentQuestions.map((question, index) => (
          <div key={question.id}>
            <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
              {question.label} {index < 2 && '*'}
            </label>
            <div className="relative">
              <select
                value={answers[question.id] || ''}
                onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors ${
                  errors.questions?.[question.id] 
                    ? 'border-red-500 bg-red-50' 
                    : answers[question.id] && !errors.questions?.[question.id]
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-300 hover:border-gray-400'
                }`}
                required={index < 2}
              >
                <option value="">Select an option...</option>
                {question.options.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
              <ValidationStatus
                isValid={!!answers[question.id] && !errors.questions?.[question.id]}
                hasError={!!errors.questions?.[question.id]}
                isActive={!!answers[question.id] || !!errors.questions?.[question.id]}
              />
            </div>
            {errors.questions?.[question.id] && <ErrorAlert message={errors.questions[question.id]} />}
          </div>
        ))}

        {/* Color Wheel for Raptor and Resprays */}
        {(formData.serviceType === 'raptor' || formData.serviceType === 'resprays') && (
          <div className="mt-6 p-6 border border-gray-200 rounded-lg bg-gray-50">
            <h4 className="text-lg font-bold text-gray-900 mb-4 gilroy-text-bold">
              Choose Your Color
            </h4>
            <p className="text-sm text-gray-600 mb-4 gilroy-text">
              Select any color from the wheel below. This helps us understand your vision and provide a more accurate quote.
            </p>
            <ColorWheel 
              onColorChange={setSelectedColor} 
              selectedColor={selectedColor} 
            />
          </div>
        )}
      </div>

      {/* Image Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
          Photos (optional) - Max {MAX_FILES} images, 10MB each
        </label>
        <div
          className={`border-2 border-dashed p-6 text-center transition-colors ${
            isDragging 
              ? 'border-black bg-gray-50' 
              : errors.files 
                ? 'border-red-500 bg-red-50' 
                : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className={`mx-auto h-12 w-12 mb-4 ${errors.files ? 'text-red-400' : 'text-gray-400'}`} />
          <p className="text-gray-600 mb-2 gilroy-text">
            Drag photos here or{' '}
            <label className="text-black font-medium cursor-pointer hover:underline">
              browse files
              <input
                type="file"
                multiple
                accept={ALLOWED_FILE_TYPES.join(',')}
                onChange={(e) => handleImageUpload(e.target.files)}
                className="hidden"
              />
            </label>
          </p>
          <p className="text-xs text-gray-500 gilroy-text">
            Supported formats: JPEG, PNG, GIF, WebP, BMP, TIFF
          </p>
          <p className="text-xs text-gray-500 gilroy-text">
            {images.length}/{MAX_FILES} files uploaded
          </p>
        </div>
        {errors.files && <ErrorAlert message={errors.files} />}

        {/* Image Previews */}
        {images.length > 0 && (
          <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
            {images.map((file, index) => (
              <div key={index} className="relative group">
                <img
                  src={URL.createObjectURL(file)}
                  alt={`Upload ${index + 1}`}
                  className="w-full h-24 object-cover border border-gray-200 group-hover:border-gray-400 transition-colors"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors shadow-lg"
                  title="Remove image"
                >
                  <X size={16} />
                </button>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b truncate">
                  {file.name}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Additional Message */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2 gilroy-text-medium">
          Additional details (optional)
        </label>
        <div className="relative">
          <textarea
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            onBlur={() => handleBlur('message')}
            rows={4}
            className={`w-full px-4 py-3 pr-12 border focus:ring-2 focus:ring-black focus:border-transparent gilroy-text transition-colors resize-vertical ${
              errors.message 
                ? 'border-red-500 bg-red-50' 
                : formData.message && !errors.message && formData.message.length > 0
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-300 hover:border-gray-400'
            }`}
            placeholder="Tell us more about your project..."
            maxLength={1000}
          />
          {/* Validation Status for textarea positioned at top-right */}
          {(formData.message.length > 0) && (
            <div className="absolute right-3 top-3">
              {errors.message ? (
                <XCircle size={20} className="text-red-500" />
              ) : (
                <CheckCircle size={20} className="text-green-500" />
              )}
            </div>
          )}
        </div>
        {errors.message && <ErrorAlert message={errors.message} />}
        <div className="flex justify-between items-center mt-1">
          <p className="text-xs text-gray-500 gilroy-text">
            Help us understand your specific needs and timeline
          </p>
          <p className={`text-xs gilroy-text ${
            formData.message.length > 900 
              ? 'text-orange-600 font-medium' 
              : formData.message.length > 950 
                ? 'text-red-600 font-medium' 
                : 'text-gray-500'
          }`}>
            {formData.message.length}/1000 characters
          </p>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-black text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed gilroy-text-bold transition-all hover:shadow-lg"
      >
        {isSubmitting ? (
          <span className="flex items-center justify-center space-x-2">
            <Loader2 size={20} className="animate-spin" />
            <span>Sending...</span>
          </span>
        ) : (
          <>
            <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">
              Send Message
            </span>
            <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
            <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
              Send Message
            </span>
          </>
        )}
      </button>
      
      {/* Form Help Text */}
      <div className="text-center text-sm text-gray-500 gilroy-text">
        <p>All fields marked with * are required</p>
        <p className="mt-1">We typically respond within 24 hours</p>
      </div>
    </form>
  );
};

// Company stats for the redesigned header
const companyStats = [
  { label: "Response Time", value: "< 2 hours", icon: Clock },
  { label: "Projects Done", value: "500+", icon: CheckCircle },
  { label: "Years Experience", value: "15+", icon: Shield },
  { label: "Success Rate", value: "99.9%", icon: Zap }
];

// Contact methods with JCC's actual information
const contactMethods = [
  {
    icon: Mail,
    title: "Email Us",
    description: "Get a quote or ask questions",
    value: "<EMAIL>",
    link: "mailto:<EMAIL>",
    gradient: "from-blue-500/20 to-cyan-500/20",
    hoverColor: "blue"
  },
  {
    icon: Phone,
    title: "Call Us",
    description: "Speak directly with our team",
    value: "+44 1234 567890",
    link: "tel:+441234567890",
    gradient: "from-green-500/20 to-emerald-500/20",
    hoverColor: "green"
  },
  {
    icon: MapPin,
    title: "Visit Us",
    description: "Our workshop location",
    value: "Workshop Address, UK",
    link: "#location",
    gradient: "from-purple-500/20 to-pink-500/20",
    hoverColor: "purple"
  }
];

export default function ContactPage() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      <Header />
      
      {/* Hero Section - Black background like home page */}
      <section className="relative min-h-[60vh] flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Grid Pattern Overlay - same as home page */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)] z-10"></div>
        
        <div className="relative z-20 text-center max-w-5xl mx-auto px-4">
          <motion.h1 
            className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-black uppercase tracking-tight text-white mb-6 md:mb-8 leading-none"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Let&apos;s Chat
          </motion.h1>
          
          {/* White line divider - same as home page */}
          <div className="w-20 md:w-24 h-1 bg-white mx-auto mb-6 md:mb-8"></div>
          
          <motion.p 
            className="gilroy-text text-lg md:text-xl lg:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Ready to give your vehicle some love? Let&apos;s make it happen!
          </motion.p>
        </div>
      </section>

      {/* Contact Section - White background like home page sections */}
      <section id="contact" className="py-20 md:py-32 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16">
            {/* Left Column - Contact Info */}
            <div className="space-y-6 md:space-y-8">
              {/* Contact Info Card */}
              <div className="bg-white text-black p-6 md:p-8 lg:p-12">
                <h3 className="gilroy-text-bold text-2xl md:text-3xl font-black uppercase tracking-tight mb-6 md:mb-8 text-black">
                  Get In Touch
                </h3>
                <div className="space-y-6 md:space-y-8">
                  <a href="tel:01603336439" className="flex items-center space-x-4 md:space-x-6 touch-manipulation group p-2 -m-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-black text-white flex items-center justify-center">
                      <Phone size={20} className="md:w-6 md:h-6" />
                    </div>
                    <div>
                      <p className="gilroy-text text-xs md:text-sm text-gray-600 uppercase tracking-wider mb-1">Phone</p>
                      <span className="gilroy-text text-lg md:text-xl font-bold text-black group-hover:text-gray-700 transition-colors">01603 336 439</span>
                    </div>
                  </a>
                  <a href="mailto:<EMAIL>" className="flex items-center space-x-4 md:space-x-6 touch-manipulation group p-2 -m-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-black text-white flex items-center justify-center">
                      <Mail size={20} className="md:w-6 md:h-6" />
                    </div>
                    <div>
                      <p className="gilroy-text text-xs md:text-sm text-gray-600 uppercase tracking-wider mb-1">Email</p>
                      <span className="gilroy-text text-sm md:text-lg font-bold text-black break-all group-hover:text-gray-700 transition-colors"><EMAIL></span>
                    </div>
                  </a>
                  <div className="flex items-center space-x-4 md:space-x-6">
                    <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-black text-white flex items-center justify-center">
                      <MapPin size={20} className="md:w-6 md:h-6" />
                    </div>
                    <div>
                      <p className="gilroy-text text-xs md:text-sm text-gray-600 uppercase tracking-wider mb-1">Location</p>
                      <span className="gilroy-text text-base md:text-lg font-bold text-black">Frost Industrial Estate<br />Norwich, NR8 6AP</span>
                    </div>
                  </div>
                  <a href="https://instagram.com/jcc_.ltd" target="_blank" rel="noopener noreferrer" className="flex items-center space-x-4 md:space-x-6 touch-manipulation group p-2 -m-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-black text-white flex items-center justify-center">
                      <Instagram size={20} className="md:w-6 md:h-6" />
                    </div>
                    <div>
                      <p className="gilroy-text text-xs md:text-sm text-gray-600 uppercase tracking-wider mb-1">Instagram</p>
                      <span className="gilroy-text text-base md:text-lg font-bold text-black group-hover:text-gray-700 transition-colors">@jcc_.ltd</span>
                    </div>
                  </a>
                </div>
              </div>

              {/* Map Section */}
              <div className="relative w-full h-80 md:h-96 lg:h-[400px] overflow-hidden">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2429.123456789!2d1.297!3d52.628!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTLCsDM3JzQxLjAiTiAxwrAxNyc0OS4wIkU!5e0!3m2!1sen!2suk!4v1234567890123!5m2!1sen!2suk&q=Frost+Industrial+Estate,+Norwich,+NR8+6AP"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen={false}
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="pointer-events-none"
                  title="JCC Customs and Commercial Location - Frost Industrial Estate, Norwich"
                ></iframe>
                <div className="absolute inset-0 bg-transparent pointer-events-none"></div>
              </div>
              
              {/* WhatsApp Quick Response Card */}
              <div className="bg-black text-white p-6 md:p-8 lg:p-12 border border-gray-800">
                <h3 className="gilroy-text-bold text-xl md:text-2xl lg:text-3xl font-black uppercase tracking-tight mb-4 md:mb-6 text-white">
                  Quick Response
                </h3>
                <p className="gilroy-text text-base md:text-lg text-gray-300 mb-6 md:mb-8 leading-relaxed">
                  Need a quick estimate or some advice? Just WhatsApp us some pics and we&apos;ll get back to you <span className="text-white font-bold">super fast</span>!
                </p>
                <button className="bg-white text-black px-5 md:px-6 py-3 md:py-3 text-sm md:text-base font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95 gilroy-text-bold" style={{ minHeight: '48px' }}>
                  <span className="relative z-10 flex items-center space-x-2 md:space-x-3 group-hover:opacity-0 transition-opacity duration-300">
                    <MessageCircle size={18} className="md:w-5 md:h-5" />
                    <span>WhatsApp Us</span>
                  </span>
                  <div className="absolute inset-0 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                  <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                    <div className="flex items-center space-x-2 md:space-x-3">
                      <MessageCircle size={18} className="md:w-5 md:h-5" />
                      <span>WhatsApp Us</span>
                    </div>
                  </span>
                </button>
              </div>
            </div>

            {/* Right Column - Dynamic Contact Form */}
            <div className="bg-white text-black p-6 md:p-8 lg:p-12">
              <h3 className="gilroy-text-bold text-2xl md:text-3xl font-black uppercase tracking-tight mb-6 md:mb-8 text-black">
                Tell Us What You Need
              </h3>
              <DynamicContactForm />
            </div>
          </div>

          {/* Bottom Info Bar */}
          <div className="mt-16 md:mt-20 text-center bg-black py-8 md:py-12 px-6 md:px-8">
            <p className="gilroy-text text-base md:text-lg text-gray-300 mb-2">
              <span className="text-white font-bold">Just give us a shout first</span> - We&apos;ll make sure someone&apos;s here to see you
            </p>
            <p className="gilroy-text text-xs md:text-sm text-gray-400 uppercase tracking-wider">
              We&rsquo;re here Monday to Friday, 8AM - 6PM
            </p>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
}
