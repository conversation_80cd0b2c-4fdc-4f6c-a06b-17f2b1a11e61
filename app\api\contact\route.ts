import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schema for contact form
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  serviceType: z.string().min(1, 'Service type is required'),
  message: z.string().max(1000, 'Message must be less than 1000 characters').optional().default(''),
  serviceQuestions: z.record(z.string(), z.string()).optional(),
  selectedColor: z.string().optional().nullable().transform(val => val === '' ? null : val),
  // Vehicle details
  vehicleMake: z.string().optional().transform(val => val === '' ? null : val),
  vehicleModel: z.string().optional().transform(val => val === '' ? null : val),
  vehicleYear: z.string().optional().transform(val => val === '' ? null : (val ? parseInt(val) : null)),
  vehicleType: z.string().optional().transform(val => val === '' ? null : val),
})

// File validation
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/tiff',
  'image/svg+xml',
  'image/heic',
  'image/heif',
  'image/avif',
  'image/ico',
  'application/pdf',
  'application/msword'
]

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const MAX_FILES = 5

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Parse form data
    const formData = await request.formData()
    
    // Extract form fields
    const name = formData.get('name') as string
    const email = formData.get('email') as string
    const phone = formData.get('phone') as string
    const serviceType = formData.get('serviceType') as string
    const message = formData.get('message') as string || ''
    const serviceQuestions = formData.get('serviceQuestions') ? 
      JSON.parse(formData.get('serviceQuestions') as string) : {}
    const selectedColor = formData.get('selectedColor') as string || null
    
    // Extract vehicle details
    const vehicleMake = formData.get('vehicleMake') as string || ''
    const vehicleModel = formData.get('vehicleModel') as string || ''
    const vehicleYear = formData.get('vehicleYear') as string || ''
    const vehicleType = formData.get('vehicleType') as string || ''
    
    // Validate form data
    const validatedData = contactFormSchema.parse({
      name,
      email,
      phone,
      serviceType,
      message,
      serviceQuestions,
      selectedColor,
      vehicleMake,
      vehicleModel,
      vehicleYear,
      vehicleType
    })
    
    // Handle file uploads
    const files = formData.getAll('files') as File[]
    const attachmentUrls: string[] = []
    const attachmentNames: string[] = []
    
    if (files.length > 0) {
      // Validate files
      if (files.length > MAX_FILES) {
        return NextResponse.json(
          { error: `Maximum ${MAX_FILES} files allowed` },
          { status: 400 }
        )
      }
      
      for (const file of files) {
        // Validate file type
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          return NextResponse.json(
            { error: `Invalid file type: ${file.name}. Only images are allowed.` },
            { status: 400 }
          )
        }
        
        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
          return NextResponse.json(
            { error: `File too large: ${file.name}. Maximum size is 10MB.` },
            { status: 400 }
          )
        }
      }
      
      // Upload files to Supabase Storage
      for (const file of files) {
        const fileExt = file.name.split('.').pop()
        const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`
        const filePath = `contact-attachments/${fileName}`
        
        // Convert File to ArrayBuffer
        const fileBuffer = await file.arrayBuffer()
        
        const { data, error } = await supabase.storage
          .from('contact-attachments')
          .upload(filePath, fileBuffer, {
            contentType: file.type,
          })
        
        if (error) {
          console.error('File upload error:', error)
          return NextResponse.json(
            { error: `Failed to upload file: ${file.name}` },
            { status: 500 }
          )
        }
        
        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('contact-attachments')
          .getPublicUrl(filePath)
        
        attachmentUrls.push(publicUrl)
        attachmentNames.push(file.name)
      }
    }
    
    // Insert contact submission into database
    const { data: contact, error: dbError } = await supabase
      .from('contact_submissions')
      .insert({
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        category: validatedData.serviceType,
        service_type: validatedData.serviceType,
        message: validatedData.message,
        service_questions: validatedData.serviceQuestions,
        selected_color: validatedData.selectedColor,
        vehicle_make: validatedData.vehicleMake,
        vehicle_model: validatedData.vehicleModel,
        vehicle_year: validatedData.vehicleYear,
        vehicle_type: validatedData.vehicleType,
        attachments: attachmentNames,
        attachment_urls: attachmentUrls,
        status: 'new',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (dbError) {
      console.error('Database error:', dbError)
      return NextResponse.json(
        { error: 'Failed to save contact submission' },
        { status: 500 }
      )
    }
    
    // TODO: Send notification email to admin
    // TODO: Send confirmation email to customer
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'Contact form submitted successfully',
        contactId: contact.id
      },
      { status: 201 }
    )
    
  } catch (error) {
    console.error('Contact form submission error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid form data', details: error.issues },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 