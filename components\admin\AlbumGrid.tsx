'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import {
    FolderOpen,
    Plus,
    Edit,
    Trash2,
    Eye,
    Star,
    Image as ImageIcon,
    Calendar,
    Upload,
    AlertTriangle
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { createClient } from '@/lib/supabase'
import MediaGalleryPicker from '@/components/admin/MediaGalleryPicker'

interface Album {
    id: string
    title: string
    description: string
    slug: string
    cover_image_url?: string
    cover_image_alt?: string
    is_featured: boolean
    is_published: boolean
    order_index: number
    created_at: string
    updated_at: string
    image_count?: number
}

interface AlbumFormData {
    title: string
    description: string
    slug: string
    coverImageUrl: string
    coverImageAlt: string
    isFeatured: boolean
    isPublished: boolean
    orderIndex: number
}

export default function AlbumGrid() {
    const [albums, setAlbums] = useState<Album[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState('')
    const [isCreateOpen, setIsCreateOpen] = useState(false)
    const [editingAlbum, setEditingAlbum] = useState<Album | null>(null)
    const [deletingAlbum, setDeletingAlbum] = useState<Album | null>(null)
    const [isGalleryPickerOpen, setIsGalleryPickerOpen] = useState(false)
    const [isImageAssignmentOpen, setIsImageAssignmentOpen] = useState(false)
    const [assigningToAlbum, setAssigningToAlbum] = useState<Album | null>(null)
    const [formData, setFormData] = useState<AlbumFormData>({
        title: '',
        description: '',
        slug: '',
        coverImageUrl: '',
        coverImageAlt: '',
        isFeatured: false,
        isPublished: true,
        orderIndex: 0
    })

    const supabase = createClient()

    // Fetch albums with image counts
    useEffect(() => {
        fetchAlbums()
    }, [])

    const fetchAlbums = async () => {
        try {
            setLoading(true)

            // Get albums
            const { data: albumsData, error: albumsError } = await supabase
                .from('gallery_albums')
                .select('*')
                .order('order_index', { ascending: true })
                .order('created_at', { ascending: false })

            if (albumsError) throw albumsError

            // Get image counts for each album
            const albumsWithCounts = await Promise.all(
                (albumsData || []).map(async (album) => {
                    const { count } = await supabase
                        .from('gallery_images')
                        .select('*', { count: 'exact', head: true })
                        .eq('album_id', album.id)

                    return { ...album, image_count: count || 0 }
                })
            )

            setAlbums(albumsWithCounts)
        } catch (err) {
            console.error('Error fetching albums:', err)
            setError('Failed to load albums')
        } finally {
            setLoading(false)
        }
    }

    // Generate slug from title
    const generateSlug = (title: string) => {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
    }

    const handleTitleChange = (title: string) => {
        setFormData(prev => ({
            ...prev,
            title,
            slug: generateSlug(title)
        }))
    }

    const resetForm = () => {
        setFormData({
            title: '',
            description: '',
            slug: '',
            coverImageUrl: '',
            coverImageAlt: '',
            isFeatured: false,
            isPublished: true,
            orderIndex: 0
        })
    }

    const handleCreate = () => {
        resetForm()
        setEditingAlbum(null)
        setIsCreateOpen(true)
    }

    const handleEdit = (album: Album) => {
        setFormData({
            title: album.title,
            description: album.description || '',
            slug: album.slug,
            coverImageUrl: album.cover_image_url || '',
            coverImageAlt: album.cover_image_alt || '',
            isFeatured: album.is_featured,
            isPublished: album.is_published,
            orderIndex: album.order_index
        })
        setEditingAlbum(album)
        setIsCreateOpen(true)
    }

    const handleAddImages = (album: Album) => {
        setAssigningToAlbum(album)
        setIsImageAssignmentOpen(true)
    }

    const handleImageAssignment = (result: any) => {
        if (result.assigned && result.assigned > 0) {
            // Refresh the albums list to update image counts
            fetchAlbums()
            alert(`Successfully assigned ${result.assigned} image(s) to "${assigningToAlbum?.title}"`)
        }
        setAssigningToAlbum(null)
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        try {
            if (editingAlbum) {
                // Update existing album
                const { error } = await supabase
                    .from('gallery_albums')
                    .update({
                        title: formData.title,
                        description: formData.description || null,
                        slug: formData.slug,
                        cover_image_url: formData.coverImageUrl || null,
                        cover_image_alt: formData.coverImageAlt || null,
                        is_featured: formData.isFeatured,
                        is_published: formData.isPublished,
                        order_index: formData.orderIndex
                    })
                    .eq('id', editingAlbum.id)

                if (error) throw error
            } else {
                // Create new album
                const { error } = await supabase
                    .from('gallery_albums')
                    .insert({
                        title: formData.title,
                        description: formData.description || null,
                        slug: formData.slug,
                        cover_image_url: formData.coverImageUrl || null,
                        cover_image_alt: formData.coverImageAlt || null,
                        is_featured: formData.isFeatured,
                        is_published: formData.isPublished,
                        order_index: formData.orderIndex
                    })

                if (error) throw error
            }

            setIsCreateOpen(false)
            setEditingAlbum(null)
            resetForm()
            fetchAlbums()
        } catch (err: any) {
            console.error('Error saving album:', err)
            setError(err.message || 'Failed to save album')
        }
    }

    const handleDelete = async () => {
        if (!deletingAlbum) return

        try {
            const { error } = await supabase
                .from('gallery_albums')
                .delete()
                .eq('id', deletingAlbum.id)

            if (error) throw error

            setDeletingAlbum(null)
            fetchAlbums()
        } catch (err: any) {
            console.error('Error deleting album:', err)
            setError(err.message || 'Failed to delete album')
        }
    }

    if (loading) {
        return (
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium gilroy-text-bold">Albums</h3>
                    <Button disabled className="gilroy-text-medium">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Album
                    </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Array(6).fill(0).map((_, i) => (
                        <Card key={i} className="animate-pulse">
                            <div className="aspect-video bg-gray-200 rounded-t-lg" />
                            <CardContent className="p-4">
                                <div className="h-4 bg-gray-200 rounded mb-2" />
                                <div className="h-3 bg-gray-200 rounded w-2/3" />
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-medium gilroy-text-bold">Albums</h3>
                    <p className="text-sm text-gray-600 gilroy-text">Organize your gallery images into albums</p>
                </div>
                <Button onClick={handleCreate} className="gilroy-text-medium">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Album
                </Button>
            </div>

            {/* Error Display */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
                        <span className="text-red-700 text-sm gilroy-text">{error}</span>
                    </div>
                </div>
            )}

            {/* Albums Grid */}
            {albums.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {albums.map((album) => (
                        <Card key={album.id} className="group hover:shadow-lg transition-all duration-200">
                            <div className="relative aspect-video overflow-hidden rounded-t-lg bg-gray-100">
                                {album.cover_image_url ? (
                                    <Image
                                        src={album.cover_image_url}
                                        alt={album.cover_image_alt || album.title}
                                        fill
                                        className="object-cover group-hover:scale-105 transition-transform duration-200"
                                    />
                                ) : (
                                    <div className="flex items-center justify-center h-full bg-gray-100">
                                        <FolderOpen className="h-12 w-12 text-gray-400" />
                                    </div>
                                )}

                                {/* Status badges */}
                                <div className="absolute top-2 left-2 space-y-1">
                                    {album.is_featured && (
                                        <Badge className="bg-yellow-500 text-white text-xs">
                                            <Star className="h-3 w-3 mr-1" />
                                            Featured
                                        </Badge>
                                    )}
                                    {!album.is_published && (
                                        <Badge variant="secondary" className="text-xs">
                                            Draft
                                        </Badge>
                                    )}
                                </div>

                                {/* Image count */}
                                <div className="absolute top-2 right-2">
                                    <Badge variant="outline" className="bg-white/90 text-xs">
                                        <ImageIcon className="h-3 w-3 mr-1" />
                                        {album.image_count}
                                    </Badge>
                                </div>
                            </div>

                            <CardContent className="p-4">
                                <div className="space-y-2">
                                    <h4 className="font-medium gilroy-text-bold line-clamp-1">{album.title}</h4>
                                    {album.description && (
                                        <p className="text-sm text-gray-600 gilroy-text line-clamp-2">{album.description}</p>
                                    )}

                                    <div className="flex items-center justify-between text-xs text-gray-500 gilroy-text">
                                        <span>Order: {album.order_index}</span>
                                        <span>{new Date(album.created_at).toLocaleDateString()}</span>
                                    </div>

                                    {/* Actions */}
                                    <div className="flex items-center space-x-2 pt-2">
                                        <Link href={`/gallery/albums/${album.slug}`} target="_blank">
                                            <Button variant="ghost" size="sm" className="gilroy-text">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </Link>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleAddImages(album)}
                                            className="gilroy-text"
                                            title="Add images to album"
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleEdit(album)}
                                            className="gilroy-text"
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setDeletingAlbum(album)}
                                            className="text-red-600 hover:text-red-700 gilroy-text"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            ) : (
                <Card className="p-8 text-center">
                    <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2 gilroy-text-bold">No Albums Yet</h4>
                    <p className="text-gray-600 mb-4 gilroy-text">Create your first album to organize gallery images</p>
                    <Button onClick={handleCreate} className="gilroy-text-medium">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Album
                    </Button>
                </Card>
            )}

            {/* Create/Edit Album Dialog */}
            <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle className="gilroy-text-bold">
                            {editingAlbum ? 'Edit Album' : 'Create New Album'}
                        </DialogTitle>
                        <DialogDescription className="gilroy-text">
                            {editingAlbum ? 'Update album details and settings' : 'Create a new album to organize your gallery images'}
                        </DialogDescription>
                    </DialogHeader>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="title" className="gilroy-text-medium">Title</Label>
                                <Input
                                    id="title"
                                    value={formData.title}
                                    onChange={(e) => handleTitleChange(e.target.value)}
                                    placeholder="Album title"
                                    required
                                    className="gilroy-text"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="slug" className="gilroy-text-medium">Slug</Label>
                                <Input
                                    id="slug"
                                    value={formData.slug}
                                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                                    placeholder="album-slug"
                                    required
                                    className="gilroy-text"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description" className="gilroy-text-medium">Description</Label>
                            <Textarea
                                id="description"
                                value={formData.description}
                                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                placeholder="Album description"
                                rows={3}
                                className="gilroy-text"
                            />
                        </div>

                        <div className="space-y-4">
                            <div>
                                <Label className="gilroy-text-medium">Cover Image</Label>
                                <div className="mt-2 space-y-3">
                                    {formData.coverImageUrl ? (
                                        <div className="relative w-full h-48 border rounded-lg overflow-hidden bg-gray-100">
                                            <Image
                                                src={formData.coverImageUrl}
                                                alt={formData.coverImageAlt || 'Cover image'}
                                                fill
                                                className="object-cover"
                                            />
                                        </div>
                                    ) : (
                                        <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                                            <div className="text-center">
                                                <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                                                <p className="text-sm text-gray-500">No cover image selected</p>
                                            </div>
                                        </div>
                                    )}
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsGalleryPickerOpen(true)}
                                        className="w-full gilroy-text"
                                    >
                                        <ImageIcon className="h-4 w-4 mr-2" />
                                        {formData.coverImageUrl ? 'Change Cover Image' : 'Select Cover Image'}
                                    </Button>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="coverImageAlt" className="gilroy-text-medium">Cover Image Alt Text</Label>
                                <Input
                                    id="coverImageAlt"
                                    value={formData.coverImageAlt}
                                    onChange={(e) => setFormData(prev => ({ ...prev, coverImageAlt: e.target.value }))}
                                    placeholder="Descriptive text for cover image"
                                    className="gilroy-text"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="orderIndex" className="gilroy-text-medium">Order Index</Label>
                            <Input
                                id="orderIndex"
                                type="number"
                                value={formData.orderIndex}
                                onChange={(e) => setFormData(prev => ({ ...prev, orderIndex: parseInt(e.target.value) || 0 }))}
                                placeholder="0"
                                className="gilroy-text"
                            />
                        </div>

                        <div className="flex items-center space-x-6">
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="isFeatured"
                                    checked={formData.isFeatured}
                                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
                                />
                                <Label htmlFor="isFeatured" className="gilroy-text-medium">Featured Album</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="isPublished"
                                    checked={formData.isPublished}
                                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isPublished: checked }))}
                                />
                                <Label htmlFor="isPublished" className="gilroy-text-medium">Published</Label>
                            </div>
                        </div>

                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsCreateOpen(false)}
                                className="gilroy-text"
                            >
                                Cancel
                            </Button>
                            <Button type="submit" className="gilroy-text-medium">
                                {editingAlbum ? 'Update Album' : 'Create Album'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            {/* Media Gallery Picker for Cover Image */}
            <MediaGalleryPicker
                isOpen={isGalleryPickerOpen}
                onClose={() => setIsGalleryPickerOpen(false)}
                onSelect={(image) => setFormData(prev => ({
                    ...prev,
                    coverImageUrl: image.image_url,
                    coverImageAlt: image.alt_text || image.title
                }))}
            />

            {/* Media Gallery Picker for Album Assignment */}
            <MediaGalleryPicker
                isOpen={isImageAssignmentOpen}
                onClose={() => setIsImageAssignmentOpen(false)}
                onSelect={handleImageAssignment}
                mode="assign-to-album"
                albumId={assigningToAlbum?.id}
            />

            {/* Delete Confirmation Dialog */}
            <Dialog open={!!deletingAlbum} onOpenChange={() => setDeletingAlbum(null)}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle className="gilroy-text-bold">Delete Album</DialogTitle>
                        <DialogDescription className="gilroy-text">
                            Are you sure you want to delete "{deletingAlbum?.title}"? This action cannot be undone.
                            Images in this album will not be deleted, they'll just be unassigned from the album.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setDeletingAlbum(null)}
                            className="gilroy-text"
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDelete}
                            className="gilroy-text-medium"
                        >
                            Delete Album
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
