"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, ArrowUpR<PERSON> } from "lucide-react";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

export interface Gallery4Item {
  id: string;
  title: string;
  description: string;
  href: string;
  image: string;
}

export interface Gallery4Props {
  title?: string;
  description?: string;
  items: Gallery4Item[];
}

const data = [
  {
    id: "shadcn-ui",
    title: "shadcn/ui: Building a Modern Component Library",
    description:
      "Explore how shadcn/ui revolutionized React component libraries by providing a unique approach to component distribution and customization, making it easier for developers to build beautiful, accessible applications.",
    href: "https://ui.shadcn.com",
    image:
      "https://images.unsplash.com/photo-1551250928-243dc937c49d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w2NDI3NzN8MHwxfGFsbHwxMjN8fHx8fHwyfHwxNzIzODA2OTM5fA&ixlib=rb-4.0.3&q=80&w=1080",
  },
  {
    id: "tailwind",
    title: "Tailwind CSS: The Utility-First Revolution",
    description:
      "Discover how Tailwind CSS transformed the way developers style their applications, offering a utility-first approach that speeds up development while maintaining complete design flexibility.",
    href: "https://tailwindcss.com",
    image:
      "https://images.unsplash.com/photo-1551250928-e4a05afaed1e?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w2NDI3NzN8MHwxfGFsbHwxMjR8fHx8fHwyfHwxNzIzODA2OTM5fA&ixlib=rb-4.0.3&q=80&w=1080",
  },
  {
    id: "astro",
    title: "Astro: The All-in-One Web Framework",
    description:
      "Learn how Astro's innovative 'Islands Architecture' and zero-JS-by-default approach is helping developers build faster websites while maintaining rich interactivity where needed.",
    href: "https://astro.build",
    image:
      "https://images.unsplash.com/photo-1536735561749-fc87494598cb?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w2NDI3NzN8MHwxfGFsbHwxNzd8fHx8fHwyfHwxNzIzNjM0NDc0fA&ixlib=rb-4.0.3&q=80&w=1080",
  },
  {
    id: "react",
    title: "React: Pioneering Component-Based UI",
    description:
      "See how React continues to shape modern web development with its component-based architecture, enabling developers to build complex user interfaces with reusable, maintainable code.",
    href: "https://react.dev",
    image:
      "https://images.unsplash.com/photo-**********-9133768e4094?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=******************************************************&ixlib=rb-4.0.3&q=80&w=1080",
  },
  {
    id: "nextjs",
    title: "Next.js: The React Framework for Production",
    description:
      "Explore how Next.js has become the go-to framework for building full-stack React applications, offering features like server components, file-based routing, and automatic optimization.",
    href: "https://nextjs.org",
    image:
      "https://images.unsplash.com/photo-**********-a5d71eda5800?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=******************************************************&ixlib=rb-4.0.3&q=80&w=1080",
  },
];

const Gallery4 = ({
  title = "Case Studies",
  description = "Discover how leading companies and developers are leveraging modern web technologies to build exceptional digital experiences. These case studies showcase real-world applications and success stories.",
  items = data,
}: Gallery4Props) => {
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [scrollSnapList, setScrollSnapList] = useState<number[]>([]);
  const totalSlides = scrollSnapList.length || items.length;

  useEffect(() => {
    if (!carouselApi) {
      return;
    }
    const updateSelection = () => {
      const snaps = carouselApi.scrollSnapList();
      setScrollSnapList(snaps);
      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());
      setCurrentSlide(carouselApi.selectedScrollSnap());
    };
    updateSelection();
    carouselApi.on("select", updateSelection);
    carouselApi.on("reInit", updateSelection);
    return () => {
      carouselApi.off("select", updateSelection);
      carouselApi.off("reInit", updateSelection);
    };
  }, [carouselApi]);

  return (
    <section className="py-20 md:py-32 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Modern header with better visual hierarchy */}
        <div className="mb-12 md:mb-16 lg:mb-20 flex flex-col md:flex-row md:items-end md:justify-between gap-8 md:gap-12">
          {/* Title section with enhanced typography */}
          <div className="flex-1 max-w-4xl">
            
            <h2 className="gilroy-text-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black uppercase tracking-tight mb-6 md:mb-8 leading-none text-black">
              {title}
            </h2>
            
            <p className="gilroy-text text-lg md:text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-2xl">
              {description}
            </p>
          </div>
          
          {/* Navigation buttons with modern styling */}
          <div className="flex shrink-0 items-center gap-3">
            <Button
              size="icon"
              variant="outline"
              onClick={() => {
                carouselApi?.scrollPrev();
              }}
              disabled={!canScrollPrev}
              className="disabled:pointer-events-auto h-12 w-12 md:h-14 md:w-14 border-2 border-gray-200 hover:border-black hover:bg-black hover:text-white disabled:border-gray-100 disabled:bg-gray-50 disabled:text-gray-300 transition-all duration-200"
            >
              <ArrowLeft className="size-5 md:size-6" />
            </Button>
            <Button
              size="icon"
              variant="outline"
              onClick={() => {
                carouselApi?.scrollNext();
              }}
              disabled={!canScrollNext}
              className="disabled:pointer-events-auto h-12 w-12 md:h-14 md:w-14 border-2 border-gray-200 hover:border-black hover:bg-black hover:text-white disabled:border-gray-100 disabled:bg-gray-50 disabled:text-gray-300 transition-all duration-200"
            >
              <ArrowRight className="size-5 md:size-6" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Enhanced carousel with better spacing */}
      <div className="w-full">
        <Carousel
          setApi={setCarouselApi}
          opts={{
            align: "start",
            loop: false,
            slidesToScroll: 1,
            containScroll: "trimSnaps",
            breakpoints: {
              "(max-width: 768px)": {
                dragFree: false,
              },
            },
          }}
          className="relative"
        >
          <CarouselContent className="ml-0 2xl:ml-[max(8rem,calc(50vw-700px))] 2xl:mr-[max(0rem,calc(50vw-700px))]">
            {items.map((item) => (
              <CarouselItem
                key={item.id}
                className="pl-4 md:max-w-[420px] lg:max-w-[480px]"
              >
                <a
                  href={item.href}
                  className="group flex flex-col justify-between h-full"
                >
                  {/* Enhanced image container */}
                  <div className="flex aspect-[3/2] overflow-hidden rounded-2xl mb-6 relative">
                    <div className="flex-1 relative">
                      <div className="relative h-full w-full origin-bottom transition duration-500 group-hover:scale-105">
                        <img
                          src={item.image}
                          alt={item.title}
                          className="h-full w-full object-cover object-center"
                        />
                        {/* Subtle overlay for better text readability */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Enhanced content section */}
                  <div className="flex-1 flex flex-col">
                    <h3 className="gilroy-text-bold mb-3 line-clamp-2 break-words text-xl md:text-2xl lg:text-3xl font-black uppercase tracking-tight text-black leading-tight group-hover:text-gray-700 transition-colors duration-200">
                      {item.title}
                    </h3>
                    
                    <p className="gilroy-text mb-6 md:mb-8 line-clamp-3 text-base md:text-lg text-gray-600 leading-relaxed flex-1">
                      {item.description}
                    </p>
                    
                    {/* Enhanced call-to-action */}
                    <div className="flex items-center gilroy-text-medium text-sm md:text-base font-medium uppercase tracking-wider text-black group-hover:text-gray-600 transition-colors duration-200">
                      Learn More
                      <ArrowUpRight className="ml-2 size-4 md:size-5 transition-transform group-hover:translate-x-1 group-hover:-translate-y-1" />
                    </div>
                  </div>
                </a>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
        
        {/* Synced dot navigation */}
        <div className="mt-12 md:mt-16 flex justify-center gap-3 md:gap-4">
          {scrollSnapList.length > 0 ? (
            scrollSnapList
              .filter((_, index) => index !== 4) // Remove the 5th dot (index 4)
              .map((_, index) => {
                const isActive = currentSlide === index;
                
                return (
                  <button
                    key={index}
                    className={`h-2 w-8 md:h-3 md:w-12 rounded-full transition-all duration-300 touch-manipulation ${
                      isActive ? "bg-black" : "bg-gray-300 hover:bg-gray-400"
                    }`}
                    onClick={() => {
                      if (carouselApi) {
                        carouselApi.scrollTo(index);
                      }
                    }}
                    aria-label={`Go to slide ${index + 1}`}
                    style={{ minWidth: '32px', minHeight: '16px' }} // Better touch target on mobile
                  />
                );
              })
          ) : (
            // Fallback while carousel is loading - show only 4 dots
            items.slice(0, 4).map((_, index) => {
              const isActive = currentSlide === index;
              
              return (
                <button
                  key={index}
                  className={`h-2 w-8 md:h-3 md:w-12 rounded-full transition-all duration-300 touch-manipulation ${
                    isActive ? "bg-black" : "bg-gray-300 hover:bg-gray-400"
                  }`}
                  onClick={() => {
                    if (carouselApi) {
                      carouselApi.scrollTo(index);
                    }
                  }}
                  aria-label={`Go to slide ${index + 1}`}
                  style={{ minWidth: '32px', minHeight: '16px' }}
                />
              );
            })
          )}
        </div>
      </div>
    </section>
  );
};

export { Gallery4 };
