# Deployment Guide - Vercel with Bun

This guide covers deploying your Next.js application to Vercel using <PERSON><PERSON> as the package manager.

## Prerequisites

- [Vercel CLI](https://vercel.com/cli) installed globally
- [Bun](https://bun.sh/) installed locally for development
- Git repository connected to Vercel

## Configuration Files

The following files have been configured for optimal Vercel deployment:

### `vercel.json`
- Specifies Bun as the package manager
- Configures build and dev commands
- Sets up Node.js runtime for serverless functions
- Optimizes for UK region (lhr1)

### `package.json`
- Engine specifications for Bun and Node.js
- Optimized build scripts

### `.vercelignore`
- Excludes unnecessary files from deployment
- Reduces deployment size and improves performance

### `next.config.ts`
- Production optimizations
- Image optimization settings
- Performance enhancements

## Local Testing

Before deploying, ensure your build works locally:

```bash
# Install dependencies
bun install

# Test the build
bun run build

# Test the production build locally
bun run start
```

## Deployment Steps

### 1. Initial Setup

```bash
# Login to Vercel
vercel login

# Link your project (first time only)
vercel link
```

### 2. Environment Variables

Set up environment variables in the Vercel dashboard:

1. Go to your project settings in Vercel
2. Navigate to Environment Variables
3. Add any required environment variables:
   - `NEXT_PUBLIC_SITE_URL` - Your production domain
   - `NEXT_TELEMETRY_DISABLED=1` - Disable Next.js telemetry

### 3. Deploy

```bash
# Deploy to preview
vercel

# Deploy to production
vercel --prod
```

## Automatic Deployments

Once connected to a Git repository, Vercel will automatically:

- Deploy preview builds for pull requests
- Deploy production builds when pushing to main branch
- Use Bun for all build processes

## Build Configuration

The build process uses:
- **Package Manager**: Bun
- **Build Command**: `bun run build`
- **Install Command**: `bun install`
- **Node.js Runtime**: 20.x
- **Output**: Standalone (optimized for serverless)

## Performance Optimizations

Implemented optimizations:
- Image optimization with AVIF/WebP formats
- Package import optimization for heavy libraries
- Compression enabled
- ETags disabled for better caching
- Standalone output for smaller deployments

## Monitoring

After deployment, monitor:
- Build times in Vercel dashboard
- Performance metrics
- Error logs in Vercel Functions tab

## Troubleshooting

### Common Issues

1. **Build Failures**: Check ESLint warnings don't become errors
2. **Missing Dependencies**: Ensure all dependencies are in `package.json`
3. **Environment Variables**: Verify all required env vars are set in Vercel

### Logs

View deployment logs:
```bash
vercel logs [deployment-url]
```

## Custom Domain

To set up a custom domain:

1. Go to your project settings in Vercel
2. Navigate to Domains
3. Add your custom domain
4. Configure DNS records as instructed

## Rollback

To rollback to a previous deployment:

```bash
vercel rollback [deployment-url]
```

## Support

For issues:
- Check Vercel documentation
- Review build logs in Vercel dashboard
- Ensure local build works before deploying 