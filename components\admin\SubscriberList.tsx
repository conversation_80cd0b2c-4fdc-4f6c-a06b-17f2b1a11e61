'use client'

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Mail, 
  Calendar, 
  MoreVertical, 
  UserCheck, 
  UserX,
  Trash2,
  Edit
} from 'lucide-react'

interface Subscriber {
  id: string
  email: string
  name?: string
  status: 'active' | 'unsubscribed' | 'bounced'
  created_at: string
  last_email_sent?: string
  tags?: string[]
}

interface SubscriberListProps {
  subscribers: Subscriber[]
}

export default function SubscriberList({ subscribers }: SubscriberListProps) {
  const [selectedSubscribers, setSelectedSubscribers] = useState<string[]>([])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'unsubscribed':
        return 'bg-red-100 text-red-800'
      case 'bounced':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-4 w-4 text-green-600" />
      case 'unsubscribed':
        return <UserX className="h-4 w-4 text-red-600" />
      case 'bounced':
        return <Mail className="h-4 w-4 text-yellow-600" />
      default:
        return <Mail className="h-4 w-4 text-gray-600" />
    }
  }

  const handleSelectSubscriber = (subscriberId: string) => {
    setSelectedSubscribers(prev => 
      prev.includes(subscriberId) 
        ? prev.filter(id => id !== subscriberId)
        : [...prev, subscriberId]
    )
  }

  const handleSelectAll = () => {
    if (selectedSubscribers.length === subscribers.length) {
      setSelectedSubscribers([])
    } else {
      setSelectedSubscribers(subscribers.map(s => s.id))
    }
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedSubscribers.length > 0 && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <p className="text-sm text-blue-700 gilroy-text-medium">
              {selectedSubscribers.length} subscriber{selectedSubscribers.length !== 1 ? 's' : ''} selected
            </p>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="outline" className="gilroy-text">
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </Button>
              <Button size="sm" variant="outline" className="gilroy-text">
                <UserX className="h-4 w-4 mr-2" />
                Unsubscribe
              </Button>
              <Button size="sm" variant="outline" className="gilroy-text text-red-600 hover:text-red-700">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Subscribers Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedSubscribers.length === subscribers.length && subscribers.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider gilroy-text-medium">
                Subscriber
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider gilroy-text-medium">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider gilroy-text-medium">
                Subscribed
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider gilroy-text-medium">
                Last Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider gilroy-text-medium">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {subscribers.length > 0 ? (
              subscribers.map((subscriber) => (
                <tr key={subscriber.id} className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedSubscribers.includes(subscriber.id)}
                      onChange={() => handleSelectSubscriber(subscriber.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Mail className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 gilroy-text-medium">
                          {subscriber.name || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500 gilroy-text">
                          {subscriber.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(subscriber.status)}
                      <Badge className={`${getStatusColor(subscriber.status)} gilroy-text-medium`}>
                        {subscriber.status.toUpperCase()}
                      </Badge>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 gilroy-text">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(subscriber.created_at).toLocaleDateString()}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 gilroy-text">
                    {subscriber.last_email_sent ? (
                      <div className="flex items-center space-x-1">
                        <Mail className="h-4 w-4" />
                        <span>{new Date(subscriber.last_email_sent).toLocaleDateString()}</span>
                      </div>
                    ) : (
                      <span className="text-gray-400">Never</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-700 gilroy-text"
                      >
                        <Mail className="h-4 w-4 mr-1" />
                        Email
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-gray-700 gilroy-text"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 gilroy-text"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <Mail className="h-16 w-16 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium mb-2">
                      No subscribers yet
                    </h3>
                    <p className="text-gray-500 gilroy-text mb-6">
                      Start building your email list by adding subscribers or importing from a CSV file
                    </p>
                    <Button className="gilroy-text-medium">
                      <UserCheck className="h-4 w-4 mr-2" />
                      Add Subscriber
                    </Button>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {subscribers.length > 0 && (
        <div className="flex items-center justify-between px-6 py-3 border-t border-gray-200">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button variant="outline" size="sm" className="gilroy-text">
              Previous
            </Button>
            <Button variant="outline" size="sm" className="gilroy-text">
              Next
            </Button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 gilroy-text">
                Showing <span className="font-medium">1</span> to <span className="font-medium">{subscribers.length}</span> of{' '}
                <span className="font-medium">{subscribers.length}</span> results
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="gilroy-text">
                Previous
              </Button>
              <Button variant="outline" size="sm" className="gilroy-text">
                Next
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 