import { createServerSupabaseClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { headers } from 'next/headers'
import AdminNav from '@/components/admin/AdminNav'
import AdminSidebar from '@/components/admin/AdminSidebar'

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get the current pathname
  const headersList = await headers()
  const pathname = headersList.get('x-pathname') || ''
  
  // Skip authentication for login and auth pages
  if (pathname === '/admin/login' || pathname.startsWith('/admin/auth/')) {
    return (
      <div className="min-h-screen bg-gray-50" style={{ fontFamily: 'Gilroy, sans-serif' }}>
        {children}
      </div>
    )
  }

  const supabase = await createServerSupabaseClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/admin/login')
  }

  const { data: adminUser, error } = await supabase
    .from('admin_users')
    .select('*')
    .eq('id', user.id)
    .single()

  if (error || !adminUser) {
    redirect('/admin/login')
  }

  return (
    <div className="min-h-screen bg-gray-50" style={{ fontFamily: 'Gilroy, sans-serif' }}>
      <AdminNav user={adminUser} />
      <div className="flex">
        <AdminSidebar />
        <main className="flex-1 overflow-hidden">
          {/* Container with proper width constraints matching website design */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="max-w-full">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
} 