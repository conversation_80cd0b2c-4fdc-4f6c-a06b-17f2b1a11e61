import { NextRequest, NextResponse } from 'next/server'

// Bot user agents that should receive prerendered content
const BOT_USER_AGENTS = [
  'googlebot',
  'bingbot',
  'slurp',
  'duckduckbot',
  'baiduspider',
  'yandexbot',
  'sogou',
  'facebookexternalhit',
  'twitterbot',
  'rogerbot',
  'linkedinbot',
  'embedly',
  'quora link preview',
  'showyoubot',
  'outbrain',
  'pinterest/0.',
  'developers.google.com/+/web/snippet',
  'slackbot',
  'vkshare',
  'w3c_validator',
  'redditbot',
  'applebot',
  'whatsapp',
  'flipboard',
  'tumblr',
  'bitlybot',
  'skypeuripreview',
  'nuzzel',
  'discordbot',
  'google page speed',
  'qwantify',
  'pinterestbot',
  'bitrix link preview',
  'xing-contenttabreceiver',
  'chrome-lighthouse',
  'telegrambot'
]

// File extensions that should not be prerendered
const STATIC_FILE_EXTENSIONS = [
  '.js', '.css', '.xml', '.less', '.png', '.jpg', '.jpeg', '.gif', '.pdf',
  '.doc', '.txt', '.ico', '.rss', '.zip', '.mp3', '.rar', '.exe', '.wmv',
  '.doc', '.avi', '.ppt', '.mpg', '.mpeg', '.tif', '.wav', '.mov', '.psd',
  '.ai', '.xls', '.mp4', '.m4a', '.swf', '.dat', '.dmg', '.iso', '.flv',
  '.m4v', '.torrent', '.woff', '.ttf', '.svg', '.webp', '.woff2', '.eot',
  '.otf', '.json', '.map'
]

// Prerender.io configuration
const PRERENDER_TOKEN = process.env.PRERENDER_TOKEN
const PRERENDER_SERVICE_URL = process.env.PRERENDER_SERVICE_URL || 'https://service.prerender.io'

function isBot(userAgent: string): boolean {
  if (!userAgent) return false
  
  const lowerUserAgent = userAgent.toLowerCase()
  return BOT_USER_AGENTS.some(bot => lowerUserAgent.includes(bot))
}

function isStaticFile(url: string): boolean {
  const pathname = new URL(url).pathname
  return STATIC_FILE_EXTENSIONS.some(ext => pathname.endsWith(ext))
}

function shouldPrerender(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || ''
  const url = request.url
  
  // Don't prerender if it's not a bot
  if (!isBot(userAgent)) {
    return false
  }
  
  // Don't prerender static files
  if (isStaticFile(url)) {
    return false
  }
  
  // Don't prerender admin routes
  if (request.nextUrl.pathname.startsWith('/admin')) {
    return false
  }
  
  // Don't prerender API routes
  if (request.nextUrl.pathname.startsWith('/api')) {
    return false
  }
  
  // Don't prerender Next.js internal routes
  if (request.nextUrl.pathname.startsWith('/_next')) {
    return false
  }
  
  return true
}

async function fetchPrerenderedContent(url: string): Promise<Response | null> {
  try {
    const prerenderUrl = `${PRERENDER_SERVICE_URL}/${encodeURIComponent(url)}`
    
    const headers: HeadersInit = {
      'User-Agent': 'Prerender (+https://github.com/prerender/prerender)',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    }
    
    // Add authorization header if token is provided
    if (PRERENDER_TOKEN) {
      headers['X-Prerender-Token'] = PRERENDER_TOKEN
    }
    
    const response = await fetch(prerenderUrl, {
      method: 'GET',
      headers,
      // Set a reasonable timeout
      signal: AbortSignal.timeout(10000) // 10 seconds
    })
    
    if (!response.ok) {
      console.error(`Prerender.io error: ${response.status} ${response.statusText}`)
      return null
    }
    
    return response
  } catch (error) {
    console.error('Prerender.io fetch error:', error)
    return null
  }
}

export async function prerenderMiddleware(request: NextRequest): Promise<NextResponse | null> {
  // Check if this request should be prerendered
  if (!shouldPrerender(request)) {
    return null
  }
  
  console.log(`Prerendering request for bot: ${request.headers.get('user-agent')}`)
  console.log(`URL: ${request.url}`)
  
  // Fetch prerendered content
  const prerenderResponse = await fetchPrerenderedContent(request.url)
  
  if (!prerenderResponse) {
    // If prerender fails, continue with normal flow
    console.log('Prerender failed, continuing with normal flow')
    return null
  }
  
  // Get the prerendered HTML
  const html = await prerenderResponse.text()
  
  // Return the prerendered content
  return new NextResponse(html, {
    status: prerenderResponse.status,
    headers: {
      'Content-Type': 'text/html; charset=utf-8',
      'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
      'X-Prerendered': 'true'
    }
  })
} 