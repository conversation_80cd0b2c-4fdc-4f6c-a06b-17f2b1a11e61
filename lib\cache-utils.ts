/**
 * Client-side cache utilities
 */

/**
 * Trigger cache invalidation via webhook (client-side)
 * @param table - Database table that changed
 * @param record - Optional record payload used for targeted invalidation
 */
export async function triggerCacheInvalidation(table: 'gallery_images' | 'before_after_pairs' | 'gallery_albums', record: Record<string, any> = {}) {
  try {
    const response = await fetch('/api/gallery/cache-revalidation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        table,
        event_type: 'manual_invalidation',
        record,
      }),
    })

    if (!response.ok) {
      throw new Error(`Cache invalidation failed with status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Failed to trigger cache invalidation:', error)
    throw error
  }
}

/**
 * Helper function to invalidate gallery cache after operations
 * @param category - Optional specific category
 */
export async function invalidateGalleryCache(category?: string) {
  try {
    await triggerCacheInvalidation('gallery_images', { category })
    console.log('Gallery cache invalidated successfully', { category })
    return true
  } catch (error) {
    console.error('Failed to invalidate gallery cache:', error)
    return false
  }
}

/**
 * Helper function to invalidate albums cache after operations
 */
export async function invalidateAlbumsCache() {
  try {
    await triggerCacheInvalidation('gallery_albums')
    console.log('Gallery albums cache invalidated successfully')
    return true
  } catch (error) {
    console.error('Failed to invalidate gallery albums cache:', error)
    return false
  }
}

/**
 * Trigger blog cache invalidation via webhook (client-side)
 * @param articleData - Optional article data with slug and tags
 */
export async function triggerBlogCacheInvalidation(articleData?: { slug?: string; tags?: string[] }) {
  try {
    const response = await fetch('/api/blog/cache-revalidation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'internal',
      },
      body: JSON.stringify({
        table: 'blog_articles',
        event_type: 'manual_invalidation',
        record: articleData,
      }),
    })

    if (!response.ok) {
      throw new Error(`Blog cache invalidation failed with status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Failed to trigger blog cache invalidation:', error)
    throw error
  }
}

/**
 * Helper function to invalidate blog cache after operations
 * @param articleData - Optional article data with slug and tags
 */
export async function invalidateBlogCache(articleData?: { slug?: string; tags?: string[] }) {
  try {
    await triggerBlogCacheInvalidation(articleData)
    console.log('Blog cache invalidated successfully', { articleData })
    return true
  } catch (error) {
    console.error('Failed to invalidate blog cache:', error)
    return false
  }
} 