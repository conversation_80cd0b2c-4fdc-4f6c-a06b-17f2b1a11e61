import { createServerSupabaseClient } from '@/lib/supabase'

// Force dynamic rendering for admin pages that use cookies
export const dynamic = 'force-dynamic'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Plus, 
  Download, 
  Mail, 
  Calendar,
  TrendingUp,
  UserCheck,
  UserX,
  Search
} from 'lucide-react'
import Link from 'next/link'
import SubscriberList from '@/components/admin/SubscriberList'
import SubscriberImportForm from '@/components/admin/SubscriberImportForm'

export default async function SubscribersPage() {
  const supabase = await createServerSupabaseClient()

  // Get subscribers
  const { data: subscribers } = await supabase
    .from('subscribers')
    .select('*')
    .order('created_at', { ascending: false })

  // Get subscriber stats
  const { count: totalSubscribers } = await supabase
    .from('subscribers')
    .select('*', { count: 'exact', head: true })

  const { count: activeSubscribers } = await supabase
    .from('subscribers')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'active')

  const { count: unsubscribedCount } = await supabase
    .from('subscribers')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'unsubscribed')

  // Get recent subscribers (last 7 days)
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)
  
  const { count: recentSubscribers } = await supabase
    .from('subscribers')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', weekAgo.toISOString())

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 gilroy-text-bold">Email Subscribers</h1>
          <p className="text-gray-600 mt-2 gilroy-text">
            Manage your email subscriber list and engagement
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" className="gilroy-text-medium">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <SubscriberImportForm />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Total Subscribers</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{totalSubscribers || 0}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Active Subscribers</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{activeSubscribers || 0}</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Unsubscribed</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{unsubscribedCount || 0}</p>
              </div>
              <UserX className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">New This Week</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{recentSubscribers || 0}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Subscribers List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="gilroy-text-bold">Subscriber List</CardTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="gilroy-text">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm" className="gilroy-text">
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <SubscriberList subscribers={subscribers || []} />
        </CardContent>
      </Card>
    </div>
  )
} 