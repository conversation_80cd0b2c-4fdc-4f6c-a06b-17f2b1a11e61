import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import GracefullyDegradingErrorBoundary from "@/components/gracefully-degrading-error-boundary";
import AnalyticsWrapper from "@/components/ui/analytics-wrapper"

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "JCC Customs and Commercial - The Campervan, Caravan and Motorhome Guys",
  description: "Expert repairs, restorations and customizations for campervans, caravans, motorhomes, vans and cars. Quality workmanship and customer-first service.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <GracefullyDegradingErrorBoundary>
          {children}
        </GracefullyDegradingErrorBoundary>
        <AnalyticsWrapper />
      </body>
    </html>
  );
}
