import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Prerender Test - JCC Customs and Commercial',
  description: 'Test page to verify Prerender.io integration for SEO optimization',
  openGraph: {
    title: 'Prerender Test - JCC Customs and Commercial',
    description: 'Test page to verify Prerender.io integration for SEO optimization',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Prerender Test - JCC Customs and Commercial',
    description: 'Test page to verify Prerender.io integration for SEO optimization',
  }
}

export default function PrerenderTestPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 gilroy-text-bold">
            Prerender.io Test Page
          </h1>
          
          <p className="text-xl text-muted-foreground mb-8 gilroy-text">
            This page is used to test the Prerender.io integration for SEO optimization.
          </p>

          <div className="bg-card border border-border rounded-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-4 gilroy-text-bold">
              SEO Test Content
            </h2>
            <p className="text-foreground gilroy-text leading-relaxed">
              This content should be visible to search engine crawlers when they visit this page.
              The Prerender.io service will render this page server-side and serve the static HTML
              to bots like Googlebot, ensuring proper SEO indexing.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-bold text-foreground mb-3 gilroy-text-bold">
                For Search Engines
              </h3>
              <p className="text-muted-foreground gilroy-text text-sm">
                Search engine bots will receive pre-rendered HTML content, ensuring
                proper indexing and SEO optimization.
              </p>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-bold text-foreground mb-3 gilroy-text-bold">
                For Users
              </h3>
              <p className="text-muted-foreground gilroy-text text-sm">
                Regular users will receive the full interactive Next.js application
                with all client-side functionality intact.
              </p>
            </div>
          </div>

          <div className="mt-8 p-4 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground gilroy-text">
              <strong>Test Instructions:</strong> To test this integration, visit this page with a bot user agent
              or use tools like curl with a Googlebot user agent to see the prerendered content.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 