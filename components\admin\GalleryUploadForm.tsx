'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Upload, X, Image, Star, Calendar, PoundSterling } from 'lucide-react'
import { invalidateGalleryCache } from '@/lib/cache-utils'
import {
  galleryUploadSchema,
  beforeAfterFormSchema,
  type GalleryUpload,
  type BeforeAfterUpload
} from '@/lib/validations'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

const VEHICLE_TYPES = [
  'Campervan',
  'Motorhome',
  'Caravan',
  'Van',
  'Car',
  'Other'
]

const SERVICE_TYPES = [
  'Full Respray',
  'Partial Respray',
  'Accident Repair',
  'Rust Repair',
  'Body Modifications',
  'Interior Conversion',
  'Electrical Work',
  'Night Heater Installation',
  'Raptor Coating',
  'Other'
]

const TRANSFORMATIONS_CATEGORY = 'before-after'

const ALL_CATEGORIES = [
  { value: 'accident-repairs', label: 'Accident Repairs' },
  { value: 'resprays', label: 'Full Resprays' },
  { value: 'rust-treatment', label: 'Rust Treatment' },
  { value: 'raptor-paint', label: 'Raptor Paint' },
  { value: 'restoration', label: 'Restoration' },
  { value: 'custom-work', label: 'Custom Work' },
  { value: 'workshop', label: 'Workshop' },
  { value: 'before-after', label: 'Before & After' },
]

interface GalleryUploadFormProps {
  onSuccess?: () => void
}

export default function GalleryUploadForm({ onSuccess }: GalleryUploadFormProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<string[]>([])
  const [error, setError] = useState<string | null>(null)
  const [isBeforeAfter, setIsBeforeAfter] = useState(false)

  // Regular upload state
  const [files, setFiles] = useState<File[]>([])
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [altText, setAltText] = useState('')
  const [category, setCategory] = useState(ALL_CATEGORIES[0].value)
  const [tags, setTags] = useState('')
  const [isFeatured, setIsFeatured] = useState(false)
  const [orderIndex, setOrderIndex] = useState<number>(0)
  const [isPublic, setIsPublic] = useState(true)
  const [albumId, setAlbumId] = useState<string>('none')
  const [albums, setAlbums] = useState<{ id: string; title: string }[]>([])

  // Before/after specific state
  const [beforeFile, setBeforeFile] = useState<File | null>(null)
  const [afterFile, setAfterFile] = useState<File | null>(null)
  const [vehicleYear, setVehicleYear] = useState('')
  const [vehicleMake, setVehicleMake] = useState('')
  const [vehicleModel, setVehicleModel] = useState('')
  const [vehicleType, setVehicleType] = useState<string>('')
  const [vehicleColor, setVehicleColor] = useState('')
  const [completionDate, setCompletionDate] = useState('')
  const [estimatedCost, setEstimatedCost] = useState('')
  const [workNotes, setWorkNotes] = useState('')

  const fileInputRef = useRef<HTMLInputElement>(null)
  const beforeInputRef = useRef<HTMLInputElement>(null)
  const afterInputRef = useRef<HTMLInputElement>(null)

  // Fetch albums on component mount
  useEffect(() => {
    const fetchAlbums = async () => {
      try {
        const response = await fetch('/api/gallery/albums?published_only=false')
        if (response.ok) {
          const data = await response.json()
          setAlbums(data.albums || [])
        }
      } catch (error) {
        console.error('Error fetching albums:', error)
      }
    }
    fetchAlbums()
  }, [])

  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles) return

    const validFiles = Array.from(selectedFiles).filter(file => {
      if (!file.type.startsWith('image/')) {
        setError('Please select only image files')
        return false
      }
      if (file.size > 50 * 1024 * 1024) {
        setError('Files must be smaller than 50MB')
        return false
      }
      return true
    })

    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles])
      setError(null)

      // Auto-fill title from first file if empty
      if (!title && validFiles.length > 0) {
        const filename = validFiles[0].name.replace(/\.[^/.]+$/, '')
        setTitle(filename.charAt(0).toUpperCase() + filename.slice(1))
      }
    }
  }, [title])

  const handleBeforeAfterSelect = useCallback((file: File, type: 'before' | 'after') => {
    if (!file.type.startsWith('image/')) {
      setError('Please select only image files')
      return
    }
    if (file.size > 50 * 1024 * 1024) {
      setError('Files must be smaller than 50MB')
      return
    }

    if (type === 'before') {
      setBeforeFile(file)
    } else {
      setAfterFile(file)
    }
    setError(null)

    // Auto-fill title if empty
    if (!title) {
      const filename = file.name.replace(/\.[^/.]+$/, '')
      setTitle(`${vehicleMake} ${vehicleModel}`.trim() || filename)
    }
  }, [title, vehicleMake, vehicleModel])

  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }, [])

  const removeBeforeAfterFile = useCallback((type: 'before' | 'after') => {
    if (type === 'before') {
      setBeforeFile(null)
    } else {
      setAfterFile(null)
    }
  }, [])

  const resetForm = useCallback(() => {
    setFiles([])
    setBeforeFile(null)
    setAfterFile(null)
    setTitle('')
    setDescription('')
    setAltText('')
    setCategory(ALL_CATEGORIES[0].value)
    setTags('')
    setIsFeatured(false)
    setOrderIndex(0)
    setIsPublic(true)
    setAlbumId('none')
    setVehicleYear('')
    setVehicleMake('')
    setVehicleModel('')
    setVehicleType('')
    setVehicleColor('')
    setCompletionDate('')
    setEstimatedCost('')
    setWorkNotes('')
    setUploadProgress([])
    setError(null)
  }, [])

  const uploadToSupabase = async (file: File, filename: string): Promise<string> => {
    const supabase = createClient()

    const { data, error } = await supabase.storage
      .from('gallery-images')
      .upload(filename, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) throw error

    // Get public URL from the uploaded file path
    const { data: { publicUrl } } = supabase.storage
      .from('gallery-images')
      .getPublicUrl(data.path)

    return publicUrl
  }

  const handleRegularUpload = async (formData: GalleryUpload) => {
    const supabase = createClient()

    for (const file of files) {
      const filename = `${Date.now()}-${file.name}`
      setUploadProgress(prev => [...prev, `Uploading ${file.name}...`])

      const imageUrl = await uploadToSupabase(file, filename)

      setUploadProgress(prev => [...prev, `Saving ${file.name} to database...`])

      const { error: dbError } = await supabase
        .from('gallery_images')
        .insert({
          title: formData.title,
          description: formData.description,
          alt_text: formData.altText,
          category: formData.category,
          tags: formData.tags ? formData.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
          image_url: imageUrl,
          file_size: file.size,
          file_type: file.type,
          is_public: formData.isPublic,
          is_featured: formData.isFeatured,
          order_index: formData.orderIndex,
          album_id: (formData.albumId && formData.albumId !== 'none') ? formData.albumId : null
        })

      if (dbError) throw dbError
      setUploadProgress(prev => [...prev, `✓ ${file.name} uploaded successfully`])
    }
  }

  const handleBeforeAfterUpload = async (formData: BeforeAfterUpload) => {
    if (!beforeFile || !afterFile) {
      throw new Error('Both before and after images are required')
    }

    const supabase = createClient()

    // Upload before image
    setUploadProgress(prev => [...prev, 'Uploading before image...'])
    const beforeFilename = `before-${Date.now()}-${beforeFile.name}`
    const beforeImageUrl = await uploadToSupabase(beforeFile, beforeFilename)

    // Upload after image
    setUploadProgress(prev => [...prev, 'Uploading after image...'])
    const afterFilename = `after-${Date.now()}-${afterFile.name}`
    const afterImageUrl = await uploadToSupabase(afterFile, afterFilename)

    // Create gallery entries
    setUploadProgress(prev => [...prev, 'Creating gallery entries...'])

    const baseTitle = formData.title || `${formData.vehicleMake} ${formData.vehicleModel}`.trim()
    const baseDescription = formData.description || `Work completed on ${formData.vehicleYear} ${formData.vehicleMake} ${formData.vehicleModel}`

    // Insert before image
    const { data: beforeImage, error: beforeError } = await supabase
      .from('gallery_images')
      .insert({
        title: `${baseTitle} - Before`,
        description: baseDescription,
        alt_text: formData.altText || `Before: ${baseTitle}`,
        category: formData.category,
        tags: formData.tags ? formData.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        image_url: beforeImageUrl,
        file_size: beforeFile.size,
        file_type: beforeFile.type,
        is_public: formData.isPublic,
        is_featured: formData.isFeatured,
        order_index: formData.orderIndex,
        album_id: (formData.albumId && formData.albumId !== 'none') ? formData.albumId : null
      })
      .select()
      .single()

    if (beforeError) throw beforeError

    // Insert after image
    const { data: afterImage, error: afterError } = await supabase
      .from('gallery_images')
      .insert({
        title: `${baseTitle} - After`,
        description: baseDescription,
        alt_text: formData.altText || `After: ${baseTitle}`,
        category: formData.category,
        tags: formData.tags ? formData.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        image_url: afterImageUrl,
        file_size: afterFile.size,
        file_type: afterFile.type,
        is_public: formData.isPublic,
        is_featured: formData.isFeatured,
        order_index: formData.orderIndex,
        album_id: (formData.albumId && formData.albumId !== 'none') ? formData.albumId : null
      })
      .select()
      .single()

    if (afterError) throw afterError

    // Create before/after pair
    setUploadProgress(prev => [...prev, 'Creating before/after pair...'])

    const { error: pairError } = await supabase
      .from('before_after_pairs')
      .insert({
        title: baseTitle,
        description: baseDescription,
        before_image_id: beforeImage.id,
        after_image_id: afterImage.id,
        category: formData.category,
        tags: formData.tags ? formData.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        order_index: formData.orderIndex,
        is_featured: formData.isFeatured,
        is_public: formData.isPublic,
        vehicle_details: {
          year: formData.vehicleYear ? parseInt(formData.vehicleYear) : null,
          make: formData.vehicleMake,
          model: formData.vehicleModel,
          type: formData.vehicleType,
          color: formData.vehicleColor
        },
        work_details: {
          service_type: formData.category,
          completion_date: formData.completionDate || null,
          estimated_cost: formData.estimatedCost ? parseFloat(formData.estimatedCost) : null,
          work_notes: formData.workNotes
        }
      })

    if (pairError) throw pairError
    setUploadProgress(prev => [...prev, '✓ Before/after pair created successfully'])
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsUploading(true)
    setUploadProgress([])
    setError(null)

    try {
      if (isBeforeAfter) {
        // Validate before/after form
        const formData = beforeAfterFormSchema.parse({
          title,
          description,
          altText,
          tags,
          isFeatured,
          orderIndex,
          isPublic,
          category: TRANSFORMATIONS_CATEGORY,
          albumId: albumId || undefined,
          vehicleYear,
          vehicleMake,
          vehicleModel,
          vehicleType,
          vehicleColor,
          completionDate,
          estimatedCost,
          workNotes
        })

        await handleBeforeAfterUpload(formData)
      } else {
        // Validate regular form
        const formData = galleryUploadSchema.parse({
          title,
          description,
          altText,
          category,
          tags,
          isFeatured,
          orderIndex,
          isPublic,
          albumId: albumId || undefined,
          vehicleYear,
          vehicleMake,
          vehicleModel,
          vehicleType,
          vehicleColor,
          completionDate,
          estimatedCost,
          workNotes
        })

        if (files.length === 0) {
          throw new Error('Please select at least one file')
        }

        await handleRegularUpload(formData)
      }

      setUploadProgress(prev => [...prev, 'Invalidating cache...'])
      await invalidateGalleryCache()

      setUploadProgress(prev => [...prev, '✓ Upload completed successfully!'])
      resetForm()
      onSuccess?.()
    } catch (err) {
      console.error('Upload error:', err)
      setError(err instanceof Error ? err.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = useCallback((e: React.DragEvent, type?: 'before' | 'after') => {
    e.preventDefault()
    const droppedFiles = e.dataTransfer.files

    if (isBeforeAfter && type) {
      if (droppedFiles.length > 0) {
        handleBeforeAfterSelect(droppedFiles[0], type)
      }
    } else if (!isBeforeAfter) {
      handleFileSelect(droppedFiles)
    }
  }, [isBeforeAfter, handleFileSelect, handleBeforeAfterSelect])

  const currentFiles = isBeforeAfter ?
    [beforeFile, afterFile].filter(Boolean).length :
    files.length

  return (
    <Card className="h-fit">
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-lg font-semibold">Upload Images</CardTitle>
            <CardDescription className="text-sm mt-1">
              {isBeforeAfter ?
                'Upload before/after transformation images' :
                'Upload images to the gallery'
              }
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2 flex-shrink-0">
            <Label htmlFor="before-after-toggle" className="text-sm whitespace-nowrap">Before/After</Label>
            <Switch
              id="before-after-toggle"
              checked={isBeforeAfter}
              onCheckedChange={(checked) => {
                setIsBeforeAfter(checked)
                if (checked) {
                  setCategory('before-after')
                } else {
                  setCategory(ALL_CATEGORIES[0].value)
                }
              }}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* File Upload Section */}
          {isBeforeAfter ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              {/* Before Image */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Before Image</Label>
                <div
                  className="border-2 border-dashed border-red-300 rounded-lg p-4 text-center hover:border-red-400 transition-colors cursor-pointer min-h-[120px] flex flex-col justify-center"
                  onDrop={(e) => handleDrop(e, 'before')}
                  onDragOver={(e) => e.preventDefault()}
                  onClick={() => beforeInputRef.current?.click()}
                >
                  <input
                    ref={beforeInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handleBeforeAfterSelect(e.target.files[0], 'before')}
                    className="hidden"
                  />
                  {beforeFile ? (
                    <div className="space-y-2">
                      <div className="relative">
                        <img
                          src={URL.createObjectURL(beforeFile)}
                          alt="Before preview"
                          className="w-full h-24 object-cover rounded"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeBeforeAfterFile('before')
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <Badge variant="destructive" className="text-xs truncate max-w-full">{beforeFile.name}</Badge>
                    </div>
                  ) : (
                    <>
                      <Image className="mx-auto h-6 w-6 text-red-400 mb-2" />
                      <p className="text-xs text-muted-foreground leading-tight">Drop before image here or click</p>
                    </>
                  )}
                </div>
              </div>

              {/* After Image */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">After Image</Label>
                <div
                  className="border-2 border-dashed border-green-300 rounded-lg p-4 text-center hover:border-green-400 transition-colors cursor-pointer min-h-[120px] flex flex-col justify-center"
                  onDrop={(e) => handleDrop(e, 'after')}
                  onDragOver={(e) => e.preventDefault()}
                  onClick={() => afterInputRef.current?.click()}
                >
                  <input
                    ref={afterInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handleBeforeAfterSelect(e.target.files[0], 'after')}
                    className="hidden"
                  />
                  {afterFile ? (
                    <div className="space-y-2">
                      <div className="relative">
                        <img
                          src={URL.createObjectURL(afterFile)}
                          alt="After preview"
                          className="w-full h-24 object-cover rounded"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeBeforeAfterFile('after')
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <Badge variant="secondary" className="text-xs truncate max-w-full">{afterFile.name}</Badge>
                    </div>
                  ) : (
                    <>
                      <Image className="mx-auto h-6 w-6 text-green-400 mb-2" />
                      <p className="text-xs text-muted-foreground leading-tight">Drop after image here or click</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleFileSelect(e.target.files)}
                  className="hidden"
                />
                <Upload className="mx-auto h-8 w-8 text-gray-400 mb-3" />
                <p className="text-sm text-gray-600 mb-1">
                  Drop images here or click to select
                </p>
                <p className="text-xs text-gray-500">
                  JPEG, PNG, WebP, GIF (max 50MB each)
                </p>
              </div>

              {files.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                  {files.map((file, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-20 object-cover rounded"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 h-6 w-6 p-0"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <Badge variant="secondary" className="absolute bottom-1 left-1 text-xs truncate max-w-[calc(100%-8px)]">
                        {file.name.length > 12 ? `${file.name.substring(0, 12)}...` : file.name}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter image title"
                className="text-sm"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="alt-text" className="text-sm font-medium">Alt Text</Label>
              <Input
                id="alt-text"
                value={altText}
                onChange={(e) => setAltText(e.target.value)}
                placeholder="Describe for accessibility"
                className="text-sm"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter image description"
              rows={2}
              className="text-sm resize-none"
            />
          </div>

          {/* Category, Tags, and Album */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label htmlFor="category" className="text-sm font-medium">Category</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {ALL_CATEGORIES.map((cat: { value: string; label: string }) => (
                    <SelectItem key={cat.value} value={cat.value} className="text-sm">{cat.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="album" className="text-sm font-medium">Album (Optional)</Label>
              <Select value={albumId} onValueChange={setAlbumId}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="No album" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none" className="text-sm">No album</SelectItem>
                  {albums.map((album) => (
                    <SelectItem key={album.id} value={album.id} className="text-sm">{album.title}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags" className="text-sm font-medium">Tags</Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="Comma separated tags"
                className="text-sm"
              />
            </div>
          </div>

          {/* Vehicle Details - For both modes */}
          <div className="space-y-3 p-3 border rounded-lg bg-blue-50">
            <h3 className="text-sm font-semibold text-blue-900">Vehicle Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <div className="space-y-2">
                <Label htmlFor="vehicle-year" className="text-sm font-medium">Year</Label>
                <Input
                  id="vehicle-year"
                  value={vehicleYear}
                  onChange={(e) => setVehicleYear(e.target.value)}
                  placeholder="2020"
                  type="number"
                  className="text-sm"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="vehicle-make" className="text-sm font-medium">Make</Label>
                <Input
                  id="vehicle-make"
                  value={vehicleMake}
                  onChange={(e) => setVehicleMake(e.target.value)}
                  placeholder="Ford, VW"
                  className="text-sm"
                  required={isBeforeAfter}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="vehicle-model" className="text-sm font-medium">Model</Label>
                <Input
                  id="vehicle-model"
                  value={vehicleModel}
                  onChange={(e) => setVehicleModel(e.target.value)}
                  placeholder="Transit, Crafter"
                  className="text-sm"
                  required={isBeforeAfter}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="vehicle-type" className="text-sm font-medium">Vehicle Type</Label>
                <Select value={vehicleType} onValueChange={setVehicleType}>
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {VEHICLE_TYPES.map(type => (
                      <SelectItem key={type} value={type} className="text-sm">{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="vehicle-color" className="text-sm font-medium">Colour</Label>
                <Input
                  id="vehicle-color"
                  value={vehicleColor}
                  onChange={(e) => setVehicleColor(e.target.value)}
                  placeholder="White, Blue"
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {/* Work Details - For both modes */}
          <div className="space-y-3 p-3 border rounded-lg bg-green-50">
            <h3 className="text-sm font-semibold text-green-900">Work Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="completion-date" className="text-sm font-medium flex items-center">
                  <Calendar className="inline h-3 w-3 mr-1" />
                  Completion Date
                </Label>
                <Input
                  id="completion-date"
                  type="date"
                  value={completionDate}
                  onChange={(e) => setCompletionDate(e.target.value)}
                  className="text-sm"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="estimated-cost" className="text-sm font-medium flex items-center">
                  <PoundSterling className="inline h-3 w-3 mr-1" />
                  Estimated Cost
                </Label>
                <Input
                  id="estimated-cost"
                  type="number"
                  step="0.01"
                  value={estimatedCost}
                  onChange={(e) => setEstimatedCost(e.target.value)}
                  placeholder="0.00"
                  className="text-sm"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="work-notes" className="text-sm font-medium">Work Notes</Label>
              <Textarea
                id="work-notes"
                value={workNotes}
                onChange={(e) => setWorkNotes(e.target.value)}
                placeholder="Additional details about the work performed..."
                rows={2}
                className="text-sm resize-none"
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-3 p-3 border rounded-lg bg-blue-50">
            <h3 className="text-sm font-semibold text-blue-900 flex items-center gap-2">
              <Star className={`h-4 w-4 ${isFeatured ? 'text-yellow-500' : 'text-gray-400'}`} />
              <span className="whitespace-nowrap">Display Settings</span>
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <div className="flex items-center gap-2">
                <Switch
                  id="is-public"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
                <Label htmlFor="is-public" className="text-sm whitespace-nowrap">Public</Label>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  id="is-featured"
                  checked={isFeatured}
                  onCheckedChange={setIsFeatured}
                />
                <Label htmlFor="is-featured" className="text-sm whitespace-nowrap">Featured</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="order-index" className="text-sm font-medium whitespace-nowrap">Display Order</Label>
                <Input
                  id="order-index"
                  type="number"
                  value={orderIndex}
                  onChange={(e) => setOrderIndex(parseInt(e.target.value) || 0)}
                  placeholder="0"
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {uploadProgress.length > 0 && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="space-y-1">
                {uploadProgress.map((message, index) => (
                  <p key={index} className="text-xs text-blue-600">{message}</p>
                ))}
              </div>
            </div>
          )}

          <Button
            type="submit"
            disabled={isUploading || (!isBeforeAfter && currentFiles === 0) || (isBeforeAfter && (!beforeFile || !afterFile))}
            className="w-full text-sm"
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isBeforeAfter ? 'Uploading Before/After...' : 'Uploading...'}
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                {isBeforeAfter ? 'Upload Before/After Images' : `Upload ${currentFiles} Image${currentFiles !== 1 ? 's' : ''}`}
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
