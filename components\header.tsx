'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Menu, X, Phone, Mail, Instagram, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface HeaderProps {
  onContactClick?: () => void;
  forceVisible?: boolean;
}

export default function Header({ onContactClick, forceVisible = false }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const servicesRef = useRef<HTMLDivElement>(null);
  const servicesTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Services dropdown items
  const serviceItems = [
    {
      href: '/repairs',
      title: 'Accident Repairs',
      description: 'Collision damage & bodywork repairs',
      image: '/images/sticky-scroll-component-images/1.png'
    },
    {
      href: '/resprays',
      title: 'Full Resprays',
      description: 'Complete paint restoration services',
      image: '/images/sticky-scroll-component-images/2.jpeg'
    },
    {
      href: '/rust',
      title: 'Rust Treatment',
      description: 'Rust removal & prevention services',
      image: '/images/sticky-scroll-component-images/3.jpeg'
    },
    {
      href: '/raptor',
      title: 'Raptor Paint',
      description: 'Protective coating application',
      image: '/images/sticky-scroll-component-images/4.jpeg'
    },
    {
      href: '/night-heaters',
      title: 'Night Heaters',
      description: 'Installation & repair services',
      image: '/images/night-heater.png'
    },
    {
      href: '/contact',
      title: 'Welding & Fabrication',
      description: 'Structural repairs & custom work',
      image: '/images/sticky-scroll-component-images/5.jpeg'
    }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Set scrolled state for styling
      setIsScrolled(currentScrollY > 100);

      // Close mobile menu when scrolling
      if (isMenuOpen) {
        setIsMenuOpen(false);
      }
      
      // Only close services dropdown when scrolling down significantly (not on small scroll movements)
      if (isServicesOpen && currentScrollY > lastScrollY + 50) {
        setIsServicesOpen(false);
      }

      // Header visibility logic
      if (currentScrollY < 100) {
        // At top of page - header visible and transparent
        setIsHeaderVisible(true);
      } else if (currentScrollY > lastScrollY) {
        // Scrolling down - hide header
        setIsHeaderVisible(false);
        // Clear any pending dropdown timeout when hiding header
        if (servicesTimeoutRef.current) {
          clearTimeout(servicesTimeoutRef.current);
        }
      } else {
        // Scrolling up - show header
        setIsHeaderVisible(true);
        // Clear any pending dropdown timeout when showing header to ensure clean state
        if (servicesTimeoutRef.current) {
          clearTimeout(servicesTimeoutRef.current);
        }
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY, isMenuOpen, isServicesOpen]);

  // Close dropdown when clicking outside (only needed for mobile touch devices)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (servicesRef.current && !servicesRef.current.contains(event.target as Node)) {
        setIsServicesOpen(false);
      }
    };

    // Only add click outside handler on mobile/touch devices where hover doesn't work
    const isMobile = window.innerWidth < 768;
    if (isMobile) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }
  }, [isMenuOpen]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (servicesTimeoutRef.current) {
        clearTimeout(servicesTimeoutRef.current);
      }
    };
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
    setIsServicesOpen(false);
  };

  const handleContactClick = () => {
    if (onContactClick) {
      onContactClick();
    }
    // Close mobile menu when navigating
    setIsMenuOpen(false);
    setIsServicesOpen(false);
  };

  // Delayed hover handlers for smooth dropdown experience
  const handleServicesMouseEnter = () => {
    if (servicesTimeoutRef.current) {
      clearTimeout(servicesTimeoutRef.current);
    }
    setIsServicesOpen(true);
  };

  const handleServicesMouseLeave = () => {
    servicesTimeoutRef.current = setTimeout(() => {
      setIsServicesOpen(false);
    }, 150); // 150ms delay before closing
  };

  return (
    <>
      {/* Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 transform ${
        isHeaderVisible ? 'translate-y-0' : '-translate-y-full'
      } ${
        isScrolled || forceVisible ? 'bg-white/95 backdrop-blur-sm shadow-lg' : 'bg-transparent'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 md:h-20 lg:h-24">
            {/* Mobile Menu Button (Left) */}
            <div className="md:hidden">
              <button
                className={`p-3 rounded-md touch-manipulation transition-colors ${isScrolled || forceVisible ? 'text-black hover:bg-gray-100' : 'text-white hover:bg-white/20'}`}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                style={{ minWidth: '48px', minHeight: '48px' }} // Better touch target
              >
                <span className="sr-only">Open menu</span>
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>

            {/* Desktop Layout */}
            <div className="hidden md:flex items-center w-full">
              {/* Left Navigation */}
              <nav className="flex flex-1 justify-end items-center space-x-16 lg:space-x-24 xl:space-x-32">
                {/* Services Dropdown */}
                <div 
                  className="relative" 
                  ref={servicesRef}
                  onMouseEnter={handleServicesMouseEnter}
                  onMouseLeave={handleServicesMouseLeave}
                >
                  <button
                    onClick={() => {
                      // For touch devices, toggle dropdown on click
                      if (window.innerWidth < 768) {
                        setIsServicesOpen(!isServicesOpen);
                      }
                    }}
                    className={`flex items-center space-x-1 text-sm lg:text-base font-medium uppercase tracking-wider transition-all duration-300 py-2 px-1 relative touch-manipulation ${isScrolled || forceVisible ? 'text-black' : 'text-white'} after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-current after:transition-all after:duration-300 hover:after:w-full`}
                  >
                    <span>Services</span>
                    <motion.div
                      animate={{ rotate: isServicesOpen ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown size={16} />
                    </motion.div>
                  </button>

                  {/* Services Dropdown Menu */}
                  <AnimatePresence>
                    {isServicesOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-96 bg-black rounded-xl shadow-2xl border border-gray-800 overflow-hidden z-50"
                        style={{
                          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
                        }}
                      >
                        {/* Header */}
                        <div className="bg-white px-6 py-4 border-b border-gray-200">
                          <h3 className="text-sm font-black uppercase tracking-wider text-black gilroy-text-bold">
                            Our Services
                          </h3>
                        </div>
                        
                        {/* Services List */}
                        <div className="p-0">
                          {serviceItems.map((item, index) => (
                            <motion.div
                              key={item.href}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.04 }}
                            >
                              <Link
                                href={item.href}
                                onClick={() => {
                                  setIsServicesOpen(false);
                                  if (item.href.startsWith('#')) {
                                    scrollToSection(item.href.slice(1));
                                  }
                                }}
                                className="group relative flex items-center justify-between p-0 transition-all duration-300 touch-manipulation overflow-hidden"
                              >
                                {/* Background Image */}
                                <div 
                                  className="absolute inset-0 bg-cover bg-center transition-all duration-300 group-hover:scale-105"
                                  style={{
                                    backgroundImage: `url('${item.image}')`,
                                    filter: 'brightness(0.7) contrast(1.1)',
                                  }}
                                />
                                
                                {/* Gradient Overlay for better text contrast */}
                                <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/20 group-hover:from-black/80 group-hover:via-black/50 group-hover:to-black/30 transition-all duration-300" />
                                
                                {/* Content */}
                                <div className="relative z-10 flex-1 p-4">
                                  <div className="flex items-center space-x-3">
                                    {/* Service Icon/Number */}
                                    <div className="flex-shrink-0 w-8 h-8 bg-white/10 backdrop-blur-sm group-hover:bg-white/20 text-white flex items-center justify-center text-xs font-black transition-all duration-300 rounded border border-white/20 gilroy-text-bold">
                                      {String(index + 1).padStart(2, '0')}
                                    </div>
                                    
                                    {/* Text Content */}
                                    <div className="flex-1">
                                      <h4 className="text-sm font-black uppercase tracking-tight text-white transition-all duration-300 group-hover:text-white drop-shadow-lg gilroy-text-bold">
                                        {item.title}
                                      </h4>
                                      <p className="text-xs text-gray-200 group-hover:text-gray-100 mt-1 transition-all duration-300 drop-shadow-md gilroy-text">
                                        {item.description}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                
                                {/* Arrow */}
                                <div className="relative z-10 flex-shrink-0 ml-4 p-4">
                                  <motion.div
                                    className="w-6 h-6 flex items-center justify-center"
                                    whileHover={{ x: 2 }}
                                    transition={{ duration: 0.2 }}
                                  >
                                    <svg 
                                      width="12" 
                                      height="12" 
                                      viewBox="0 0 12 12" 
                                      fill="none" 
                                      className="text-white/80 group-hover:text-white transition-colors duration-300 drop-shadow-lg"
                                    >
                                      <path 
                                        d="M4.5 3L7.5 6L4.5 9" 
                                        stroke="currentColor" 
                                        strokeWidth="1.5" 
                                        strokeLinecap="round" 
                                        strokeLinejoin="round"
                                      />
                                    </svg>
                                  </motion.div>
                                </div>
                                
                                {/* Subtle highlight border on hover */}
                                <div className="absolute inset-0 border-2 border-white/0 group-hover:border-white/20 transition-all duration-300" />
                              </Link>
                            </motion.div>
                          ))}
                        </div>
                        
                        {/* Footer CTA */}
                        <div className="bg-white px-6 py-4 border-t border-gray-200">
                          <div className="text-center">
                            <p className="text-xs text-gray-600 mb-2 gilroy-text">
                              Need something else?
                            </p>
                            <Link
                              href="/contact"
                              onClick={() => {
                                setIsServicesOpen(false);
                              }}
                              className="text-xs font-bold uppercase tracking-wider text-black hover:text-gray-700 transition-colors duration-200 gilroy-text-bold"
                            >
                              Get Custom Quote →
                            </Link>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <Link href="/contact" className={`text-sm lg:text-base font-medium uppercase tracking-wider transition-all duration-300 py-2 px-1 relative touch-manipulation ${isScrolled || forceVisible ? 'text-black' : 'text-white'} after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-current after:transition-all after:duration-300 hover:after:w-full`}>Contact</Link>
              </nav>

              {/* Center Logo */}
              <Link href="/" className="flex-shrink-0 mx-24 lg:mx-36 xl:mx-48 touch-manipulation">
                <Image
                  src="/images/logo-main.png"
                  alt="JCC Logo"
                  width={80}
                  height={80}
                  className="h-10 w-auto md:h-12 lg:h-14"
                />
              </Link>

              {/* Right Navigation */}
              <nav className="flex flex-1 justify-start items-center space-x-16 lg:space-x-24 xl:space-x-32">
                <Link href="/gallery" className={`text-sm lg:text-base font-medium uppercase tracking-wider transition-all duration-300 py-2 px-1 relative touch-manipulation ${isScrolled || forceVisible ? 'text-black' : 'text-white'} after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-current after:transition-all after:duration-300 hover:after:w-full`}>Gallery</Link>
                <Link href="/blog" className={`text-sm lg:text-base font-medium uppercase tracking-wider transition-all duration-300 py-2 px-1 relative touch-manipulation ${isScrolled || forceVisible ? 'text-black' : 'text-white'} after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-current after:transition-all after:duration-300 hover:after:w-full`}>Blog</Link>
              </nav>
            </div>

            {/* Mobile Logo - Centered */}
            <div className="flex-1 flex justify-center md:hidden">
              <Link href="/" className="flex-shrink-0 touch-manipulation">
                <Image
                  src="/images/logo-main.png"
                  alt="JCC Logo"
                  width={80}
                  height={80}
                  className="h-10 w-auto"
                />
              </Link>
            </div>

            {/* Right-side spacer for mobile to balance the menu button */}
            <div className="md:hidden w-12 h-12"></div>
          </div>
        </div>

        {/* Mobile Menu Overlay */}
        <AnimatePresence>
          {isMenuOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="fixed inset-0 z-[9999] md:hidden"
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.6)',
                  backdropFilter: 'blur(4px)',
                  WebkitBackdropFilter: 'blur(4px)'
                }}
                onClick={() => setIsMenuOpen(false)}
              />
              
              {/* Slide-in Menu */}
              <motion.div
                initial={{ x: "100%" }}
                animate={{ x: 0 }}
                exit={{ x: "100%" }}
                transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="fixed top-0 right-0 w-80 max-w-[85vw] z-[10000] md:hidden"
                style={{
                  backgroundColor: '#ffffff',
                  borderLeft: '1px solid #e5e7eb',
                  boxShadow: '-10px 0 25px -3px rgba(0, 0, 0, 0.1), -4px 0 10px -2px rgba(0, 0, 0, 0.05)',
                  height: '100dvh' // Use dynamic viewport height for mobile
                }}
              >
                {/* Menu Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-white">
                  <div className="flex items-center space-x-3">
                    <Image
                      src="/images/logo-main.png"
                      alt="JCC Logo"
                      width={40}
                      height={40}
                      className="h-10 w-auto"
                    />
                    <div>
                      <h3 className="hipster-heading text-lg font-black uppercase tracking-tight text-black">JCC</h3>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">Menu</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsMenuOpen(false)}
                    className="p-2 rounded-full hover:bg-gray-100 transition-colors touch-manipulation"
                    style={{ minWidth: '44px', minHeight: '44px' }}
                  >
                    <X size={24} className="text-gray-600" />
                  </button>
                </div>

                {/* Menu Content */}
                <div className="flex flex-col h-full bg-white">
                  {/* Navigation Links */}
                  <nav className="flex-1 py-6 bg-white">
                    <div className="space-y-2 px-6">
                      {/* Services Section */}
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 }}
                        className="mb-4"
                      >
                        <div className="py-2 px-4">
                          <span className="hipster-heading text-lg font-black uppercase tracking-tight text-black">
                            Services
                          </span>
                          <p className="text-sm text-gray-500 mt-1">What we&apos;re great at</p>
                        </div>
                        <div className="ml-4 space-y-1">
                          {serviceItems.map((item, index) => (
                            <motion.div
                              key={item.href}
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: 0.15 + index * 0.05 }}
                            >
                              <Link
                                href={item.href}
                                onClick={() => {
                                  setIsMenuOpen(false);
                                  if (item.href.startsWith('#')) {
                                    scrollToSection(item.href.slice(1));
                                  }
                                }}
                                className="group flex items-center justify-between w-full py-3 px-3 rounded-lg hover:bg-gray-50 transition-all duration-200 touch-manipulation"
                              >
                                <div>
                                  <span className="hipster-heading text-base font-bold uppercase tracking-tight text-black group-hover:text-gray-700">
                                    {item.title}
                                  </span>
                                  <p className="text-xs text-gray-500 mt-1">{item.description}</p>
                                </div>
                                <div className="w-5 h-5 rounded-full bg-black/10 flex items-center justify-center group-hover:bg-black group-hover:text-white transition-all duration-200">
                                  <span className="text-xs font-bold">→</span>
                                </div>
                              </Link>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>

                      {/* Other Navigation Items */}
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <Link
                          href="/contact"
                          onClick={() => {
                            setIsMenuOpen(false);
                          }}
                          className="group flex items-center justify-between w-full py-4 px-4 rounded-lg hover:bg-gray-50 transition-all duration-200 touch-manipulation"
                        >
                          <div>
                            <span className="hipster-heading text-xl font-black uppercase tracking-tight text-black group-hover:text-gray-700">
                              Contact
                            </span>
                            <p className="text-sm text-gray-500 mt-1">Get in touch with us</p>
                          </div>
                          <div className="w-6 h-6 rounded-full bg-black/10 flex items-center justify-center group-hover:bg-black group-hover:text-white transition-all duration-200">
                            <span className="text-xs font-bold">→</span>
                          </div>
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.55 }}
                      >
                        <Link
                          href="/gallery"
                          onClick={() => setIsMenuOpen(false)}
                          className="group flex items-center justify-between w-full py-4 px-4 rounded-lg hover:bg-gray-50 transition-all duration-200 touch-manipulation"
                        >
                          <div>
                            <span className="hipster-heading text-xl font-black uppercase tracking-tight text-black group-hover:text-gray-700">
                              Gallery
                            </span>
                            <p className="text-sm text-gray-500 mt-1">See our best work</p>
                          </div>
                          <div className="w-6 h-6 rounded-full bg-black/10 flex items-center justify-center group-hover:bg-black group-hover:text-white transition-all duration-200">
                            <span className="text-xs font-bold">→</span>
                          </div>
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 }}
                      >
                        <Link
                          href="/blog"
                          onClick={() => setIsMenuOpen(false)}
                          className="group flex items-center justify-between w-full py-4 px-4 rounded-lg hover:bg-gray-50 transition-all duration-200 touch-manipulation"
                        >
                          <div>
                            <span className="hipster-heading text-xl font-black uppercase tracking-tight text-black group-hover:text-gray-700">
                              Blog
                            </span>
                            <p className="text-sm text-gray-500 mt-1">Tips, updates & insights</p>
                          </div>
                          <div className="w-6 h-6 rounded-full bg-black/10 flex items-center justify-center group-hover:bg-black group-hover:text-white transition-all duration-200">
                            <span className="text-xs font-bold">→</span>
                          </div>
                        </Link>
                      </motion.div>
                    </div>
                  </nav>

                  {/* Contact Section */}
                  <div className="border-t border-gray-200 p-6 bg-white">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.65 }}
                    >
                      <Link
                        href="/contact"
                        onClick={() => {
                          setIsMenuOpen(false);
                        }}
                        className="w-full bg-black text-white py-4 px-6 rounded-lg font-bold uppercase tracking-wider hover:bg-gray-800 transition-all duration-200 touch-manipulation active:scale-95 inline-block text-center"
                        style={{ minHeight: '56px' }}
                      >
                        Get A Quote
                      </Link>
                      
                      {/* Quick Contact Info */}
                      <div className="mt-4 space-y-2">
                        <a 
                          href="tel:01603336439"
                          className="flex items-center space-x-3 text-sm text-gray-600 hover:text-black transition-colors touch-manipulation p-2 -m-2 rounded"
                        >
                          <Phone size={16} />
                          <span>01603 336 439</span>
                        </a>
                        <a 
                          href="mailto:<EMAIL>"
                          className="flex items-center space-x-3 text-sm text-gray-600 hover:text-black transition-colors touch-manipulation p-2 -m-2 rounded"
                        >
                          <Mail size={16} />
                          <span className="truncate"><EMAIL></span>
                        </a>
                        <a 
                          href="https://instagram.com/jcc_.ltd"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-3 text-sm text-gray-600 hover:text-black transition-colors touch-manipulation p-2 -m-2 rounded"
                        >
                          <Instagram size={16} />
                          <span>@jcc_.ltd</span>
                        </a>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </header>
    </>
  );
} 