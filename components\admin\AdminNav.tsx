'use client'

import { createClient } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { LogOut, User, Settings } from 'lucide-react'
import Image from 'next/image'

interface AdminNavProps {
  user: {
    id: string
    email: string
    name: string
    created_at: string
  }
}

export default function AdminNav({ user }: AdminNavProps) {
  const router = useRouter()
  const supabase = createClient()

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/admin/login')
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex">
        {/* Sidebar spacer to align with sidebar */}
        <div className="w-64 flex-shrink-0"></div>
        
        {/* Main header content aligned with main content */}
        <div className="flex-1">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16 md:h-20">
              {/* Left side - Logo and branding */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <Image
                    src="/images/logo-main.png"
                    alt="JCC Logo"
                    width={40}
                    height={40}
                    className="h-8 w-auto md:h-10"
                  />
                  <div>
                    <h1 className="text-xl md:text-2xl font-bold text-gray-900 gilroy-text-bold">
                      JCC Admin Panel
                    </h1>
                    <p className="text-xs text-gray-500 gilroy-text hidden md:block">
                      The Campervan, Caravan and Motorhome Guys
                    </p>
                  </div>
                </div>
              </div>

              {/* Right side - User info and actions */}
              <div className="flex items-center space-x-4">
                {/* User info */}
                <div className="hidden sm:flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50">
                  <div className="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full">
                    <User className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900 gilroy-text-medium">{user.name}</p>
                    <p className="text-gray-500 gilroy-text text-xs">{user.email}</p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-gray-900 hover:bg-gray-100 gilroy-text"
                  >
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:ml-2 sm:inline">Settings</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSignOut}
                    className="flex items-center space-x-2 border-gray-300 text-gray-700 hover:bg-gray-50 gilroy-text"
                  >
                    <LogOut className="h-4 w-4" />
                    <span className="hidden sm:inline">Sign Out</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
} 