import { createServerSupabaseClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import { Button } from '@/components/ui/button'
import { 
  ArrowLeft, 
  Reply, 
  Calendar, 
  User, 
  MessageSquare, 
  Palette, 
  Paperclip, 
  HelpCircle,
  Car
} from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

interface Contact {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  service_type?: string
  vehicle_type?: string
  vehicle_make?: string
  vehicle_model?: string
  vehicle_year?: number
  message: string
  status: 'new' | 'responded' | 'archived'
  created_at: string
  admin_response?: string
  response_sent_at?: string
  service_questions?: Record<string, string>
  selected_color?: string
  attachments?: string[]
  attachment_urls?: string[]
}

interface ContactDetailsPageProps {
  params: Promise<{ id: string }>
}



const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default async function ContactDetailsPage({ params }: ContactDetailsPageProps) {
  const { id } = await params
  const supabase = await createServerSupabaseClient()
  
  const { data: contact, error } = await supabase
    .from('contact_submissions')
    .select('*')
    .eq('id', id)
    .single()

  if (error || !contact) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/contacts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Contacts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Contact Details</h1>
            <p className="text-lg text-gray-600 mt-1">View and manage contact submission</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Contact Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Contact Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-xl font-semibold text-gray-900">
                <User className="h-5 w-5 mr-2" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Name</label>
                  <p className="mt-1 text-base font-medium text-gray-900">{contact.name}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Email</label>
                  <p className="mt-1 text-base font-medium text-gray-900">
                    <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-800">
                      {contact.email}
                    </a>
                  </p>
                </div>
                {contact.phone && (
                  <div>
                    <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Phone</label>
                    <p className="mt-1 text-base font-medium text-gray-900">
                      <a href={`tel:${contact.phone}`} className="text-blue-600 hover:text-blue-800">
                        {contact.phone}
                      </a>
                    </p>
                  </div>
                )}
                {contact.service_type && (
                  <div>
                    <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Service Type</label>
                    <p className="mt-1 text-base font-medium text-gray-900 capitalize">{contact.service_type}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Information */}
          {(contact.vehicle_make || contact.vehicle_model || contact.vehicle_year || contact.vehicle_type) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-gray-900">
                  <Car className="h-5 w-5 mr-2" />
                  Vehicle Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {contact.vehicle_type && (
                    <div>
                      <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Vehicle Type</label>
                      <p className="mt-1 text-base font-medium text-gray-900 capitalize">{contact.vehicle_type}</p>
                    </div>
                  )}
                  {contact.vehicle_make && (
                    <div>
                      <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Make</label>
                      <p className="mt-1 text-base font-medium text-gray-900">{contact.vehicle_make}</p>
                    </div>
                  )}
                  {contact.vehicle_model && (
                    <div>
                      <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Model</label>
                      <p className="mt-1 text-base font-medium text-gray-900">{contact.vehicle_model}</p>
                    </div>
                  )}
                  {contact.vehicle_year && (
                    <div>
                      <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Year</label>
                      <p className="mt-1 text-base font-medium text-gray-900">{contact.vehicle_year}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Message */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-xl font-semibold text-gray-900">
                <MessageSquare className="h-5 w-5 mr-2" />
                Message
              </CardTitle>
            </CardHeader>
                          <CardContent>
                <p className="text-base text-gray-900 whitespace-pre-wrap leading-relaxed">{contact.message}</p>
              </CardContent>
          </Card>

          {/* Service-specific questions */}
          {contact.service_questions && Object.keys(contact.service_questions).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2" />
                  Service Questions
                </CardTitle>
              </CardHeader>
                <CardContent className="space-y-3">
                 {Object.entries(contact.service_questions).map(([question, answer]) => (
                   <div key={question} className="border-l-4 border-blue-500 pl-4">
                     <p className="text-sm font-medium text-gray-700">{question}</p>
                     <p className="text-sm text-gray-900 mt-1">{String(answer)}</p>
                   </div>
                 ))}
              </CardContent>
            </Card>
          )}

          {/* Color Selection */}
          {contact.selected_color && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="h-5 w-5 mr-2" />
                  Selected Color
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-8 h-8 rounded-full border-2 border-gray-300"
                    style={{ backgroundColor: contact.selected_color }}
                  />
                  <span className="text-sm font-mono text-gray-700">{contact.selected_color}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Attachments */}
          {contact.attachment_urls && contact.attachment_urls.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Paperclip className="h-5 w-5 mr-2" />
                  Attachments ({contact.attachment_urls.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                   {contact.attachment_urls.map((url: string, index: number) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <Paperclip className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-700 truncate">
                          {contact.attachments?.[index] || `Attachment ${index + 1}`}
                        </span>
                      </div>
                      <a 
                        href={url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
                      >
                        View →
                      </a>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Admin Response */}
          {contact.admin_response && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Reply className="h-5 w-5 mr-2" />
                  Admin Response
                </CardTitle>
                <CardDescription>
                  Sent on {contact.response_sent_at ? formatDate(contact.response_sent_at) : 'Unknown'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{contact.admin_response}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                <div>
                  <p className="text-sm font-medium">Contact Submitted</p>
                  <p className="text-xs text-gray-500">{formatDate(contact.created_at)}</p>
                </div>
              </div>
              {contact.response_sent_at && (
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">Response Sent</p>
                    <p className="text-xs text-gray-500">{formatDate(contact.response_sent_at)}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {contact.status === 'new' && (
                <Link href={`/admin/contacts/${contact.id}/respond`} className="block">
                  <Button className="w-full">
                    <Reply className="h-4 w-4 mr-2" />
                    Respond to Contact
                  </Button>
                </Link>
              )}
              {contact.status !== 'new' && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No actions available
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 