'use client';

import { useState, useEffect } from 'react';


import Image from 'next/image';
import Link from 'next/link';
import { Palette, Clock, Camera, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import JCCStickyFooter from '@/components/footer';
import ResponsiveVideo from '@/components/ui/responsive-video';
import Header from '@/components/header';

// Color palette options
const colorPalettes = [
  {
    id: 'classic',
    name: 'Classic Colors',
    colors: ['#000000', '#FFFFFF', '#C0C0C0', '#8B0000', '#000080', '#006400'],
    description: 'Timeless colors that never go out of style',
    popular: true
  },
  {
    id: 'modern',
    name: 'Modern Metallics',
    colors: ['#2F4F4F', '#708090', '#696969', '#A9A9A9', '#D3D3D3', '#F5F5F5'],
    description: 'Contemporary metallic finishes for the modern vehicle',
    popular: false
  },
  {
    id: 'bold',
    name: 'Bold Statement',
    colors: ['#FF4500', '#FFD700', '#32CD32', '#1E90FF', '#9932CC', '#FF1493'],
    description: 'Make a statement with vibrant, eye-catching colors',
    popular: false
  }
];

// Finish types
const finishTypes = [
  {
    id: 'gloss',
    name: 'High Gloss',
    description: 'Mirror-like finish with maximum depth and shine',
    durability: 95,
    maintenance: 'Medium',
    cost: '£££',
    image: '/images/sticky-scroll-component-images/1.png'
  },
  {
    id: 'satin',
    name: 'Satin Finish',
    description: 'Elegant low-sheen finish with sophisticated appeal',
    durability: 90,
    maintenance: 'Low',
    cost: '££',
    image: '/images/sticky-scroll-component-images/2.jpeg'
  },
  {
    id: 'matte',
    name: 'Matte Black',
    description: 'Ultra-modern flat finish for contemporary styling',
    durability: 85,
    maintenance: 'High',
    cost: '££££',
    image: '/images/sticky-scroll-component-images/3.jpeg'
  }
];

// Featured projects
const featuredProjects = [
  {
    id: 'vw-crafter',
    title: 'VW Crafter',
    subtitle: 'Complete Transformation',
    description: 'From tired white to stunning black - this Crafter got the full treatment',
    image: '/images/sticky-scroll-component-images/4.jpeg',
    beforeImage: '/images/sticky-scroll-component-images/5.jpeg',
    category: 'Commercial Vehicle',
    duration: '7 days',
    featured: true
  },
  {
    id: 'peugeot-boxer',
    title: 'Peugeot Boxer',
    subtitle: 'Professional Respray',
    description: 'Bringing this hardworking van back to showroom condition',
    image: '/images/sticky-scroll-component-images/6.jpeg',
    beforeImage: '/images/sticky-scroll-component-images/7.jpeg',
    category: 'Van Respray',
    duration: '5 days',
    featured: false
  },
  {
    id: 'classic-restoration',
    title: 'Classic Restoration',
    subtitle: 'Concours Quality',
    description: 'Award-winning finish on this beautiful classic vehicle',
    image: '/images/sticky-scroll-component-images/8.jpeg',
    beforeImage: '/images/sticky-scroll-component-images/9.jpeg',
    category: 'Classic Car',
    duration: '14 days',
    featured: false
  }
];

// Process steps
const processSteps = [
  {
    step: 1,
    title: 'Color Consultation',
    description: 'Work with you to choose the perfect color and finish',
    duration: '1 hour',
    includes: ['Color matching', 'Finish selection', 'Sample viewing', 'Cost estimation']
  },
  {
    step: 2,
    title: 'Surface Preparation',
    description: 'Meticulous preparation for the perfect foundation',
    duration: '2-3 days',
    includes: ['Paint removal', 'Surface cleaning', 'Primer application', 'Sanding & smoothing']
  },
  {
    step: 3,
    title: 'Paint Application',
    description: 'Expert spray application in controlled environment',
    duration: '2-3 days',
    includes: ['Base coat', 'Color coats', 'Clear coat', 'Curing process']
  },
  {
    step: 4,
    title: 'Quality Finishing',
    description: 'Final polishing and quality control inspection',
    duration: '1-2 days',
    includes: ['Hand polishing', 'Quality checks', 'Final detailing', 'Customer handover']
  }
];

export default function RespraysPage() {
  const [selectedPalette, setSelectedPalette] = useState<string>('classic');
  const [selectedFinish, setSelectedFinish] = useState<string>('gloss');
  const [selectedProject, setSelectedProject] = useState<string>('vw-crafter');

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      <Header onContactClick={() => scrollToSection('contact')} />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Professional Automotive Paint Spray Booth"
            fill
            className="object-cover"
            priority
          />
        </div>
        
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black/75 z-5"></div>
        
        {/* Animated Paint Drops */}
        <div className="absolute inset-0 opacity-15 z-10">
          {Array.from({ length: 15 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${(i * 17 + 8) % 100}%`,
                top: `${(i * 23 + 5) % 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                scale: [1, 1.3, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 6 + (i % 3),
                repeat: Infinity,
                delay: i * 0.4
              }}
            >
              <div 
                className="w-8 h-8 rounded-full"
                style={{
                  background: `linear-gradient(45deg, #fff ${i * 10}%, transparent ${i * 10 + 50}%)`
                }}
              />
            </motion.div>
          ))}
        </div>
        
        {/* Grid Pattern Overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)] z-15"></div>
        
        <div className="relative z-20 text-center max-w-6xl mx-auto px-4">
          <motion.div 
            className="mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <h1 className="gilroy-text-bold text-5xl md:text-7xl lg:text-8xl font-black uppercase tracking-tight text-white mb-6 leading-none">
              Perfect
              <br />
              <span className="text-gray-300">Resprays</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </motion.div>
           
          <motion.p 
            className="gilroy-text text-xl md:text-2xl font-light mb-6 max-w-4xl mx-auto leading-relaxed text-gray-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            Transform your vehicle with professional automotive paint systems
          </motion.p>
           
          <motion.p 
            className="gilroy-text text-lg md:text-xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            From classic colors to modern metallics - showroom-quality finishes that last.
          </motion.p>
           
          <motion.div 
            className="flex flex-col sm:flex-row gap-6 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <button 
              onClick={() => scrollToSection('colors')}
              className="gilroy-text-bold bg-white text-black px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Explore Colors</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Explore Colors
              </span>
            </button>
            <button 
              onClick={() => scrollToSection('projects')}
              className="gilroy-text-bold border-2 border-white text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">View Projects</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                View Projects
              </span>
            </button>
          </motion.div>
        </div>
      </section>

      {/* Interactive Color Palette Section */}
      <section id="colors" className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black">
              Choose Your Color
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-12"></div>
            <p className="gilroy-text text-2xl md:text-3xl font-light text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Explore our curated color palettes and find the perfect shade for your vehicle
            </p>
          </div>

          {/* Color Palette Tabs */}
          <div className="flex flex-wrap justify-center gap-4 mb-16">
            {colorPalettes.map((palette) => (
              <button
                key={palette.id}
                onClick={() => setSelectedPalette(palette.id)}
                className={`px-8 py-4 border-2 transition-all duration-300 ${
                  selectedPalette === palette.id
                    ? 'bg-black text-white border-black'
                    : 'bg-white text-black border-gray-200 hover:border-black'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {palette.popular && (
                    <Star size={16} className={selectedPalette === palette.id ? 'text-white' : 'text-black'} />
                  )}
                  <span className="gilroy-text-bold font-bold uppercase tracking-wider">{palette.name}</span>
                </div>
              </button>
            ))}
          </div>

          {/* Selected Palette Display */}
          <AnimatePresence mode="wait">
            {selectedPalette && (
              <motion.div
                key={selectedPalette}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -30 }}
                transition={{ duration: 0.3 }}
                className="bg-black text-white p-12"
              >
                {(() => {
                  const palette = colorPalettes.find(p => p.id === selectedPalette);
                  if (!palette) return null;
                  
                  return (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                      <div>
                        <h3 className="gilroy-text-bold text-3xl md:text-4xl font-black uppercase tracking-tight text-white mb-6">
                          {palette.name}
                        </h3>
                        <p className="gilroy-text text-xl text-gray-300 mb-8 leading-relaxed">
                          {palette.description}
                        </p>
                        
                        <div className="grid grid-cols-3 gap-4 mb-8">
                          {palette.colors.map((color, index) => (
                            <motion.div
                              key={index}
                              className="aspect-square border-2 border-white/20 cursor-pointer group"
                              style={{ backgroundColor: color }}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <div className="w-full h-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black/50">
                                <span className="gilroy-text-bold text-white text-sm font-bold uppercase tracking-wider">
                                  {color}
                                </span>
                              </div>
                            </motion.div>
                          ))}
                        </div>

                        <Link 
                          href="/#contact"
                          className="gilroy-text-bold bg-white text-black px-8 py-3 font-bold uppercase tracking-wider relative overflow-hidden group inline-block"
                        >
                          <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Quote for This Palette</span>
                          <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                          <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                            Get Quote for This Palette
                          </span>
                        </Link>
                      </div>
                      
                      <div className="bg-gray-800 aspect-video flex items-center justify-center">
                        <div className="text-center">
                          <Palette size={48} className="text-gray-400 mx-auto mb-4" />
                          <p className="gilroy-text text-gray-400">
                            Color Samples
                            <br />
                            <span className="text-sm">Coming Soon</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* Finish Types Comparison */}
      <section className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl font-black uppercase tracking-tight mb-8 text-black">
              Finish Options
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-12"></div>
            <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Choose the perfect finish type to complement your color selection
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {finishTypes.map((finish, index) => (
              <motion.div
                key={finish.id}
                className={`bg-white p-8 shadow-lg cursor-pointer transition-all duration-300 ${
                  selectedFinish === finish.id ? 'ring-4 ring-black' : 'hover:shadow-xl'
                }`}
                onClick={() => setSelectedFinish(finish.id)}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-gray-200 aspect-video mb-6 flex items-center justify-center">
                  <Camera size={32} className="text-gray-400" />
                </div>
                
                <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-4 text-black">
                  {finish.name}
                </h3>
                
                <p className="gilroy-text text-gray-600 mb-6 leading-relaxed">
                  {finish.description}
                </p>
                
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="gilroy-text-bold text-sm font-bold uppercase tracking-wider text-black">Durability</span>
                      <span className="gilroy-text text-sm text-gray-600">{finish.durability}%</span>
                    </div>
                    <div className="w-full bg-gray-200 h-2">
                      <div 
                        className="bg-black h-2 transition-all duration-500"
                        style={{ width: `${finish.durability}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="gilroy-text-bold font-bold uppercase tracking-wider text-black block">Maintenance</span>
                      <span className="gilroy-text text-gray-600">{finish.maintenance}</span>
                    </div>
                    <div>
                      <span className="gilroy-text-bold font-bold uppercase tracking-wider text-black block">Cost</span>
                      <span className="gilroy-text text-gray-600">{finish.cost}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects Showcase */}
      <section id="projects" className="py-32 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl font-black uppercase tracking-tight mb-8 text-white">
              Our Work
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-12"></div>
            <p className="gilroy-text text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              See the incredible transformations we&apos;ve achieved for our customers
            </p>
          </div>

          {/* Project Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-16">
            {featuredProjects.map((project) => (
              <button
                key={project.id}
                onClick={() => setSelectedProject(project.id)}
                className={`px-6 py-3 border-2 transition-all duration-300 ${
                  selectedProject === project.id
                    ? 'bg-white text-black border-white'
                    : 'bg-transparent text-white border-gray-600 hover:border-white'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {project.featured && (
                    <Star size={14} className={selectedProject === project.id ? 'text-black' : 'text-white'} />
                  )}
                  <span className="gilroy-text-bold font-bold uppercase tracking-wider text-sm">{project.title}</span>
                </div>
              </button>
            ))}
          </div>

          {/* Selected Project Display */}
          <AnimatePresence mode="wait">
            {selectedProject && (
              <motion.div
                key={selectedProject}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -30 }}
                transition={{ duration: 0.3 }}
              >
                {(() => {
                  const project = featuredProjects.find(p => p.id === selectedProject);
                  if (!project) return null;
                  
                  return (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                      <div>
                        <div className="mb-6">
                          <span className="gilroy-text text-sm uppercase tracking-wider text-gray-400 block mb-2">
                            {project.category}
                          </span>
                          <h3 className="gilroy-text-bold text-4xl md:text-5xl font-black uppercase tracking-tight text-white mb-2">
                            {project.title}
                          </h3>
                          <p className="gilroy-text text-xl text-gray-300 mb-4">
                            {project.subtitle}
                          </p>
                        </div>
                        
                        <p className="gilroy-text text-lg text-gray-300 mb-8 leading-relaxed">
                          {project.description}
                        </p>

                        <div className="flex items-center space-x-8 mb-8">
                          <div className="flex items-center space-x-2">
                            <Clock size={16} className="text-white" />
                            <span className="gilroy-text text-gray-300">{project.duration}</span>
                          </div>
                          {project.featured && (
                            <div className="flex items-center space-x-2">
                              <Star size={16} className="text-white" />
                              <span className="gilroy-text text-gray-300">Featured Project</span>
                            </div>
                          )}
                        </div>

                        <Link 
                          href={`/resprays/${project.id}`}
                          className="gilroy-text-bold bg-white text-black px-8 py-3 font-bold uppercase tracking-wider relative overflow-hidden group inline-block"
                        >
                          <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">View Full Project</span>
                          <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                          <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                            View Full Project
                          </span>
                        </Link>
                      </div>
                      
                      <div className="space-y-4">
                        <div className="bg-gray-800 aspect-video flex items-center justify-center">
                          <div className="text-center">
                            <Camera size={48} className="text-gray-400 mx-auto mb-4" />
                            <p className="gilroy-text text-gray-400">
                              After Image
                              <br />
                              <span className="text-sm">Coming Soon</span>
                            </p>
                          </div>
                        </div>
                        <div className="bg-gray-700 aspect-video flex items-center justify-center">
                          <div className="text-center">
                            <Camera size={32} className="text-gray-500 mx-auto mb-2" />
                            <p className="gilroy-text text-gray-500 text-sm">
                              Before Image
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* Paintjob Showcase Videos */}
      <section className="py-32 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl font-black uppercase tracking-tight mb-8 text-black">
              Paintjob Showcase
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-12"></div>
            <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Watch our skilled team transform vehicles with professional spray techniques
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Paintjob Showcase Video 1 */}
                         <div className="space-y-6">
               <ResponsiveVideo 
                 src="/videos/website/paintjob-showcase-2.mov"
                 aspectRatio="auto"
                 maxHeight="60vh"
                 muted={true}
                 className="shadow-lg"
               />
               <div className="text-center">
                 <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-2 text-black">
                   Professional Spray Technique
                 </h3>
                 <p className="gilroy-text text-gray-600 leading-relaxed">
                   Our expert technicians demonstrate precise spray patterns and professional finishing techniques
                 </p>
               </div>
             </div>

             {/* Paintjob Showcase Video 2 */}
             <div className="space-y-6">
               <ResponsiveVideo 
                 src="/videos/website/paintjob-showcase-3.mov"
                 aspectRatio="auto"
                 maxHeight="60vh"
                 muted={true}
                 className="shadow-lg"
               />
               <div className="text-center">
                 <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-2 text-black">
                   Detailed Paint Application
                 </h3>
                 <p className="gilroy-text text-gray-600 leading-relaxed">
                   See the meticulous attention to detail that goes into every paint job at JCC
                 </p>
               </div>
             </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className="gilroy-text text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              Ready to transform your vehicle with a professional respray? Get in touch for a free quote.
            </p>
            <Link 
              href="/#contact"
              className="gilroy-text-bold bg-black text-white px-8 py-4 font-bold uppercase tracking-wider relative overflow-hidden group inline-block"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Your Quote</span>
              <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Your Quote
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl font-black uppercase tracking-tight mb-8 text-black">
              Our Process
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-12"></div>
            <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Professional respray process ensuring perfect results every time
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={step.step}
                className="text-center"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                {/* Step Number */}
                <div className="w-16 h-16 bg-black text-white flex items-center justify-center font-bold text-2xl mx-auto mb-6">
                  {step.step}
                </div>
                
                <h3 className="gilroy-text-bold text-xl font-bold uppercase tracking-wider mb-4 text-black">
                  Step {step.step}: {step.title}
                </h3>
                
                <p className="gilroy-text text-gray-600 mb-6 leading-relaxed">
                  {step.description}
                </p>
                
                <div className="flex items-center space-x-2 mb-6">
                  <Clock size={16} className="text-black" />
                  <span className="gilroy-text text-sm text-gray-600">{step.duration}</span>
                </div>
                
                <div>
                  <h4 className="gilroy-text-bold text-sm font-bold uppercase tracking-wider text-black mb-2">
                    What&apos;s Included:
                  </h4>
                  <ul className="grid grid-cols-2 gap-2">
                    {step.includes.map((item, itemIndex) => (
                      <li key={itemIndex} className="gilroy-text text-sm text-gray-600 flex items-center justify-center space-x-2">
                        <span>•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="gilroy-text-bold text-4xl md:text-5xl font-black uppercase tracking-tight mb-8 text-white">
            Ready For Your Respray?
          </h2>
          <div className="w-24 h-1 bg-white mx-auto mb-8"></div>
          <p className="gilroy-text text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-12">
            Transform your vehicle with a professional respray. From color consultation to final finish - we handle every detail with expert care.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link 
              href="/#contact"
              className="gilroy-text-bold bg-white text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Your Quote</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Your Quote
              </span>
            </Link>
            
            <button 
              onClick={() => scrollToSection('colors')}
              className="gilroy-text-bold border-2 border-white text-white px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Explore Colors</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Explore Colors
              </span>
            </button>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
} 