'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Plus, 
  CheckCircle, 
  Circle, 
  Edit, 
  Trash2, 
  Calendar,
  AlertCircle,
  CheckCircle2
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { projectTodoSchema, type ProjectTodoInput } from '@/lib/validations'

interface Todo {
  id: string
  project_id: string
  title: string
  description: string | null
  completed: boolean
  priority: 'low' | 'medium' | 'high'
  due_date: string | null
  created_at: string
  updated_at: string
}

interface ProjectTodosProps {
  projectId: string
  todos: Todo[]
}

type NewTodo = {
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  due_date: string
}

export default function ProjectTodos({ projectId, todos }: ProjectTodosProps) {
  const [isAddingTodo, setIsAddingTodo] = useState(false)
  const [editingTodo, setEditingTodo] = useState<Todo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  // Form for adding new todos
  const {
    register: registerAdd,
    handleSubmit: handleAddSubmit,
    formState: { errors: addErrors },
    reset: resetAdd,
  } = useForm<ProjectTodoInput>({
    resolver: zodResolver(projectTodoSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
      dueDate: '',
    }
  })

  // Form for editing todos
  const {
    register: registerEdit,
    handleSubmit: handleEditSubmit,
    formState: { errors: editErrors },
    reset: resetEdit,
    setValue: setEditValue,
  } = useForm<ProjectTodoInput>({
    resolver: zodResolver(projectTodoSchema),
  })

  const onAddTodo = async (data: ProjectTodoInput) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_todos')
        .insert([{
          project_id: projectId,
          title: data.title,
          description: data.description || null,
          priority: data.priority,
          due_date: data.dueDate || null,
          completed: false
        }])

      if (error) throw error

      resetAdd()
      setIsAddingTodo(false)
      router.refresh()
    } catch (error) {
      console.error('Error adding todo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateTodo = async (todo: Todo, updates: Partial<Todo>) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_todos')
        .update(updates)
        .eq('id', todo.id)

      if (error) throw error

      router.refresh()
    } catch (error) {
      console.error('Error updating todo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleComplete = async (todo: Todo) => {
    await handleUpdateTodo(todo, { completed: !todo.completed })
  }

  const handleDeleteTodo = async (todoId: string) => {
    if (!confirm('Are you sure you want to delete this task?')) return

    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_todos')
        .delete()
        .eq('id', todoId)

      if (error) throw error

      router.refresh()
    } catch (error) {
      console.error('Error deleting todo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const onEditTodo = async (data: ProjectTodoInput) => {
    if (!editingTodo) return

    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('project_todos')
        .update({
          title: data.title,
          description: data.description,
          priority: data.priority,
          due_date: data.dueDate
        })
        .eq('id', editingTodo.id)

      if (error) throw error

      setEditingTodo(null)
      resetEdit()
      router.refresh()
    } catch (error) {
      console.error('Error updating todo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditTodo = (todo: Todo) => {
    setEditingTodo(todo)
    setEditValue('title', todo.title)
    setEditValue('description', todo.description || '')
    setEditValue('priority', todo.priority)
    setEditValue('dueDate', todo.due_date || '')
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const completedTodos = todos.filter(todo => todo.completed)
  const pendingTodos = todos.filter(todo => !todo.completed)
  const completionPercentage = todos.length > 0 ? Math.round((completedTodos.length / todos.length) * 100) : 0

  return (
    <div className="space-y-4">
      {/* Progress Overview */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 gilroy-text-medium">Task Progress</span>
          <span className="text-sm font-bold text-gray-900 gilroy-text-bold">{completionPercentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
        <div className="flex items-center justify-between mt-2 text-xs text-gray-600 gilroy-text">
          <span>{completedTodos.length} completed</span>
          <span>{pendingTodos.length} remaining</span>
        </div>
      </div>

      {/* Add Todo Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 gilroy-text-bold">Tasks</h3>
        <Dialog open={isAddingTodo} onOpenChange={setIsAddingTodo}>
          <DialogTrigger asChild>
            <Button size="sm" className="gilroy-text-medium">
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="gilroy-text-bold">Add New Task</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleAddSubmit(onAddTodo)} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Title</label>
                <Input
                  {...registerAdd('title')}
                  placeholder="Enter task title"
                  className={`gilroy-text ${addErrors.title ? 'border-red-500' : ''}`}
                />
                {addErrors.title && (
                  <p className="text-sm text-red-600 mt-1">{addErrors.title.message}</p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Description</label>
                <Textarea
                  {...registerAdd('description')}
                  placeholder="Enter task description (optional)"
                  className={`gilroy-text ${addErrors.description ? 'border-red-500' : ''}`}
                />
                {addErrors.description && (
                  <p className="text-sm text-red-600 mt-1">{addErrors.description.message}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Priority</label>
                  <select
                    {...registerAdd('priority')}
                    className={`w-full p-2 border border-gray-300 rounded-md gilroy-text ${addErrors.priority ? 'border-red-500' : ''}`}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                  {addErrors.priority && (
                    <p className="text-sm text-red-600 mt-1">{addErrors.priority.message}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Due Date</label>
                  <Input
                    type="date"
                    {...registerAdd('dueDate')}
                    className={`gilroy-text ${addErrors.dueDate ? 'border-red-500' : ''}`}
                  />
                  {addErrors.dueDate && (
                    <p className="text-sm text-red-600 mt-1">{addErrors.dueDate.message}</p>
                  )}
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsAddingTodo(false)
                    resetAdd()
                  }}
                  className="gilroy-text"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="gilroy-text-medium"
                >
                  {isLoading ? 'Adding...' : 'Add Task'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Todo List */}
      <div className="space-y-2">
        {todos.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <CheckCircle2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="gilroy-text">No tasks yet. Add your first task to get started!</p>
          </div>
        ) : (
          <>
            {/* Pending Tasks */}
            {pendingTodos.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700 gilroy-text-medium">Pending Tasks</h4>
                {pendingTodos.map((todo) => (
                  <TodoItem
                    key={todo.id}
                    todo={todo}
                    onToggleComplete={handleToggleComplete}
                    onEdit={handleEditTodo}
                    onDelete={handleDeleteTodo}
                    isLoading={isLoading}
                  />
                ))}
              </div>
            )}

            {/* Completed Tasks */}
            {completedTodos.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700 gilroy-text-medium">Completed Tasks</h4>
                {completedTodos.map((todo) => (
                  <TodoItem
                    key={todo.id}
                    todo={todo}
                    onToggleComplete={handleToggleComplete}
                    onEdit={handleEditTodo}
                    onDelete={handleDeleteTodo}
                    isLoading={isLoading}
                  />
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Edit Todo Dialog */}
      {editingTodo && (
        <Dialog open={!!editingTodo} onOpenChange={() => setEditingTodo(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="gilroy-text-bold">Edit Task</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleEditSubmit(onEditTodo)} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Title</label>
                <Input
                  {...registerEdit('title')}
                  className={`gilroy-text ${editErrors.title ? 'border-red-500' : ''}`}
                />
                {editErrors.title && (
                  <p className="text-sm text-red-600 mt-1">{editErrors.title.message}</p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Description</label>
                <Textarea
                  {...registerEdit('description')}
                  className={`gilroy-text ${editErrors.description ? 'border-red-500' : ''}`}
                />
                {editErrors.description && (
                  <p className="text-sm text-red-600 mt-1">{editErrors.description.message}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Priority</label>
                  <select
                    {...registerEdit('priority')}
                    className={`w-full p-2 border border-gray-300 rounded-md gilroy-text ${editErrors.priority ? 'border-red-500' : ''}`}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                  {editErrors.priority && (
                    <p className="text-sm text-red-600 mt-1">{editErrors.priority.message}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 gilroy-text-medium">Due Date</label>
                  <Input
                    type="date"
                    {...registerEdit('dueDate')}
                    className={`gilroy-text ${editErrors.dueDate ? 'border-red-500' : ''}`}
                  />
                  {editErrors.dueDate && (
                    <p className="text-sm text-red-600 mt-1">{editErrors.dueDate.message}</p>
                  )}
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEditingTodo(null)}
                  className="gilroy-text"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="gilroy-text-medium"
                >
                  {isLoading ? 'Updating...' : 'Update Task'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

// TodoItem component
interface TodoItemProps {
  todo: Todo
  onToggleComplete: (todo: Todo) => void
  onEdit: (todo: Todo) => void
  onDelete: (todoId: string) => void
  isLoading: boolean
}

function TodoItem({ todo, onToggleComplete, onEdit, onDelete, isLoading }: TodoItemProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const isOverdue = todo.due_date && new Date(todo.due_date) < new Date() && !todo.completed

  return (
    <div className={`p-3 border rounded-lg transition-all duration-200 ${
      todo.completed 
        ? 'bg-gray-50 border-gray-200' 
        : 'bg-white border-gray-200 hover:border-gray-300'
    }`}>
      <div className="flex items-start space-x-3">
        <button
          onClick={() => onToggleComplete(todo)}
          disabled={isLoading}
          className="mt-1 flex-shrink-0"
        >
          {todo.completed ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <Circle className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          )}
        </button>

        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={`font-medium gilroy-text-medium ${
              todo.completed ? 'line-through text-gray-500' : 'text-gray-900'
            }`}>
              {todo.title}
            </h4>
            <div className="flex items-center space-x-2">
              <Badge className={`${getPriorityColor(todo.priority)} gilroy-text text-xs`}>
                {todo.priority}
              </Badge>
              {isOverdue && (
                <Badge className="bg-red-100 text-red-800 gilroy-text text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Overdue
                </Badge>
              )}
            </div>
          </div>

          {todo.description && (
            <p className={`text-sm mt-1 gilroy-text ${
              todo.completed ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {todo.description}
            </p>
          )}

          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center space-x-4 text-xs text-gray-500 gilroy-text">
              {todo.due_date && (
                <div className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>Due: {new Date(todo.due_date).toLocaleDateString()}</span>
                </div>
              )}
              <span>Created: {new Date(todo.created_at).toLocaleDateString()}</span>
            </div>

            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(todo)}
                disabled={isLoading}
                className="h-8 w-8 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(todo.id)}
                disabled={isLoading}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 