export type ConsentStatus = 'accepted' | 'declined' | null

export const getConsentStatus = (): ConsentStatus => {
  if (typeof window === 'undefined') return null
  
  const consent = localStorage.getItem('gdpr-consent')
  if (consent === 'accepted') return 'accepted'
  if (consent === 'declined') return 'declined'
  return null
}

export const setConsentStatus = (status: 'accepted' | 'declined'): void => {
  if (typeof window === 'undefined') return
  
  localStorage.setItem('gdpr-consent', status)
}

export const clearConsentStatus = (): void => {
  if (typeof window === 'undefined') return
  
  localStorage.removeItem('gdpr-consent')
}

export const hasConsentBeenGiven = (): boolean => {
  return getConsentStatus() !== null
}

export const isAnalyticsEnabled = (): boolean => {
  return getConsentStatus() === 'accepted'
} 