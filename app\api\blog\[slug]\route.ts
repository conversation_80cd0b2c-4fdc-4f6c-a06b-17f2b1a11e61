import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params

    if (!slug) {
      return NextResponse.json({ error: 'Slug is required' }, { status: 400 })
    }

    const supabase = await createServerSupabaseClient()

    const { data: article, error } = await supabase
      .from('blog_articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        content,
        content_html,
        featured_image,
        hero_image,
        category,
        tags,
        status,
        published_at,
        seo_title,
        seo_description,
        created_at,
        updated_at
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .single()

    if (error || !article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 })
    }

    // Transform data to match frontend interface
    const transformedArticle = {
      id: article.id,
      title: article.title,
      slug: article.slug,
      excerpt: article.excerpt || '',
      content: article.content_html || article.content,
      image: article.featured_image || '/images/winter-mountain.png',
      heroImage: article.hero_image,
      category: article.tags?.[0] || 'General',
      tags: article.tags || [],
      author: 'JCC Team',
      date: article.published_at ? new Date(article.published_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      readTime: `${Math.ceil((article.content?.length || 0) / 1000)} min read`,
      featured: article.tags?.includes('featured') || false,
      seoTitle: article.seo_title,
      seoDescription: article.seo_description,
      createdAt: article.created_at,
      updatedAt: article.updated_at
    }

    // Set cache headers - 24 hour cache with stale-while-revalidate
    const response = NextResponse.json({ article: transformedArticle })

    // Cache for 24 hours with 12 hour stale-while-revalidate
    response.headers.set('Cache-Control', 's-maxage=86400, stale-while-revalidate=43200')

    // Add cache tag for targeted invalidation
    response.headers.set('Cache-Tags', `blog-article-${slug}`)

    return response

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 