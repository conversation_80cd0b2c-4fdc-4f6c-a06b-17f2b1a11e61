'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Send, Sparkles, Wand2 } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import ReactMarkdown from 'react-markdown';

interface ContactResponseFormProps {
  contact: {
    id: string
    name: string
    email: string
    phone?: string
    message: string
    service_type?: string
    service_questions?: Record<string, string>
    selected_color?: string
    vehicle_type?: string
    vehicle_make?: string
    vehicle_model?: string
    vehicle_year?: number
    attachments?: string[]
    attachment_urls?: string[]
  }
}



export default function ContactResponseForm({ contact }: ContactResponseFormProps) {
  const router = useRouter()
  const [response, setResponse] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [pendingSuggestion, setPendingSuggestion] = useState<string | null>(null)
  const [prevResponse, setPrevResponse] = useState<string>('')
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [isEnhancing, setIsEnhancing] = useState(false)
  const [selectedTone, setSelectedTone] = useState<'professional' | 'casual'>('professional')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!response.trim()) return

    setIsSubmitting(true)
    try {
      const res = await fetch('/api/admin/contacts/respond', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactId: contact.id,
          response: response.trim(),
        }),
      })

      if (res.ok) {
        router.push('/admin/contacts')
        router.refresh()
      } else {
        throw new Error('Failed to send response')
      }
    } catch (error) {
      console.error('Error sending response:', error)
      alert('Failed to send response. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getSuggestions = async (tone: 'professional' | 'casual' = selectedTone) => {
    setIsLoadingSuggestions(true)
    try {
      const res = await fetch('/api/ai/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactContext: {
            name: contact.name,
            email: contact.email,
            message: contact.message,
            serviceType: contact.service_type,
            serviceQuestions: contact.service_questions,
            selectedColor: contact.selected_color,
            vehicleType: contact.vehicle_type,
            vehicleMake: contact.vehicle_make,
            vehicleModel: contact.vehicle_model,
            vehicleYear: contact.vehicle_year,
          },
          type: 'suggestions',
          tone,
        }),
      })

      if (res.ok) {
        const data = await res.json()
        const suggestion = data.suggestions?.[0]?.content
        if (suggestion) {
          // Skip preview, paste directly and show accept/decline overlay
          setPrevResponse(response)
          setPendingSuggestion(suggestion)
          setResponse(suggestion)
        }
      } else {
        throw new Error('Failed to get suggestions')
      }
    } catch (error) {
      console.error('Error getting suggestions:', error)
      alert('Failed to get AI suggestions. Please try again.')
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  const enhanceResponse = async (tone: 'professional' | 'casual') => {
    if (!response.trim()) {
      alert('Please write a response first before enhancing it.')
      return
    }

    setIsEnhancing(true)
    try {
      const res = await fetch('/api/ai/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactContext: {
            name: contact.name,
            email: contact.email,
            message: contact.message,
            serviceType: contact.service_type,
            serviceQuestions: contact.service_questions,
            selectedColor: contact.selected_color,
            vehicleType: contact.vehicle_type,
            vehicleMake: contact.vehicle_make,
            vehicleModel: contact.vehicle_model,
            vehicleYear: contact.vehicle_year,
          },
          currentResponse: response,
          type: 'enhance',
          tone,
        }),
      })

      if (res.ok) {
        const data = await res.json()
        const enhancedResponse = data.enhancedResponse
        if (enhancedResponse) {
          // Skip preview, paste directly and show accept/decline overlay
          setPrevResponse(response)
          setPendingSuggestion(enhancedResponse)
          setResponse(enhancedResponse)
        }
      } else {
        throw new Error('Failed to enhance response')
      }
    } catch (error) {
      console.error('Error enhancing response:', error)
      alert('Failed to enhance response. Please try again.')
    } finally {
      setIsEnhancing(false)
    }
  }



  const handleAcceptSuggestion = () => {
    setPendingSuggestion(null)
  }

  const handleDeclineSuggestion = () => {
    if (prevResponse !== undefined) {
      setResponse(prevResponse)
    }
    setPendingSuggestion(null)
  }

  return (
    <div className="space-y-6">
      {/* Response Form */}
      <Card>
        <CardHeader>
          <CardTitle>Send Response</CardTitle>
          <p className="text-sm text-gray-600">
            Your response will be sent to {contact.email}
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* AI Controls - Compact and Subtle */}
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Response</span>
              <div className="flex items-center gap-1">
                <Select value={selectedTone} onValueChange={(value: 'professional' | 'casual') => setSelectedTone(value)}>
                  <SelectTrigger className="w-20 h-6 text-xs border-0 shadow-none bg-transparent text-gray-500 hover:text-gray-700">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="professional">Formal</SelectItem>
                    <SelectItem value="casual">Friendly</SelectItem>
                  </SelectContent>
                </Select>
                
                <button 
                  type="button"
                  onClick={() => getSuggestions(selectedTone)}
                  disabled={isLoadingSuggestions}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                  title="AI Generate Response"
                >
                  {isLoadingSuggestions ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Sparkles className="h-3 w-3" />
                  )}
                </button>

                {response.trim() && (
                  <button 
                    type="button"
                    onClick={() => enhanceResponse(selectedTone)}
                    disabled={isEnhancing}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                    title="Enhance Tone"
                  >
                    {isEnhancing ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Wand2 className="h-3 w-3" />
                    )}
                  </button>
                )}
              </div>
            </div>

            {/* Response Textarea */}
            <div className="relative">
              <Textarea
                value={response}
                onChange={(e) => setResponse(e.target.value)}
                placeholder="Type your response here..."
                className="min-h-[200px] resize-none"
                disabled={isSubmitting}
              />

              {/* Minimal Accept/Decline Overlay */}
              {pendingSuggestion && (
                <div className="absolute top-1 right-1 bg-white/90 backdrop-blur-sm border border-gray-200 shadow-sm rounded px-2 py-1 flex items-center gap-2 text-xs animate-in fade-in duration-200">
                  <span className="text-gray-500 text-xs">AI</span>
                  <button
                    onClick={handleAcceptSuggestion}
                    className="w-5 h-5 flex items-center justify-center bg-green-50 text-green-600 rounded hover:bg-green-100 transition-colors"
                    title="Accept"
                  >
                    ✓
                  </button>
                  <button
                    onClick={handleDeclineSuggestion}
                    className="w-5 h-5 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
                    title="Decline"
                  >
                    ✕
                  </button>
                </div>
              )}
            </div>

            {/* Send Button */}
            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={isSubmitting || !response.trim()}
                className="flex items-center gap-2"
              >
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                Send Response
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/admin/contacts')}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 