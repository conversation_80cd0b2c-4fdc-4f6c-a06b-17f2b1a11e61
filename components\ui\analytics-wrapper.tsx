'use client'

import { useState, useEffect } from 'react'
import { Analytics } from '@vercel/analytics/next'
import GDPRConsent from './gdpr-consent'

export default function AnalyticsWrapper() {
  const [consentGiven, setConsentGiven] = useState<boolean | null>(null)
  const [showConsent, setShowConsent] = useState(false)

  useEffect(() => {
    // Check existing consent on mount
    const existingConsent = localStorage.getItem('gdpr-consent')
    if (existingConsent) {
      setConsentGiven(existingConsent === 'accepted')
    } else {
      setShowConsent(true)
    }
  }, [])

  const handleConsentChange = (consent: boolean) => {
    setConsentGiven(consent)
    setShowConsent(false)
  }

  return (
    <>
      {/* Only render Analytics if consent is given */}
      {consentGiven && <Analytics />}
      
      {/* Show GDPR consent popup if needed */}
      {showConsent && <GDPRConsent onConsentChange={handleConsentChange} />}
    </>
  )
} 