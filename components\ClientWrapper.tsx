'use client'

import { ReactNode } from 'react'

interface ClientWrapperProps {
  children: ReactNode
}

/**
 * Client Wrapper Component
 * 
 * This component ensures that its children are only rendered on the client side,
 * preventing SSR serialization issues with event handlers and other client-side functionality.
 */
export default function ClientWrapper({ children }: ClientWrapperProps) {
  return <>{children}</>
}

/**
 * No-SSR Wrapper Component
 * 
 * This component prevents server-side rendering of its children,
 * useful for components with client-side only functionality.
 */
export function NoSSRWrapper({ children }: ClientWrapperProps) {
  return <div suppressHydrationWarning>{children}</div>
} 