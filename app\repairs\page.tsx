'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Wrench, Shield, Hammer, Clock, CheckCircle, AlertTriangle, Car, Camera } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import JCCStickyFooter from '@/components/footer';
import Header from '@/components/header';

// Repair categories with interactive content
const repairCategories = [
  {
    id: 'accident',
    title: 'Accident Repairs',
    icon: Car,
    description: 'High quality finish on all our repairs using OEM parts',
    services: ['Collision damage repair', 'Panel replacement', 'Dent removal', 'Scratch repair', 'Bumper restoration', 'Paint matching'],
    timeframe: '2-5 days',
    severity: 'High Priority',
    image: '/images/sticky-scroll-component-images/1.png',
    details: 'We can get you back on the road before you know it. Using only original manufacturer parts for a finish that\'s second to none.'
  },
  {
    id: 'rust',
    title: 'Rust & Welding',
    icon: Shield,
    description: 'Saving the oldest vans on the road, from classic VW buses to T5/T6s',
    services: ['Rust removal', 'Structural welding', 'Panel fabrication', 'Corrosion treatment', 'Preventive coating', 'Classic restoration'],
    timeframe: '3-7 days',
    severity: 'Critical',
    image: '/images/sticky-scroll-component-images/2.jpeg',
    details: 'We have helped save some of the oldest vans on the road. Expert welding and rust treatment to extend your vehicle\'s life.'
  },
  {
    id: 'replacement',
    title: 'Replacement Parts',
    icon: Wrench,
    description: 'Original body panels supplied by the original manufacturer',
    services: ['OEM body panels', 'Genuine parts sourcing', 'Panel fitting', 'Color matching', 'Quality finishing', 'Warranty coverage'],
    timeframe: '1-3 days',
    severity: 'Standard',
    image: '/images/sticky-scroll-component-images/3.jpeg',
    details: 'We use original body panels supplied by the original manufacturer. So our finish is second to none.'
  }
];

// Repair process steps
const repairSteps = [
  {
    step: 1,
    title: 'Damage Assessment',
    description: 'Thorough inspection of accident damage and structural integrity',
    duration: '30-60 minutes',
    tools: ['Visual inspection', 'Digital photography', 'Damage documentation', 'Parts assessment']
  },
  {
    step: 2,
    title: 'Parts Sourcing',
    description: 'Ordering genuine OEM parts from original manufacturers',
    duration: '1-3 days',
    tools: ['OEM part numbers', 'Manufacturer catalogs', 'Quality verification', 'Delivery coordination']
  },
  {
    step: 3,
    title: 'Expert Repair',
    description: 'Professional bodywork repair using industry-leading techniques',
    duration: '2-5 days',
    tools: ['Panel beating', 'Welding equipment', 'Paint systems', 'Precision tools']
  },
  {
    step: 4,
    title: 'Quality Finish',
    description: 'Final inspection and quality control before delivery',
    duration: '1 day',
    tools: ['Paint matching', 'Final inspection', 'Quality checks', 'Customer handover']
  }
];

// Testimonials
const testimonials = [
  {
    quote: "Incredible work on my van after the accident. Looks better than new!",
    author: "Sarah M.",
    project: "Collision Repair"
  },
  {
    quote: "Saved my classic VW bus from the rust. These guys are miracle workers!",
    author: "Mike T.",
    project: "Rust Treatment & Welding"
  },
  {
    quote: "Perfect color match and finish. You can't even tell it was damaged.",
    author: "Emma L.",
    project: "Panel Replacement"
  }
];

export default function RepairsPage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>('accident');
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white text-black">
      <Header onContactClick={() => scrollToSection('contact')} />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2132&q=80"
            alt="Professional Auto Body Repair Shop"
            fill
            className="object-cover"
            priority
          />
        </div>
        
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black/70 z-5"></div>
        
        {/* Animated Tool Background */}
        <div className="absolute inset-0 opacity-10 z-10">
          {Array.from({ length: 12 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${(i * 23 + 15) % 100}%`,
                top: `${(i * 31 + 10) % 100}%`,
              }}
              animate={{
                rotate: [0, 180, 360],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 8 + (i % 4),
                repeat: Infinity,
                delay: i * 0.5
              }}
            >
              {i % 3 === 0 ? (
                <Wrench size={40 + (i % 20)} className="text-white" />
              ) : i % 3 === 1 ? (
                <Hammer size={40 + (i % 20)} className="text-white" />
              ) : (
                <Shield size={40 + (i % 20)} className="text-white" />
              )}
            </motion.div>
          ))}
        </div>
        
        {/* Grid Pattern Overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)] z-15"></div>
        
        <div className="relative z-20 text-center max-w-6xl mx-auto px-4">
          <motion.div 
            className="mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <h1 className="gilroy-text-bold text-5xl md:text-7xl lg:text-8xl font-black uppercase tracking-tight text-white mb-6 leading-none">
              Accident
              <br />
              <span className="text-gray-300">Repairs</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto my-8"></div>
          </motion.div>
           
          <motion.p 
            className="gilroy-text text-xl md:text-2xl font-light mb-6 max-w-4xl mx-auto leading-relaxed text-gray-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            From accident damage to rust treatment, we have you covered.
          </motion.p>
           
          <motion.p 
            className="gilroy-text text-lg md:text-xl text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            Quality repairs & expert craftsmanship.
          </motion.p>
           
          <motion.div 
            className="flex flex-col sm:flex-row gap-6 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
          >
            <button 
              onClick={() => scrollToSection('services')}
              className="gilroy-text-bold bg-white text-black px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See Our Services</span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See Our Services
              </span>
            </button>
            <Link 
              href="/#contact"
              className="gilroy-text-bold border-2 border-white text-white px-8 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Quote</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Quote
              </span>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* What We Do Section */}
      <section id="services" className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight mb-8 text-black">
              Our Repair Services
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-2xl md:text-3xl font-light text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From minor dents to major collision damage - we restore your vehicle to its former glory
            </p>
          </div>

          {/* Service Categories */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {repairCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`p-8 border-2 transition-all duration-300 group text-left ${
                  selectedCategory === category.id
                    ? 'bg-black text-white border-black'
                    : 'bg-white text-black border-gray-200 hover:border-black'
                }`}
              >
                <category.icon 
                  size={48} 
                  className={`mb-6 ${
                    selectedCategory === category.id ? 'text-white' : 'text-black'
                  }`} 
                />
                <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-4">
                  {category.title}
                </h3>
                <p className={`gilroy-text text-lg leading-relaxed ${
                  selectedCategory === category.id ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {category.description}
                </p>
              </button>
            ))}
          </div>

          {/* Selected Category Details */}
          <AnimatePresence mode="wait">
            {selectedCategory && (
              <motion.div
                key={selectedCategory}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -30 }}
                transition={{ duration: 0.3 }}
                className="bg-black text-white p-12"
              >
                {(() => {
                  const category = repairCategories.find(c => c.id === selectedCategory);
                  if (!category) return null;
                  
                  return (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                      <div>
                        <div className="flex items-center space-x-4 mb-6">
                          <category.icon size={40} className="text-white" />
                          <h3 className="gilroy-text-bold text-3xl md:text-4xl font-black uppercase tracking-tight text-white">
                            {category.title}
                          </h3>
                        </div>
                        
                        <p className="gilroy-text text-xl text-gray-300 mb-8 leading-relaxed">
                          {category.description}
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                          <div>
                            <h4 className="gilroy-text-bold text-lg font-bold uppercase tracking-wider text-white mb-4">
                              Our Services
                            </h4>
                            <ul className="space-y-2">
                              {category.services.map((service, index) => (
                                <li key={index} className="gilroy-text flex items-center space-x-3 text-gray-300">
                                  <CheckCircle size={16} className="text-white flex-shrink-0" />
                                  <span>{service}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          <div>
                            <h4 className="gilroy-text-bold text-lg font-bold uppercase tracking-wider text-white mb-4">
                              Service Details
                            </h4>
                            <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                <Clock size={16} className="text-white" />
                                <span className="gilroy-text text-gray-300">Timeframe: {category.timeframe}</span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <AlertTriangle size={16} className="text-white" />
                                <span className="gilroy-text text-gray-300">Priority: {category.severity}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <Link 
                          href="/#contact"
                          className="gilroy-text-bold bg-white text-black px-8 py-3 font-bold uppercase tracking-wider relative overflow-hidden group inline-block"
                        >
                          <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Quote</span>
                          <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                          <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                            Get Quote
                          </span>
                        </Link>
                      </div>
                      
                      <div className="bg-gray-800 aspect-video flex items-center justify-center">
                        <div className="text-center">
                          <Camera size={48} className="text-gray-400 mx-auto mb-4" />
                          <p className="gilroy-text text-gray-400">
                            Service Examples
                            <br />
                            <span className="text-sm">Coming Soon</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* Our Process */}
      <section className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl font-black uppercase tracking-tight mb-8 text-black">
              Our Repair Process
            </h2>
            <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
            <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Professional repair process ensuring quality results every time
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {repairSteps.map((step, index) => (
              <motion.div
                key={step.step}
                className="bg-white p-8 shadow-lg relative"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                {/* Step Number */}
                <div className="absolute -top-4 -left-4 bg-black text-white w-12 h-12 flex items-center justify-center font-bold text-xl">
                  {step.step}
                </div>
                
                <h3 className="gilroy-text-bold text-xl font-bold uppercase tracking-wider mb-4 text-black mt-4">
                  {step.title}
                </h3>
                
                <p className="gilroy-text text-gray-600 mb-6 leading-relaxed">
                  {step.description}
                </p>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-black" />
                    <span className="gilroy-text text-sm text-gray-600">{step.duration}</span>
                  </div>
                  
                  <div>
                    <h4 className="gilroy-text-bold text-sm font-bold uppercase tracking-wider text-black mb-2">
                      Tools & Equipment:
                    </h4>
                    <ul className="space-y-1">
                      {step.tools.map((tool, toolIndex) => (
                        <li key={toolIndex} className="gilroy-text text-sm text-gray-600 flex items-center space-x-2">
                          <div className="w-2 h-2 bg-black rounded-full"></div>
                          <span>{tool}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Quality Promise */}
      <section className="py-32 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="gilroy-text-bold text-5xl md:text-6xl font-black uppercase tracking-tight mb-8 text-white">
              Why Choose JCC?
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-8"></div>
            <p className="gilroy-text text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Experience the difference of professional accident repair specialists
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="text-center">
              <Shield size={64} className="text-white mx-auto mb-6" />
              <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-4 text-white">
                Quality Guarantee
              </h3>
              <p className="gilroy-text text-gray-300 leading-relaxed">
                Every repair comes with our comprehensive guarantee. We stand behind our work with warranty coverage and ongoing support.
              </p>
            </div>
            
            <div className="text-center">
              <Clock size={64} className="text-white mx-auto mb-6" />
              <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-4 text-white">
                Insurance Approved
              </h3>
              <p className="gilroy-text text-gray-300 leading-relaxed">
                We work directly with all major insurance companies to streamline your claim process and get you back on the road faster.
              </p>
            </div>
            
            <div className="text-center">
              <CheckCircle size={64} className="text-white mx-auto mb-6" />
              <h3 className="gilroy-text-bold text-2xl font-bold uppercase tracking-wider mb-4 text-white">
                Expert Technicians
              </h3>
              <p className="gilroy-text text-gray-300 leading-relaxed">
                Our certified technicians have years of experience with all vehicle types, from classic cars to modern motorhomes.
              </p>
            </div>
          </div>

          {/* Rotating Testimonials */}
          <div className="max-w-4xl mx-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentTestimonial}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.5 }}
                className="text-center"
              >
                                <blockquote className="gilroy-text text-3xl md:text-4xl font-light text-white mb-8 leading-relaxed italic">
                  &ldquo;{testimonials[currentTestimonial].quote}&rdquo;
                </blockquote>
                <div className="space-y-2">
                  <p className="gilroy-text-bold text-xl font-bold text-white">
                    {testimonials[currentTestimonial].author}
                  </p>
                  <p className="gilroy-text text-gray-400">
                    {testimonials[currentTestimonial].project}
                  </p>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Testimonial Indicators */}
            <div className="flex justify-center space-x-3 mt-12">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentTestimonial ? 'bg-white' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="gilroy-text-bold text-4xl md:text-5xl font-black uppercase tracking-tight mb-8 text-black">
            Ready To Restore Your Vehicle?
          </h2>
          <div className="w-24 h-1 bg-black mx-auto mb-8"></div>
          <p className="gilroy-text text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Don't let accident damage hold you back. Get professional repair services that restore your vehicle to its former glory.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link 
              href="/#contact"
              className="gilroy-text-bold bg-black text-white px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">Get Repair Quote</span>
              <div className="absolute inset-0 bg-white border-2 border-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get Repair Quote
              </span>
            </Link>
            <Link 
              href="/gallery"
              className="gilroy-text-bold border-2 border-black text-black px-10 py-4 text-lg font-bold uppercase tracking-wider relative overflow-hidden group"
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">See Our Work</span>
              <div className="absolute inset-0 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                See Our Work
              </span>
            </Link>
          </div>
        </div>
      </section>

      <JCCStickyFooter />
    </div>
  );
} 