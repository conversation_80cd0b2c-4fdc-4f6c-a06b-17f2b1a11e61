"use client";
import {FC} from "react";

import Image from "next/image";

// Types
interface iCardItem {
	title: string;
	description: string;
	tag: string;
	src: string;
	link: string;
	color: string;
	textColor: string;
}

interface iCardProps extends Omit<iCardItem, "src" | "link" | "tag"> {
	src: string;
}

// Components
const Card: FC<iCardProps> = ({
	title,
	description,
	textColor,
	src,
}) => {
	return (
		<div className="h-screen flex items-center justify-center sticky top-0 md:p-0 px-4">
			<div
				className="relative flex flex-col h-[400px] w-[800px] py-12 px-10 md:px-12
				rotate-0 md:h-[500px] md:w-[900px] items-center justify-center mx-auto 
				shadow-md pr-3 pl-3 pt-3 pb-4"
			>
				<span className="font-bold relative text-5xl md:text-7xl mt-5 z-30">
					<span
						className="relative z-30 font-tiemposHeadline font-black tracking-tight"
						style={{color: textColor}}
					>
						{title}
					</span>
				</span>
				<div
					className="font-manrope text-lg md:text-2xl font-medium text-center mb-0 z-30 mt-2 lowercase tracking-wide relative"
					style={{lineHeight: 1.4, color: textColor}}
				>
					{description}
				</div>
				<div className="absolute inset-0 z-0">
					<Image
						className="w-full h-full object-cover"
						src={src}
						alt="Background"
						layout="fill"
					/>
					{/* Subtle gradient overlay for text contrast */}
					<div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40"></div>
				</div>
			</div>
		</div>
	);
};

/**
 * CardSlide component displays a series of cards in a vertical scroll layout
 * Each card contains a title, description, and decorative elements
 */
interface iCardSlideProps {
	items: iCardItem[];
}

const CardsParallax: FC<iCardSlideProps> = ({items}) => {
	return (
		<div className="min-h-screen">
			{items.map((project, i) => {
				return <Card key={`p_${i}`} {...project} />;
			})}
		</div>
	);
};

export {CardsParallax, type iCardItem};
