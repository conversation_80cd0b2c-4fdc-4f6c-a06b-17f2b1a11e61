import { NextRequest, NextResponse } from 'next/server'
import { revalidateTag, revalidatePath } from 'next/cache'

export async function POST(request: NextRequest) {
  try {
    // Check for webhook secret for external calls
    const authHeader = request.headers.get('authorization')
    const isInternalCall = !authHeader || authHeader === 'internal'
    
    if (!isInternalCall) {
      const token = authHeader?.replace('Bearer ', '')
      if (token !== process.env.WEBHOOK_SECRET) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    const body = await request.json()
    const { table, event_type, record } = body

    // Only process blog_articles table
    if (table !== 'blog_articles') {
      return NextResponse.json({ message: 'Ignored - not blog_articles table' })
    }

    console.log(`Blog cache invalidation triggered: ${event_type}`, { record })

    // Base cache tags to invalidate
    const tagsToInvalidate = [
      'blog-articles',
      'blog-published'
    ]

    // Add category-specific tags if record has tags
    if (record?.tags && Array.isArray(record.tags)) {
      record.tags.forEach((tag: string) => {
        tagsToInvalidate.push(`blog-category-${tag}`)
      })
    }

    // Add featured tag if article is featured
    if (record?.tags?.includes('featured')) {
      tagsToInvalidate.push('blog-featured')
    }

    // Add slug-specific tag for individual article pages
    if (record?.slug) {
      tagsToInvalidate.push(`blog-article-${record.slug}`)
    }

    // Invalidate cache tags
    tagsToInvalidate.forEach(tag => {
      revalidateTag(tag)
    })

    // Revalidate related paths
    const pathsToRevalidate = [
      '/blog',
      '/api/blog'
    ]

    // Add individual article path if slug exists
    if (record?.slug) {
      pathsToRevalidate.push(`/blog/${record.slug}`)
    }

    pathsToRevalidate.forEach(path => {
      revalidatePath(path)
    })

    console.log('Blog cache invalidated successfully', {
      tags: tagsToInvalidate,
      paths: pathsToRevalidate,
      event_type,
      slug: record?.slug
    })

    return NextResponse.json({
      success: true,
      message: 'Blog cache invalidated successfully',
      invalidated_tags: tagsToInvalidate,
      revalidated_paths: pathsToRevalidate,
      event_type,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Blog cache invalidation failed:', error)
    return NextResponse.json(
      { error: 'Failed to invalidate blog cache', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 