'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, Home, RefreshCw, Settings, Shield } from 'lucide-react'
import Link from 'next/link'

interface AdminErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function AdminError({ error, reset }: AdminErrorProps) {
  useEffect(() => {
    // Log the error for debugging
    console.error('Admin error caught:', error)
    
    // You can integrate with error reporting services here
    // Sentry.captureException(error, { tags: { section: 'admin' } })
  }, [error])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-2xl w-full">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl text-gray-900 gilroy-text-bold">
              Admin Panel Error
            </CardTitle>
            <p className="text-gray-600 mt-2 gilroy-text">
              Something went wrong in the admin panel. This error has been logged for investigation.
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Error Details (development only) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-gray-100 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-2 gilroy-text-medium">
                  Error Details:
                </h4>
                <p className="text-sm text-gray-700 font-mono break-all">
                  {error.message}
                </p>
                {error.digest && (
                  <p className="text-xs text-gray-500 mt-2">
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}

            {/* Recovery Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={reset}
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 gilroy-text-medium"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>

              <Link href="/admin">
                <Button
                  variant="outline"
                  className="w-full h-12 gilroy-text-medium"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Admin Dashboard
                </Button>
              </Link>
            </div>

            {/* Additional Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link href="/">
                <Button
                  variant="outline"
                  className="w-full h-12 gilroy-text-medium"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Main Website
                </Button>
              </Link>

              <Link href="/admin/login">
                <Button
                  variant="outline"
                  className="w-full h-12 gilroy-text-medium"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Re-login
                </Button>
              </Link>
            </div>

            {/* Common Issues */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2 gilroy-text-medium">
                Common Solutions:
              </h4>
              <ul className="text-sm text-blue-700 space-y-1 gilroy-text">
                <li>• Try refreshing the page</li>
                <li>• Check your internet connection</li>
                <li>• Clear your browser cache</li>
                <li>• Log out and log back in</li>
                <li>• Contact support if the issue persists</li>
              </ul>
            </div>

            {/* Support Contact */}
            <div className="text-center pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-500 gilroy-text">
                Need technical support?{' '}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-blue-600 hover:text-blue-800"
                >
                  Contact our team
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 