'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Calendar, Clock, User, ArrowLeft, ArrowRight, Loader2, Share2 } from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '@/components/header';
import JCCStickyFooter from '@/components/footer';

// Blog post interface
interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  image: string;
  heroImage?: string;
  category: string;
  tags: string[];
  author: string;
  date: string;
  readTime: string;
  featured: boolean;
  seoTitle?: string;
  seoDescription?: string;
  createdAt: string;
  updatedAt: string;
}

export default function BlogPostPage() {
  const params = useParams();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        setError(null);

        const slug = params.slug as string;

        // Fetch the specific article
        const response = await fetch(`/api/blog/${slug}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Article not found');
          }
          throw new Error('Failed to fetch article');
        }

        const data = await response.json();
        setPost(data.article);

        // Fetch related articles from the same category
        if (data.article?.category) {
          const relatedResponse = await fetch(`/api/blog?category=${data.article.category.toLowerCase()}&limit=3`);
          if (relatedResponse.ok) {
            const relatedData = await relatedResponse.json();
            const filtered = relatedData.articles?.filter((article: BlogPost) => article.slug !== slug) || [];
            setRelatedPosts(filtered.slice(0, 3));
          }
        }

      } catch (err) {
        console.error('Error fetching article:', err);
        setError(err instanceof Error ? err.message : 'Failed to load article');
      } finally {
        setLoading(false);
      }
    };

    if (params.slug) {
      fetchArticle();
    }
  }, [params.slug]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const shareArticle = () => {
    if (navigator.share && post) {
      navigator.share({
        title: post.title,
        text: post.excerpt,
        url: window.location.href,
      });
    } else if (post) {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Article URL copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white text-black">
        <Header onContactClick={() => scrollToSection('contact')} forceVisible={true} />
        <div className="pt-24 md:pt-32 pb-16 flex items-center justify-center min-h-[50vh]">
          <div className="flex items-center">
            <Loader2 className="h-8 w-8 animate-spin text-gray-600 mr-3" />
            <span className="text-gray-600" style={{ fontFamily: 'Gilroy, sans-serif' }}>
              Loading article...
            </span>
          </div>
        </div>
        <JCCStickyFooter />
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-white text-black">
        <Header onContactClick={() => scrollToSection('contact')} forceVisible={true} />
        <div className="pt-24 md:pt-32 pb-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl font-black text-gray-900 mb-4" style={{ fontFamily: 'Gilroy, sans-serif' }}>
              {error === 'Article not found' ? 'Article Not Found' : 'Error'}
            </h1>
            <p className="text-gray-600 mb-8" style={{ fontFamily: 'Gilroy, sans-serif' }}>
              {error === 'Article not found'
                ? "The article you're looking for doesn't exist or has been removed."
                : error || 'An unexpected error occurred while loading the article.'
              }
            </p>
            <div className="space-x-4">
              <Link
                href="/blog"
                className="inline-block bg-black text-white px-6 py-3 font-bold hover:bg-gray-800 transition-colors"
                style={{ fontFamily: 'Gilroy, sans-serif' }}
              >
                Back to Blog
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="inline-block bg-gray-200 text-black px-6 py-3 font-bold hover:bg-gray-300 transition-colors"
                style={{ fontFamily: 'Gilroy, sans-serif' }}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
        <JCCStickyFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Header */}
      <Header onContactClick={() => scrollToSection('contact')} forceVisible={true} />

      {/* Article Header */}
      <article className="pt-24 md:pt-32">
        {/* Back Button */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
          <Link
            href="/blog"
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-black transition-colors duration-200"
            style={{ fontFamily: 'Gilroy, sans-serif' }}
          >
            <ArrowLeft size={20} />
            <span>Back to Blog</span>
          </Link>
        </div>

        {/* Hero Image */}
        <div className="relative w-full h-64 md:h-80 lg:h-96 bg-gray-100 mb-8">
          <Image
            src={post.heroImage || post.image}
            alt={post.title}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-20"></div>
          <div className="absolute bottom-6 left-6">
            <span className="bg-black text-white px-4 py-2 text-sm font-bold uppercase tracking-wider" style={{ fontFamily: 'Gilroy, sans-serif' }}>
              {post.category}
            </span>
          </div>
        </div>

        {/* Article Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Article Meta */}
          <div className="mb-8">
            <div className="flex flex-wrap items-center gap-4 text-gray-600 text-sm mb-6">
              <div className="flex items-center space-x-2">
                <Calendar size={16} />
                <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{post.date}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock size={16} />
                <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{post.readTime}</span>
              </div>
              <div className="flex items-center space-x-2">
                <User size={16} />
                <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{post.author}</span>
              </div>
              <button
                onClick={shareArticle}
                className="flex items-center space-x-2 hover:text-black transition-colors"
                style={{ fontFamily: 'Gilroy, sans-serif' }}
              >
                <Share2 size={16} />
                <span>Share</span>
              </button>
            </div>

            <motion.h1
              className="text-3xl md:text-4xl lg:text-5xl font-black text-black mb-6 leading-tight"
              style={{ fontFamily: 'Gilroy, sans-serif' }}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {post.title}
            </motion.h1>

            {post.excerpt && (
              <motion.p
                className="text-xl text-gray-700 leading-relaxed"
                style={{ fontFamily: 'Gilroy, sans-serif' }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                {post.excerpt}
              </motion.p>
            )}
          </div>

          {/* Article Body */}
          <motion.div
            className="blog-content"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            dangerouslySetInnerHTML={{ __html: post.content }}
          />

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="mt-12 pt-12 pb-16 border-t border-gray-200">
              <h3 className="text-sm font-bold text-gray-600 mb-6 uppercase tracking-wider" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                Tags
              </h3>
              <div className="flex flex-wrap gap-3 mb-8">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 text-gray-700 px-4 py-2 text-sm font-medium"
                    style={{ fontFamily: 'Gilroy, sans-serif' }}
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

        </div>

        {/* Related Articles */}
        {relatedPosts.length > 0 && (
          <section className="py-20 md:py-24 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.h2
                className="text-3xl md:text-4xl font-black text-black mb-16 text-center"
                style={{ fontFamily: 'Gilroy, sans-serif' }}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                RELATED ARTICLES
              </motion.h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {relatedPosts.map((relatedPost, index) => (
                  <motion.article
                    key={relatedPost.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 group"
                  >
                    <div className="relative overflow-hidden">
                      <Image
                        src={relatedPost.image}
                        alt={relatedPost.title}
                        width={400}
                        height={250}
                        className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-black text-white px-3 py-1 text-xs font-bold uppercase tracking-wider" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                          {relatedPost.category}
                        </span>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="flex items-center mb-3 text-gray-600 text-sm">
                        <Calendar size={14} className="mr-2" />
                        <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{relatedPost.date}</span>
                        <Clock size={14} className="ml-4 mr-2" />
                        <span style={{ fontFamily: 'Gilroy, sans-serif' }}>{relatedPost.readTime}</span>
                      </div>

                      <h3 className="text-xl font-black text-black mb-3 leading-tight" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                        {relatedPost.title}
                      </h3>

                      <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3" style={{ fontFamily: 'Gilroy, sans-serif' }}>
                        {relatedPost.excerpt}
                      </p>

                      <Link
                        href={`/blog/${relatedPost.slug}`}
                        className="inline-flex items-center text-black font-bold hover:text-gray-700 transition-colors duration-200"
                        style={{ fontFamily: 'Gilroy, sans-serif' }}
                      >
                        Read More
                        <ArrowRight size={14} className="ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                      </Link>
                    </div>
                  </motion.article>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Footer */}
        <JCCStickyFooter />
      </article>
    </div>
  );
} 