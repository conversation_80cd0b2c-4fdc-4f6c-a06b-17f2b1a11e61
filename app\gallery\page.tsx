'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, Play, X, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '@/components/header';
import JCCStickyFooter from '@/components/footer';
import ResponsiveVideo from '@/components/ui/responsive-video';
import BeforeAfterSlider from '@/components/ui/before-after-slider';

// Gallery item interface
interface GalleryItem {
  id: string;
  title: string;
  description: string;
  category: string;
  type: 'image' | 'video' | 'before-after';
  src: string;
  thumbnail?: string;
  tags: string[];
  beforeImage?: {
    id: string;
    url: string;
    alt: string;
    title: string;
  };
  afterImage?: {
    id: string;
    url: string;
    alt: string;
    title: string;
  };
  vehicleDetails?: Record<string, any>;
  workDetails?: Record<string, any>;
  featured?: boolean;
  isFeatured?: boolean;
  alt_text?: string;
  created_at?: string;
  order_index?: number;
  album_id?: string;
}

// Album interface
interface Album {
  id: string;
  title: string;
  description: string;
  slug: string;
  cover_image_url?: string;
  cover_image_alt?: string;
  is_featured: boolean;
  is_published: boolean;
  order_index: number;
  created_at: string;
  updated_at: string;
  images?: GalleryItem[];
}

// Gallery categories
const categories = [
  { id: 'all', label: 'All Work', description: 'Everything we do' },
  { id: 'before-after', label: 'Before & After', description: 'Transformation showcases' },
  { id: 'accident-repairs', label: 'Accident Repairs', description: 'Collision damage fixes' },
  { id: 'resprays', label: 'Full Resprays', description: 'Complete paint jobs' },
  { id: 'rust-treatment', label: 'Rust Treatment', description: 'Rust removal & prevention' },
  { id: 'raptor-paint', label: 'Raptor Paint', description: 'Protective coatings' },
  { id: 'restoration', label: 'Restorations', description: 'Full vehicle makeovers' },
  { id: 'custom-work', label: 'Custom Work', description: 'Bespoke projects' }
];

// Gallery data fetching function (excludes album images)
const fetchGalleryImagesExcludingAlbums = async (category: string = 'all', search: string = ''): Promise<GalleryItem[]> => {
  try {
    const params = new URLSearchParams()
    if (category !== 'all' && category !== 'before-after') params.append('category', category)
    if (search) params.append('search', search)
    params.append('exclude_albums', 'true') // Exclude images that are in albums

    let allItems: GalleryItem[] = []

    // Fetch regular gallery images (unless only viewing before/after)
    if (category !== 'before-after') {
      const galleryResponse = await fetch(`/api/gallery?${params.toString()}`)
      if (galleryResponse.ok) {
        const galleryData = await galleryResponse.json()
        allItems = galleryData.images || []
      }
    }

    // Fetch before/after pairs (unless explicitly excluded)
    if (category === 'all' || category === 'before-after') {
      const beforeAfterParams = new URLSearchParams()
      if (category === 'before-after') beforeAfterParams.append('category', 'before-after')
      if (search) beforeAfterParams.append('search', search)

      const beforeAfterResponse = await fetch(`/api/gallery/before-after?${beforeAfterParams.toString()}`)
      if (beforeAfterResponse.ok) {
        const beforeAfterData = await beforeAfterResponse.json()
        const beforeAfterItems: GalleryItem[] = beforeAfterData.map((pair: any) => ({
          id: pair.id,
          title: pair.title,
          description: pair.description,
          category: pair.category,
          type: 'before-after' as const,
          src: pair.afterImage.url, // Use after image as main src for thumbnails
          tags: pair.tags,
          beforeImage: pair.beforeImage,
          afterImage: pair.afterImage,
          vehicleDetails: pair.vehicleDetails,
          workDetails: pair.workDetails,
          isFeatured: pair.isFeatured,
          order_index: pair.orderIndex,
          created_at: pair.createdAt
        }))
        allItems = [...allItems, ...beforeAfterItems]
      }
    }

    // Sort by featured status and order
    return allItems.sort((a, b) => {
      if (a.isFeatured && !b.isFeatured) return -1
      if (!a.isFeatured && b.isFeatured) return 1
      return (a.order_index || 0) - (b.order_index || 0)
    })
  } catch (error) {
    console.error('Error fetching gallery images:', error)
    return []
  }
}

// Gallery data fetching function (includes all images)
const fetchGalleryImages = async (category: string = 'all', search: string = ''): Promise<GalleryItem[]> => {
  try {
    const params = new URLSearchParams()
    if (category !== 'all' && category !== 'before-after') params.append('category', category)
    if (search) params.append('search', search)

    let allItems: GalleryItem[] = []

    // Fetch regular gallery images (unless only viewing before/after)
    if (category !== 'before-after') {
      const galleryResponse = await fetch(`/api/gallery?${params.toString()}`)
      if (galleryResponse.ok) {
        const galleryData = await galleryResponse.json()
        allItems = galleryData.images || []
      }
    }

    // Fetch before/after pairs (unless explicitly excluded)
    if (category === 'all' || category === 'before-after') {
      const beforeAfterParams = new URLSearchParams()
      if (category === 'before-after') beforeAfterParams.append('category', 'before-after')
      if (search) beforeAfterParams.append('search', search)

      const beforeAfterResponse = await fetch(`/api/gallery/before-after?${beforeAfterParams.toString()}`)
      if (beforeAfterResponse.ok) {
        const beforeAfterData = await beforeAfterResponse.json()
        const beforeAfterItems: GalleryItem[] = beforeAfterData.map((pair: any) => ({
          id: pair.id,
          title: pair.title,
          description: pair.description,
          category: pair.category,
          type: 'before-after' as const,
          src: pair.afterImage.url, // Use after image as main src for thumbnails
          tags: pair.tags,
          beforeImage: pair.beforeImage,
          afterImage: pair.afterImage,
          vehicleDetails: pair.vehicleDetails,
          workDetails: pair.workDetails,
          isFeatured: pair.isFeatured,
          order_index: pair.orderIndex,
          created_at: pair.createdAt
        }))
        allItems = [...allItems, ...beforeAfterItems]
      }
    }

    // Sort by featured status and order
    return allItems.sort((a, b) => {
      if (a.isFeatured && !b.isFeatured) return -1
      if (!a.isFeatured && b.isFeatured) return 1
      return (a.order_index || 0) - (b.order_index || 0)
    })
  } catch (error) {
    console.error('Error fetching gallery images:', error)
    return []
  }
}

// Lightbox component for full-screen viewing
const Lightbox = ({
  item,
  isOpen,
  onClose,
  onPrevious,
  onNext,
  showNavigation = true
}: {
  item: GalleryItem | null;
  isOpen: boolean;
  onClose: () => void;
  onPrevious: () => void;
  onNext: () => void;
  showNavigation?: boolean;
}) => {
  const [dragDirection, setDragDirection] = useState<'left' | 'right' | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isSliderDragging, setIsSliderDragging] = useState(false);

  // Keyboard controls
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen || isTransitioning) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleNext();
          break;
        case ' ':
          e.preventDefault();
          // Space bar reserved for future use
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.classList.remove('overflow-hidden');
    };
  }, [isOpen, isTransitioning, item]);

  // Handle navigation with transition effects
  const handlePrevious = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setDragDirection('right');
    setTimeout(() => {
      onPrevious();
      setDragDirection(null);
      setIsTransitioning(false);
    }, 150);
  };

  const handleNext = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setDragDirection('left');
    setTimeout(() => {
      onNext();
      setDragDirection(null);
      setIsTransitioning(false);
    }, 150);
  };

  // Handle drag gestures
  const handleDragEnd = (event: any, info: any) => {
    if (isTransitioning) return;

    const threshold = 100;
    const velocity = Math.abs(info.velocity.x);

    if (Math.abs(info.offset.x) > threshold || velocity > 500) {
      if (info.offset.x > 0) {
        handlePrevious();
      } else {
        handleNext();
      }
    }
  };

  if (!item) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-black/95 backdrop-blur-sm"
          onClick={onClose}
        >
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-60 bg-black/80 hover:bg-black text-white hover:text-gray-300 transition-colors p-3 rounded-full touch-manipulation backdrop-blur-sm"
            style={{ minWidth: '48px', minHeight: '48px' }}
            aria-label="Close lightbox"
          >
            <X size={20} />
          </button>

          {/* Navigation Buttons */}
          {showNavigation && (
            <>
              <motion.button
                onClick={(e) => {
                  e.stopPropagation();
                  handlePrevious();
                }}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-60 bg-black/80 hover:bg-black text-white hover:text-gray-300 transition-colors p-3 rounded-full touch-manipulation backdrop-blur-sm"
                style={{ minWidth: '48px', minHeight: '48px' }}
                aria-label="Previous image"
                whileHover={{ scale: 1.1, x: -2 }}
                whileTap={{ scale: 0.95 }}
                disabled={isTransitioning}
              >
                <ArrowLeft size={20} />
              </motion.button>
              <motion.button
                onClick={(e) => {
                  e.stopPropagation();
                  handleNext();
                }}
                className="absolute right-16 md:right-4 top-1/2 -translate-y-1/2 z-60 bg-black/80 hover:bg-black text-white hover:text-gray-300 transition-colors p-3 rounded-full touch-manipulation backdrop-blur-sm"
                style={{ minWidth: '48px', minHeight: '48px' }}
                aria-label="Next image"
                whileHover={{ scale: 1.1, x: 2 }}
                whileTap={{ scale: 0.95 }}
                disabled={isTransitioning}
              >
                <ArrowLeft size={20} className="rotate-180" />
              </motion.button>
            </>
          )}

          {/* Content */}
          <div className="flex flex-col items-center justify-center min-h-screen p-4 md:p-8">
            <motion.div
              key={item.id} // Important: key prop for proper animations
              initial={{
                scale: 0.8,
                opacity: 0,
                x: dragDirection === 'left' ? 100 : dragDirection === 'right' ? -100 : 0
              }}
              animate={{
                scale: 1,
                opacity: 1,
                x: 0
              }}
              exit={{
                scale: 0.8,
                opacity: 0,
                x: dragDirection === 'left' ? -100 : dragDirection === 'right' ? 100 : 0
              }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                opacity: { duration: 0.2 }
              }}
              drag={!isSliderDragging ? "x" : false}
              dragConstraints={{ left: 0, right: 0 }}
              dragElastic={0.2}
              onDragEnd={handleDragEnd}
              className="max-w-7xl w-full flex flex-col items-center cursor-grab active:cursor-grabbing"
              onClick={(e) => e.stopPropagation()}
              whileDrag={{ scale: 0.95 }}
            >
              {/* Media Content */}
              <div className="relative mb-4 md:mb-6 w-full flex justify-center">
                {item.type === 'video' ? (
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.1, duration: 0.3 }}
                  >
                    <ResponsiveVideo
                      src={item.src}
                      aspectRatio="video"
                      maxHeight="75vh"
                      className="rounded-lg"
                      muted={false}
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    className="relative"
                    initial={{ scale: 0.9, opacity: 0, rotateY: dragDirection === 'left' ? -15 : dragDirection === 'right' ? 15 : 0 }}
                    animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                    transition={{
                      delay: 0.1,
                      duration: 0.4,
                      type: "spring",
                      stiffness: 200,
                      damping: 20
                    }}
                  >
                    {/* Before/After Drag-to-Reveal Interface */}
                    {item.beforeImage && item.afterImage ? (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="w-[90vw] max-w-[90vw]">
                          <BeforeAfterSlider
                            beforeImage={item.beforeImage.url}
                            afterImage={item.afterImage.url}
                            title={item.title}
                            beforeAlt={item.beforeImage.alt}
                            afterAlt={item.afterImage.alt}
                            className="max-h-[75vh] rounded-lg w-full"
                            onDragStart={() => setIsSliderDragging(true)}
                            onDragEnd={() => setIsSliderDragging(false)}
                            showTitle={false}
                          />
                        </div>
                      </motion.div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Image
                          src={item.src}
                          alt={item.title}
                          width={1200}
                          height={800}
                          className="w-full h-auto max-h-[75vh] object-contain rounded-lg select-none pointer-events-none"
                          draggable={false}
                          onContextMenu={(e) => e.preventDefault()}
                        />
                      </motion.div>
                    )}
                  </motion.div>
                )}
              </div>

              {/* Item Details */}
              <motion.div
                className="text-center text-white max-w-4xl"
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <motion.h3
                  className="gilroy-text-bold text-xl md:text-2xl lg:text-3xl font-black uppercase tracking-tight mb-2 md:mb-4"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.3 }}
                >
                  {item.title}
                </motion.h3>
                <motion.p
                  className="gilroy-text text-base md:text-lg text-gray-300 leading-relaxed"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4, duration: 0.3 }}
                >
                  {item.description}
                </motion.p>
              </motion.div>

              {/* Keyboard Hints */}
              <motion.div
                className="mt-4 text-center"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.3 }}
              >
                <p className="text-xs text-gray-400 uppercase tracking-wider gilroy-text">
                  Swipe or use ← → arrows to navigate • ESC to close
                  {item.beforeImage && item.afterImage && ' • Drag to compare before/after'}
                </p>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Gallery grid item component
const GalleryGridItem = ({
  item,
  onClick,
  index
}: {
  item: GalleryItem;
  onClick: () => void;
  index: number;
}) => {
  const [isDraggingSlider, setIsDraggingSlider] = useState(false);

  const handleClick = () => {
    if (isDraggingSlider) return; // Prevent click when dragging slider
    onClick();
  };
  return (
    <motion.div
      initial={{ opacity: 0, y: 40, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: index * 0.08,
        type: "spring",
        stiffness: 180,
        damping: 25
      }}
      whileHover={{
        y: -8,
        scale: 1.02,
        transition: { duration: 0.4, type: "spring", stiffness: 220, damping: 28 }
      }}
      whileTap={{ scale: 0.98 }}
      className="group relative overflow-hidden rounded-xl bg-black cursor-pointer touch-manipulation shadow-2xl hover:shadow-3xl transition-all duration-700 ease-out border border-gray-100/20"
      onClick={handleClick}
    >


      {/* Media Content */}
      <div className="relative aspect-[3/2] overflow-hidden">
        {item.type === 'video' ? (
          <>
            <div className="relative w-full h-full overflow-hidden">
              <Image
                src={item.thumbnail || item.src}
                alt={item.title}
                fill
                className="object-cover select-none pointer-events-none transition-all duration-700 ease-out group-hover:scale-110 group-hover:brightness-110"
                draggable={false}
                onContextMenu={(e) => e.preventDefault()}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority={index < 6}
              />
            </div>
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-700 flex items-center justify-center">
              <motion.div
                className="bg-white/25 backdrop-blur-md rounded-full p-4 lg:p-5 group-hover:bg-white/35 transition-all duration-500 ease-out shadow-2xl border border-white/30"
                whileHover={{ scale: 1.15, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Play size={24} className="text-white ml-1 drop-shadow-lg" />
              </motion.div>
            </div>
          </>
        ) : item.type === 'before-after' && item.beforeImage && item.afterImage ? (
          <div className="relative w-full h-full">
            <BeforeAfterSlider
              beforeImage={item.beforeImage.url}
              afterImage={item.afterImage.url}
              title=""
              beforeAlt={item.beforeImage.alt}
              afterAlt={item.afterImage.alt}
              className="h-full border-0 shadow-none bg-transparent rounded-xl overflow-hidden"
              showTitle={false}
              onDragStart={() => setIsDraggingSlider(true)}
              onDragEnd={() => setTimeout(() => setIsDraggingSlider(false), 100)} // Small delay to prevent immediate click
            />
          </div>
        ) : (
          <div className="relative w-full h-full overflow-hidden">
            <Image
              src={item.src}
              alt={item.title}
              fill
              className="object-cover select-none pointer-events-none transition-all duration-700 ease-out group-hover:scale-110 group-hover:brightness-110"
              draggable={false}
              onContextMenu={(e) => e.preventDefault()}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={index < 6}
            />
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out" />

        {/* Shimmer effect on hover */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out" />
        </div>
      </div>

      {/* Content Overlay */}
      <div className="absolute inset-0 flex flex-col justify-end p-6 lg:p-8 text-white opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out transform translate-y-4 group-hover:translate-y-0">
        <motion.h3
          className="gilroy-text-bold text-xl lg:text-2xl font-black uppercase tracking-tight mb-3 leading-tight"
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          {item.title}
        </motion.h3>
        <motion.p
          className="gilroy-text text-sm lg:text-base text-white mb-4 line-clamp-3 leading-relaxed"
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {item.description}
        </motion.p>


      </div>


    </motion.div>
  );
};

export default function GalleryPage() {
  const [selectedItem, setSelectedItem] = useState<GalleryItem | null>(null);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [albums, setAlbums] = useState<Album[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch gallery images and albums
  useEffect(() => {
    const loadGalleryData = async () => {
      setIsLoading(true);
      setError('');
      try {
        // Load both gallery images and albums in parallel
        const [albumsResponse] = await Promise.all([
          fetch('/api/gallery/albums?published_only=true')
        ]);

        let albumsData = [];
        if (albumsResponse.ok) {
          const response = await albumsResponse.json();
          albumsData = response.albums || [];
          setAlbums(albumsData);
        }

        // Fetch regular gallery images, excluding those that are already in albums
        const images = await fetchGalleryImagesExcludingAlbums('all', '');
        setGalleryItems(images);

      } catch (err) {
        setError('Failed to load gallery content. Please try again later.');
        console.error('Error loading gallery:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadGalleryData();
  }, []);

  // Use all gallery items since we removed filtering
  const filteredItems = galleryItems;

  // Lightbox navigation
  const currentIndex = selectedItem ? filteredItems.findIndex(item => item.id === selectedItem.id) : -1;

  const handlePrevious = () => {
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : filteredItems.length - 1;
    setSelectedItem(filteredItems[prevIndex]);
  };

  const handleNext = () => {
    const nextIndex = currentIndex < filteredItems.length - 1 ? currentIndex + 1 : 0;
    setSelectedItem(filteredItems[nextIndex]);
  };

  const openLightbox = (item: GalleryItem) => {
    setSelectedItem(item);
    setIsLightboxOpen(true);
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
    setSelectedItem(null);
  };

  return (
    <div className="min-h-screen bg-white text-black gallery-page" style={{ userSelect: 'none', WebkitUserSelect: 'none', MozUserSelect: 'none', msUserSelect: 'none' }}>
      <style jsx global>{`
        .gallery-page img {
          -webkit-user-drag: none;
          -khtml-user-drag: none;
          -moz-user-drag: none;
          -o-user-drag: none;
          user-drag: none;
          pointer-events: none;
          -webkit-touch-callout: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }
        .gallery-text {
          user-select: text !important;
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
        }
        .gallery-interactive {
          pointer-events: auto !important;
          user-select: auto !important;
          -webkit-user-select: auto !important;
        }
        .gallery-page::selection {
          background: transparent;
        }
        .gallery-page::-moz-selection {
          background: transparent;
        }
        
        /* Enhanced shadows */
        .shadow-3xl {
          box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
        }
        
        /* Improved hover shadows */
        .hover\:shadow-3xl:hover {
          box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
        }
        
        /* Smooth image loading */
        .gallery-page img {
          transition: opacity 0.3s ease-out, filter 0.3s ease-out;
        }
        
        /* Enhanced grid animations */
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        
        .gallery-item {
          animation: slideInUp 0.6s ease-out forwards;
        }
        
        /* Loading dots animation */
        @keyframes bounce {
          0%, 80%, 100% {
            transform: scale(0);
          }
          40% {
            transform: scale(1);
          }
        }
        
        .animate-bounce {
          animation: bounce 1.4s infinite ease-in-out both;
        }
        
        /* Enhanced focus states */
        .gallery-page button:focus {
          outline: 2px solid #000;
          outline-offset: 2px;
        }
        
        .gallery-page input:focus {
          outline: 2px solid #000;
          outline-offset: 2px;
        }
        
        /* Smooth category transitions */
        .category-button {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Enhanced backdrop blur */
        .backdrop-blur-enhanced {
          backdrop-filter: blur(20px) saturate(180%);
          -webkit-backdrop-filter: blur(20px) saturate(180%);
        }
      `}</style>
      {/* Header */}
      <Header forceVisible={true} />

      {/* Hero Section */}
      <section className="relative pt-20 md:pt-24 lg:pt-28 pb-16 md:pb-20 bg-black text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_100%)]"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <div className="mb-8 md:mb-12">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-gray-400 hover:text-white transition-colors">
                Home
              </Link>
              <span className="text-gray-500">/</span>
              <span className="text-white">Gallery</span>
            </nav>
          </div>

          {/* Header Content */}
          <div className="text-center">
            <motion.h1
              className="gilroy-text-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-black uppercase tracking-tight mb-6 md:mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              Our Work
            </motion.h1>

            <motion.div
              className="w-20 md:w-24 h-1 bg-white mx-auto mb-6 md:mb-8"
              initial={{ opacity: 0, scaleX: 0 }}
              animate={{ opacity: 1, scaleX: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            />

            <motion.p
              className="gilroy-text text-lg md:text-xl lg:text-2xl text-white max-w-3xl mx-auto leading-relaxed mb-8 md:mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Quality craftsmanship from accident repairs to full restorations.
            </motion.p>

            {/* Stats */}
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <div className="text-center">
                <div className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">
                  500+
                </div>
                <div className="gilroy-text text-sm md:text-base text-white uppercase tracking-wider">
                  Projects Completed
                </div>
              </div>
              <div className="text-center">
                <div className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">
                  15+
                </div>
                <div className="gilroy-text text-sm md:text-base text-white uppercase tracking-wider">
                  Years Experience
                </div>
              </div>
              <div className="text-center">
                <div className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">
                  100%
                </div>
                <div className="gilroy-text text-sm md:text-base text-white uppercase tracking-wider">
                  Customer Satisfaction
                </div>
              </div>
              <div className="text-center">
                <div className="gilroy-text-bold text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">
                  Award
                </div>
                <div className="gilroy-text text-sm md:text-base text-white uppercase tracking-wider">
                  Winning Team
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Main Gallery Section */}
      <section className="bg-white py-16 md:py-20 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Loading State */}
          {isLoading && (
            <motion.div
              className="text-center py-20 md:py-24"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Loader2 className="h-16 w-16 text-black mx-auto animate-spin mb-6" />
              <h3 className="gilroy-text-bold text-2xl font-black text-black mb-3 uppercase tracking-tight">
                Loading Gallery...
              </h3>
              <p className="gilroy-text text-black text-lg">
                Fetching our latest work showcase
              </p>
            </motion.div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-16 md:py-20">
              <div className="mb-6">
                <X className="h-12 w-12 text-black mx-auto" />
              </div>
              <h3 className="gilroy-text-bold text-xl font-bold text-black mb-2">
                Oops! Something went wrong
              </h3>
              <p className="gilroy-text text-black mb-6">
                {error}
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-black text-white px-6 py-3 font-bold uppercase tracking-wider hover:bg-white hover:text-black border border-black transition-colors gilroy-text-bold"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Albums Section */}
          {!isLoading && !error && albums.length > 0 && (
            <>
              {/* Albums Header */}
              <div className="mb-12 md:mb-16">
                <motion.h2
                  className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl font-black uppercase tracking-tight text-black mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  Albums
                </motion.h2>
                <motion.p
                  className="gilroy-text text-lg text-black max-w-2xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  Browse our curated collections of work organised by project type and category.
                </motion.p>
              </div>

              {/* Albums Grid */}
              <div className="grid gap-8 md:gap-10 lg:gap-12 grid-cols-1 md:grid-cols-2 mb-16 md:mb-20">
                {albums.map((album, index) => (
                  <motion.div
                    key={album.id}
                    initial={{ opacity: 0, y: 40, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.6,
                      delay: index * 0.08,
                      type: "spring",
                      stiffness: 180,
                      damping: 25
                    }}
                    whileHover={{
                      y: -8,
                      scale: 1.02,
                      transition: { duration: 0.4, type: "spring", stiffness: 220, damping: 28 }
                    }}
                    whileTap={{ scale: 0.98 }}
                    className="group relative overflow-hidden rounded-xl bg-black cursor-pointer touch-manipulation shadow-2xl hover:shadow-3xl transition-all duration-700 ease-out border border-gray-100/20"
                  >
                    <Link href={`/gallery/albums/${album.slug}`}>
                      {/* Album Cover */}
                      <div className="relative aspect-[3/2] overflow-hidden">
                        {album.cover_image_url ? (
                          <Image
                            src={album.cover_image_url}
                            alt={album.cover_image_alt || album.title}
                            fill
                            className="object-cover select-none pointer-events-none transition-all duration-700 ease-out group-hover:scale-110 group-hover:brightness-110"
                            draggable={false}
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            priority={index < 6}
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                            <span className="gilroy-text-bold text-white text-6xl font-black uppercase">
                              {album.title.charAt(0)}
                            </span>
                          </div>
                        )}



                        {/* Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out" />
                      </div>

                      {/* Album Details */}
                      <div className="absolute inset-0 flex flex-col justify-end p-6 lg:p-8 text-white opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out transform translate-y-4 group-hover:translate-y-0">
                        <h3 className="gilroy-text-bold text-xl lg:text-2xl font-black uppercase tracking-tight mb-3 leading-tight">
                          {album.title}
                        </h3>
                        {album.description && (
                          <p className="gilroy-text text-sm lg:text-base text-white mb-4 line-clamp-3 leading-relaxed">
                            {album.description}
                          </p>
                        )}

                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </>
          )}

          {/* Separator between Albums and Individual Photos */}
          {!isLoading && !error && albums.length > 0 && filteredItems.length > 0 && (
            <motion.div
              className="relative mb-16 md:mb-20"
              initial={{ opacity: 0, scaleX: 0 }}
              animate={{ opacity: 1, scaleX: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="flex items-center">
                <div className="flex-1 h-px bg-gradient-to-r from-transparent via-black to-black"></div>
                <div className="px-6">
                  <span className="gilroy-text-bold text-black font-bold uppercase tracking-wider text-sm">
                    Individual Photos
                  </span>
                </div>
                <div className="flex-1 h-px bg-gradient-to-l from-transparent via-black to-black"></div>
              </div>
            </motion.div>
          )}

          {/* Individual Photos Section */}
          {!isLoading && !error && filteredItems.length > 0 && (
            <>
              {/* Only show header if there are albums above */}
              {albums.length > 0 && (
                <div className="mb-12 md:mb-16">
                  <motion.h2
                    className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl font-black uppercase tracking-tight text-black mb-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    Latest Work
                  </motion.h2>
                  <motion.p
                    className="gilroy-text text-lg text-black max-w-2xl"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                  >
                    Our most recent projects and individual showcase pieces not yet organised into albums.
                  </motion.p>
                </div>
              )}

              {/* Gallery Grid */}
              <div className="grid gap-8 md:gap-10 lg:gap-12 grid-cols-1 md:grid-cols-2">
                {filteredItems.map((item: GalleryItem, index: number) => (
                  <GalleryGridItem
                    key={item.id}
                    item={item}
                    index={index + albums.length} // Offset animation delay to account for albums
                    onClick={() => openLightbox(item)}
                  />
                ))}
              </div>
            </>
          )}

          {/* Empty State - Only show if both albums and gallery items are empty */}
          {!isLoading && !error && albums.length === 0 && filteredItems.length === 0 && (
            <div className="text-center py-16 md:py-20">
              <h3 className="gilroy-text-bold text-2xl md:text-3xl font-black uppercase tracking-tight mb-4 text-black">
                Gallery Coming Soon
              </h3>
              <p className="gilroy-text text-black mb-8 max-w-md mx-auto">
                We're preparing our gallery with amazing examples of our work. Check back soon!
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 md:py-20 bg-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="gilroy-text-bold text-3xl md:text-4xl lg:text-5xl font-black uppercase tracking-tight mb-6 md:mb-8">
            Like What You See?
          </h2>
          <p className="gilroy-text text-lg md:text-xl text-white mb-8 md:mb-12 leading-relaxed">
            Ready to get your vehicle looking amazing? Get in touch for a free, no-obligation quote.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-black px-8 py-4 text-lg gilroy-text-bold font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
              style={{ minHeight: '56px' }}
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">
                Get A Quote
              </span>
              <div className="absolute inset-0 bg-black border-2 border-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-white gilroy-text-bold font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Get A Quote
              </span>
            </Link>
            <Link
              href="tel:01603123456"
              className="border-2 border-white text-white px-8 py-4 text-lg gilroy-text-bold font-bold uppercase tracking-wider relative overflow-hidden group touch-manipulation transition-all duration-200 active:scale-95"
              style={{ minHeight: '56px' }}
            >
              <span className="relative z-10 group-hover:opacity-0 transition-opacity duration-300">
                Call Us Now
              </span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-black gilroy-text-bold font-bold uppercase tracking-wider opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                Call Us Now
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Lightbox */}
      <Lightbox
        item={selectedItem}
        isOpen={isLightboxOpen}
        onClose={closeLightbox}
        onPrevious={handlePrevious}
        onNext={handleNext}
      />

      {/* Footer */}
      <JCCStickyFooter />
    </div>
  );
}
