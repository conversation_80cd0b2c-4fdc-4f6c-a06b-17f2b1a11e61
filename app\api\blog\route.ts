import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')

    const supabase = await createServerSupabaseClient()

    let query = supabase
      .from('blog_articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        content,
        content_html,
        featured_image,
        hero_image,
        category,
        tags,
        status,
        published_at,
        seo_title,
        seo_description,
        created_at,
        updated_at
      `)
      .eq('status', 'published')
      .order('published_at', { ascending: false })

    // Apply filters
    if (category && category !== 'all') {
      query = query.contains('tags', [category])
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,excerpt.ilike.%${search}%,content.ilike.%${search}%`)
    }

    if (featured === 'true') {
      // For featured, we'll check if 'featured' is in tags array
      query = query.contains('tags', ['featured'])
    }

    if (limit) {
      query = query.limit(parseInt(limit))
    }

    const { data: articles, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch articles' }, { status: 500 })
    }

    // Transform data to match frontend interface
    const transformedArticles = articles?.map(article => ({
      id: article.id,
      title: article.title,
      slug: article.slug,
      excerpt: article.excerpt || '',
      content: article.content_html || article.content,
      image: article.featured_image || '/images/winter-mountain.png', // fallback image
      heroImage: article.hero_image,
      category: article.tags?.[0] || 'General',
      tags: article.tags || [],
      author: 'JCC Team', // Default author
      date: article.published_at ? new Date(article.published_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      readTime: `${Math.ceil((article.content?.length || 0) / 1000)} min read`,
      featured: article.tags?.includes('featured') || false,
      seoTitle: article.seo_title,
      seoDescription: article.seo_description,
      createdAt: article.created_at,
      updatedAt: article.updated_at
    })) || []

    // Set cache headers - 24 hour cache with stale-while-revalidate
    const response = NextResponse.json({
      articles: transformedArticles,
      total: transformedArticles.length
    })

    // Cache for 24 hours with 12 hour stale-while-revalidate
    response.headers.set('Cache-Control', 's-maxage=86400, stale-while-revalidate=43200')

    // Add cache tags for targeted invalidation
    const tags = ['blog-articles', 'blog-published']
    if (category) tags.push(`blog-category-${category}`)
    if (featured === 'true') tags.push('blog-featured')

    response.headers.set('Cache-Tags', tags.join(', '))

    return response

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 