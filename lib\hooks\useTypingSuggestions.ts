import { useState, useEffect, useCallback } from 'react'

interface ContactContext {
  name: string
  email: string
  message: string
  serviceType?: string
  serviceQuestions?: Record<string, string>
}

interface UseTypingSuggestionsProps {
  contactContext: ContactContext
  debounceMs?: number
}

export function useTypingSuggestions({ 
  contactContext, 
  debounceMs = 1000 
}: UseTypingSuggestionsProps) {
  const [suggestion, setSuggestion] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const getSuggestion = useCallback(async (text: string, cursorPosition: number) => {
    if (text.length < 5) {
      setSuggestion('')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/ai/typing-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactContext,
          currentText: text,
          cursorPosition,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setSuggestion(data.suggestion || '')
      } else {
        setSuggestion('')
      }
    } catch (error) {
      console.error('Error getting typing suggestion:', error)
      setSuggestion('')
    } finally {
      setIsLoading(false)
    }
  }, [contactContext])

  const debouncedGetSuggestion = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout
      return (text: string, cursorPosition: number) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          getSuggestion(text, cursorPosition)
        }, debounceMs)
      }
    })(),
    [getSuggestion, debounceMs]
  )

  const clearSuggestion = useCallback(() => {
    setSuggestion('')
  }, [])

  return {
    suggestion,
    isLoading,
    getSuggestion: debouncedGetSuggestion,
    clearSuggestion,
  }
} 