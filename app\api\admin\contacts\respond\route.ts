import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

const requestSchema = z.object({
  contactId: z.string(),
  response: z.string().min(1, 'Response is required'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { contactId, response } = requestSchema.parse(body)

    const supabase = await createServerSupabaseClient()

    // Update contact with response
    const { data: updatedContact, error: updateError } = await supabase
      .from('contact_submissions')
      .update({
        admin_response: response,
        response_sent_at: new Date().toISOString(),
        status: 'responded',
        updated_at: new Date().toISOString()
      })
      .eq('id', contactId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating contact:', updateError)
      return NextResponse.json(
        { error: 'Failed to update contact' },
        { status: 500 }
      )
    }

    // Log the email (you would integrate with your email service here)
    const { error: emailError } = await supabase
      .from('email_logs')
      .insert({
        to_email: updatedContact.email,
        subject: `Re: Your ${updatedContact.service_type || 'service'} inquiry`,
        content: response,
        type: 'contact_response',
        contact_id: contactId,
        sent_at: new Date().toISOString(),
        status: 'sent'
      })

    if (emailError) {
      console.error('Email log error:', emailError)
      // Don't fail the whole operation for logging issues
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Response sent successfully' 
    })
  } catch (error) {
    console.error('Error in contact response:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 