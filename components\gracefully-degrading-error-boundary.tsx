'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  isHydrationError: boolean
}

/**
 * Gracefully Degrading Error Boundary
 * 
 * This component helps mitigate hydration errors and SEO indexing issues
 * by gracefully handling client-side errors while preserving server-side rendering.
 * 
 * Features:
 * - Detects hydration errors specifically
 * - Preserves SEO-friendly server-rendered content
 * - Provides fallback UI for client-side errors
 * - Logs errors for debugging
 */
export class GracefullyDegradingErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: number | null = null

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      isHydrationError: false,
    }
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if this is a hydration error
    const isHydrationError = 
      error.message.includes('Hydration') ||
      error.message.includes('hydration') ||
      error.message.includes('Text content does not match') ||
      error.message.includes('server-rendered HTML') ||
      error.message.includes('client-side rendered')

    return {
      hasError: true,
      error,
      isHydrationError,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error for debugging
    console.error('Error caught by GracefullyDegradingErrorBoundary:', error)
    console.error('Error info:', errorInfo)

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo)

    // For hydration errors, attempt to retry after a short delay
    if (this.state.isHydrationError) {
      this.retryTimeoutId = window.setTimeout(() => {
        this.setState({
          hasError: false,
          error: undefined,
          isHydrationError: false,
        })
      }, 100)
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }
  }

  render() {
    if (this.state.hasError) {
      // For hydration errors, show a minimal fallback that won't interfere with SEO
      if (this.state.isHydrationError) {
        return (
          <div suppressHydrationWarning>
            {this.props.fallback || (
              <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading...</p>
                </div>
              </div>
            )}
          </div>
        )
      }

      // For other errors, show a more comprehensive error UI
      return (
        this.props.fallback || (
          <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
            <div className="max-w-md w-full text-center">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-8 h-8 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2 gilroy-text-bold">
                  Something went wrong
                </h2>
                <p className="text-gray-600 mb-6 gilroy-text">
                  We're sorry, but something unexpected happened. Please try refreshing the page.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors gilroy-text-medium"
                >
                  Refresh Page
                </button>
              </div>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}

/**
 * Hook version for functional components
 */
export function useErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null)

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  const captureError = React.useCallback((error: Error) => {
    setError(error)
  }, [])

  React.useEffect(() => {
    if (error) {
      throw error
    }
  }, [error])

  return {
    captureError,
    resetError,
  }
}

export default GracefullyDegradingErrorBoundary 