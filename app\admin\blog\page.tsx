import { createServerSupabaseClient } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  Plus,
  Edit,
  Eye,
  Trash2,
  Calendar,
  User,
  TrendingUp,
  BookOpen,
  Clock
} from 'lucide-react'
import Link from 'next/link'
import BlogPublishButton from '@/components/admin/BlogPublishButton'

export default async function BlogPage() {
  const supabase = await createServerSupabaseClient()

  // Get blog articles
  const { data: articles } = await supabase
    .from('blog_articles')
    .select('*')
    .order('created_at', { ascending: false })

  // Get blog stats
  const { count: totalArticles } = await supabase
    .from('blog_articles')
    .select('*', { count: 'exact', head: true })

  const { count: publishedArticles } = await supabase
    .from('blog_articles')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'published')

  const { count: draftArticles } = await supabase
    .from('blog_articles')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'draft')

  // Get recent articles (last 7 days)
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)

  const { count: recentArticles } = await supabase
    .from('blog_articles')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', weekAgo.toISOString())

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 gilroy-text-bold">Blog Articles</h1>
          <p className="text-gray-600 mt-2 gilroy-text">
            Create and manage blog content for your website
          </p>
        </div>
        <Link href="/admin/blog/new">
          <Button className="gilroy-text-medium">
            <Plus className="h-4 w-4 mr-2" />
            New Article
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Total Articles</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{totalArticles || 0}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Published</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{publishedArticles || 0}</p>
              </div>
              <BookOpen className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">Drafts</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{draftArticles || 0}</p>
              </div>
              <Edit className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 gilroy-text-medium">This Week</p>
                <p className="text-3xl font-bold text-gray-900 gilroy-text-bold">{recentArticles || 0}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Articles List */}
      <Card>
        <CardHeader>
          <CardTitle className="gilroy-text-bold">Blog Articles</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y divide-gray-100">
            {articles && articles.length > 0 ? (
              articles.map((article) => (
                <div key={article.id} className="p-6 hover:bg-gray-50 transition-colors duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <FileText className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium">
                            {article.title}
                          </h3>
                          <p className="text-sm text-gray-600 gilroy-text mt-1">
                            {article.excerpt || 'No excerpt available'}
                          </p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              <User className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-500 gilroy-text">
                                {article.author || 'Unknown'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-500 gilroy-text">
                                {new Date(article.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            {article.published_at && (
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span className="text-sm text-gray-500 gilroy-text">
                                  Published: {new Date(article.published_at).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                          {article.tags && article.tags.length > 0 && (
                            <div className="flex items-center space-x-2 mt-2">
                              {article.tags.slice(0, 3).map((tag: string) => (
                                <Badge key={tag} variant="outline" className="text-xs gilroy-text">
                                  {tag}
                                </Badge>
                              ))}
                              {article.tags.length > 3 && (
                                <Badge variant="outline" className="text-xs gilroy-text">
                                  +{article.tags.length - 3} more
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge className={`${getStatusColor(article.status)} gilroy-text-medium`}>
                        {article.status.toUpperCase()}
                      </Badge>
                      <div className="flex items-center space-x-2">
                        {article.status === 'published' && (
                          <Link href={`/blog/${article.slug}`} target="_blank">
                            <Button variant="ghost" size="sm" className="gilroy-text">
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                          </Link>
                        )}
                        <Link href={`/admin/blog/${article.id}/edit`}>
                          <Button variant="ghost" size="sm" className="gilroy-text">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </Link>
                        {article.status === 'draft' && (
                          <BlogPublishButton articleId={article.id} />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-12 text-center">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 gilroy-text-medium mb-2">
                  No blog articles yet
                </h3>
                <p className="text-gray-500 gilroy-text mb-6">
                  Start creating content for your blog to engage with your customers
                </p>
                <Link href="/admin/blog/new">
                  <Button className="gilroy-text-medium">
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Article
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 